'use server';

import type {
  ResourceTypeIdJson,
} from '@toolproof-npm/schema';
import type { ResourceShapeConst, FilterConst, ResourceRawMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import { listResources as _listResources } from '@toolproof-npm/shared';

// Thin server actions that wrap the canonical server-side jobs

export async function saListResources(
  resourceTypeIds: ResourceTypeIdJson[]
): Promise<ResourceDataMap> {
  return _listResources(resourceTypeIds);
}
