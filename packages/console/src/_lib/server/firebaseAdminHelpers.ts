'use server';

import type { ResourceShapeConst, ResourceRoleConst, ResourceConst, StepConst, WorkflowConst, PartialResourceMeta } from '@toolproof-npm/shared/types';
import { getNewId as getNewIdLib } from '@toolproof-npm/shared';
import path from 'path';
import { existsSync } from 'fs';


// Ensure Application Default Credentials are resolvable for libraries that rely on ADC
// (e.g., gcs-utils via google-auth-library). Prefer an explicit env var, else fallback
// to the local gcp-key.json if present.
function ensureGcpADC() {
  if (!process.env.GOOGLE_APPLICATION_CREDENTIALS || !process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
    const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
    if (existsSync(localKeyPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = localKeyPath;
    }
  }
}

// Initialize once at module load so downstream libs can discover credentials
ensureGcpADC();

export async function getNewId(identifiable: ResourceShapeConst | ResourceRoleConst | ResourceConst | 'jobs' | 'executions' | StepConst | 'STATELESS_STRATEGY' | 'STATEFUL_STRATEGY') {
  return getNewIdLib(identifiable);
}

// ATTENTION: hardcoded
const CAFS_BASE_URL = process.env.CAFS_BASE_URL || 'http://34.39.50.174/api/cafs';


/** Upload a resource via CAFS */
export async function uploadResource(meta: PartialResourceMeta, content: string) {
  try {
    const requestBody = {
      meta,
      content
    };

    const response = await fetch(`${CAFS_BASE_URL}/store`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();

  } catch (error) {
    throw new Error(`Failed to write file: ${error}`);
  }
}