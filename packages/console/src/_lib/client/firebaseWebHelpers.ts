import type { ResourceTypeIdJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import { useEffect, useMemo, useState } from 'react';
import { saListResources } from '@/_lib/server/fetchDataActions';


// Simplified resources data hook: only Types -> Resources
export function useResources(
  resourceTypeIds: ResourceTypeIdJson[],
  options?: { debug?: boolean }
): { items: ResourceMap; loading: boolean; error: Error | undefined } {
  const debug = !!options?.debug;
  const resourceTypeIdsKey = useMemo(() => [...resourceTypeIds].sort().join('|'), [resourceTypeIds]);
  const [itemsById, setItemsById] = useState<ResourceMap>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    let cancelled = false;
    const ids = resourceTypeIdsKey ? resourceTypeIdsKey.split('|').filter(Boolean) : [];
    if (debug) console.debug('[useResourcesData] fetch start', { resourceTypeIds: ids });
    const run = async () => {
      try {
        setLoading(ids.length > 0);
        setError(undefined);
        if (ids.length === 0) {
          setItemsById({});
          setLoading(false);
          return;
        }
        const result = await saListResources(ids as ResourceTypeIdJson[]);
        if (!cancelled) setItemsById(result);
      } catch (e) {
        if (!cancelled) setError(e as Error);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    run();
    return () => { cancelled = true; };
  }, [resourceTypeIdsKey, debug]);

  return { items: itemsById, loading, error };
}