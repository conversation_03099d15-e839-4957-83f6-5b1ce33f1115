import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { NextAuthOptions } from 'next-auth';
import bcrypt from 'bcryptjs';
import { dbAdmin } from '@toolproof-npm/shared';

export const authOptions: NextAuthOptions = {
  providers: [
    // Email/Password Authentication
    CredentialsProvider({
      id: 'credentials',
      name: 'Email and Password',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        name: { label: 'Name', type: 'text' },
        mode: { label: 'Mode', type: 'text' } // 'login' or 'signup'
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        const { email, password, name, mode } = credentials;

        try {
          // Check if user exists
          const usersRef = dbAdmin.collection('users');
          const userQuery = await usersRef.where('email', '==', email).get();

          if (mode === 'signup') {
            // Sign up flow
            if (!userQuery.empty) {
              throw new Error('User already exists with this email');
            }

            if (!name || name.trim().length === 0) {
              throw new Error('Name is required for signup');
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(password, 12);

            // Create new user
            const newUserRef = usersRef.doc();
            const userData = {
              id: newUserRef.id,
              email: email.toLowerCase(),
              name: name.trim(),
              password: hashedPassword,
              createdAt: new Date(),
              updatedAt: new Date(),
              provider: 'credentials'
            };

            await newUserRef.set(userData);

            return {
              id: newUserRef.id,
              email: userData.email,
              name: userData.name,
            };
          } else {
            // Login flow
            if (userQuery.empty) {
              throw new Error('No user found with this email');
            }

            const userDoc = userQuery.docs[0];
            const userData = userDoc.data();

            // Check if user signed up with credentials (has password)
            if (!userData.password) {
              throw new Error('Please sign in with the method you used to create your account');
            }

            // Verify password
            const isValidPassword = await bcrypt.compare(password, userData.password);
            if (!isValidPassword) {
              throw new Error('Invalid password');
            }

            // Update last login
            await userDoc.ref.update({
              lastLoginAt: new Date(),
              updatedAt: new Date()
            });

            return {
              id: userData.id,
              email: userData.email,
              name: userData.name
            };
          }
        } catch (error) {
          console.error('Auth error:', error);
          throw error;
        }
      }
    }),

    // Google OAuth (existing)
    GoogleProvider({
      clientId: process.env.GOOGLE_ID!,
      clientSecret: process.env.GOOGLE_SECRET!,
    }),
  ],

  callbacks: {
    async signIn({ user, account, profile }) {
      
      return true;
    },

    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
      }
      return session;
    }
  },

  pages: {
    signIn: '/auth',
    error: '/auth'
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  secret: process.env.NEXTAUTH_SECRET,
};
