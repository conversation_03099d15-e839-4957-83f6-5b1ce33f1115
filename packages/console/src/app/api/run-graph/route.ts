import { NextResponse } from 'next/server';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import type { StatefulStrategyJson, ExecutionJson, ResourceJson, StrategyStateJson } from '@toolproof-npm/schema';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';


// ATTENTION: move to a types file
export interface GraphStartEvent {
  kind: 'graph_start';
  statefulStrategy: StatefulStrategyJson;
}

export type StrategyStateDelta = StrategyStateJson;

export interface StepCompleteEvent {
  kind: 'step_complete';
  stepIndex: number; // 0-based
  execution: ExecutionJson;
  strategyStateDelta: StrategyStateDelta;
}

export interface GraphEndEvent {
  kind: 'graph_end';
}

export type WorkflowRunEvent = GraphStartEvent | StepCompleteEvent | GraphEndEvent;

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const { statefulStrategy } = (await req.json()) as { statefulStrategy: StatefulStrategyJson };
    console.log('statefulStrategy :', JSON.stringify(statefulStrategy, null, 2));
    if (!statefulStrategy) {
      return NextResponse.json({ error: 'Missing statefulStrategy' }, { status: 400 });
    }

    const apiKey = process.env.LANGCHAIN_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: 'Missing LANGCHAIN_API_KEY' }, { status: 500 });
    }

    const urlLocal = `http://localhost:2024`;
    const urlRemote = `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
    const apiUrl = process.env.LANGGRAPH_API_URL || urlLocal; // fallback to remote
    const graphId = CONSTANTS.ENGINE.GraphRunWorkflow;

    const client = new Client({ apiUrl, apiKey });
    const remoteGraph = new RemoteGraph({ graphId, url: apiUrl, apiKey });

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30 * 60 * 1000);

    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();

    (async () => {
      try {
        const thread = await client.threads.create();

        // Check if we're resuming from an interrupt
        const isResume = false; // TODO: Add logic to detect resume vs initial run

        const stream = await remoteGraph.stream(
          isResume ? null : { // Pass null for resume, initial state for new run
            messages: [new HumanMessage('Graph is invoked')],
            // Ensure intermediate state events are forwarded
            dryModeManager: { dryRunMode: false, delay: 0, drySocketMode: false },
            statefulStrategy,
          },
          {
            configurable: { thread_id: thread.thread_id },
            signal: controller.signal,
            // Request state update streaming (node-level deltas including our graphEvent annotation)
            streamMode: 'updates',
          }
        );

        // Emit an initial debug event to confirm route + stream opened
        try {
          const openDebug = {
            type: 'run_graph_debug',
            label: 'opened',
            apiUrl,
            graphId,
            threadId: thread.thread_id,
          };
          await writer.write(new TextEncoder().encode(JSON.stringify(openDebug) + '\n'));
        } catch { /* ignore debug write failures */ }

        // Emit lean graph_start using submitted statefulStrategy to prime clients
        let sentStart = false;
        let previousStrategyState: Record<string, Record<string, unknown>> = {};
        try {
          const totalSteps = statefulStrategy?.statelessStrategy?.steps?.length ?? 0;
          const startEvent = { type: 'graph_start', totalSteps, statefulStrategy };
          await writer.write(new TextEncoder().encode(JSON.stringify(startEvent) + '\n'));
          console.log('@@RUN_GRAPH_START_LOCAL');
          sentStart = true;
          // Initialize previous strategyState as empty; it will be populated from live statefulStrategy updates.
          previousStrategyState = {};
        } catch { /* ignore start write failures */ }

        let eventIdx = 0;
        for await (let event of stream) {
          try {
            eventIdx++;

            // Check if this is an interrupt event
            if (event && typeof event === 'object' && 'event' in event && event.event === 'interrupt') {
              console.log('@@RUN_GRAPH_INTERRUPT_DETECTED', event);
              // Forward interrupt event to client
              const interruptEvent = {
                type: 'interrupt',
                message: event.value || 'Graph execution interrupted',
                threadId: thread.thread_id
              };
              await writer.write(new TextEncoder().encode(JSON.stringify(interruptEvent) + '\n'));
              // Don't continue processing - wait for user response
              break;
            }

            // If multiple modes were requested and returned as tuples, unwrap [mode, chunk]
            if (Array.isArray(event) && event.length === 2 && typeof event[0] === 'string') {
              // We ignore the mode string because we explicitly requested 'updates'
              event = event[1];
            }
            const updateObj = (event && typeof event === 'object') ? (event as Record<string, unknown>) : {};
            const nodeKeys = Object.keys(updateObj);
            if (eventIdx <= 3) {
              console.log('@@RUN_GRAPH_UPDATE_SHAPE', { nodeKeys });
              const shapeDebug = { type: 'run_graph_debug', label: 'update_shape', nodeKeys };
              await writer.write(new TextEncoder().encode(JSON.stringify(shapeDebug) + '\n'));
            }
            // Each update chunk is expected to have a single key: nodeName -> nodeUpdate
            if (nodeKeys.length === 1) {
              const nodeName = nodeKeys[0];
              const nodeUpdate = updateObj[nodeName];
              const nodeUpdateObj = (nodeUpdate && typeof nodeUpdate === 'object') ? (nodeUpdate as Record<string, unknown>) : {};
              // If statefulStrategy appears here and we never sent start (edge case when earlier write failed)
              if (!sentStart && nodeUpdateObj['statefulStrategy']) {
                const sfsRaw = nodeUpdateObj['statefulStrategy'];
                const sfs = sfsRaw as StatefulStrategyJson;
                const totalStepsFallback = sfs?.statelessStrategy?.steps?.length ?? 0;
                const startEvent = { type: 'graph_start', totalSteps: totalStepsFallback, statefulStrategy: sfs };
                await writer.write(new TextEncoder().encode(JSON.stringify(startEvent) + '\n'));
                sentStart = true;
              }
              // Compute strategyStateDelta by diffing current vs previous statefulStrategy.strategyState
              const sfs = nodeUpdateObj['statefulStrategy'] as Record<string, unknown> | undefined;
              const currentStrategyState = (sfs && typeof sfs === 'object')
                ? (sfs['strategyState'] as Record<string, Record<string, unknown>> | undefined)
                : undefined;

              if (currentStrategyState && typeof currentStrategyState === 'object') {
                const strategyStateDelta: Record<string, Record<string, unknown>> = {};

                // Find new or changed entries
                for (const [execId, roleMap] of Object.entries(currentStrategyState)) {
                  const prevRoleMap = previousStrategyState[execId] || {};
                  const deltaRoleMap: Record<string, unknown> = {};

                  for (const [resourceRoleId, resource] of Object.entries(roleMap as Record<string, ResourceJson>)) {
                    // Include if new or different from previous
                    if (!prevRoleMap[resourceRoleId] || JSON.stringify(prevRoleMap[resourceRoleId]) !== JSON.stringify(resource)) {
                      // Sanitize to minimal fields for streaming
                      deltaRoleMap[resourceRoleId] = {
                        identity: resource.identity,
                        resourceTypeId: resource.resourceTypeId,
                        path: resource.path,
                        extractedData: resource.extractedData,
                      };
                    }
                  }

                  if (Object.keys(deltaRoleMap).length > 0) {
                    strategyStateDelta[execId] = deltaRoleMap;
                  }
                }

                // Emit step_complete if we have new data
                if (Object.keys(strategyStateDelta).length > 0) {
                  const stepEvent = { type: 'step_complete', strategyStateDelta };
                  console.log('@@RUN_GRAPH_STEP', { nodeName, deltaSize: Object.keys(strategyStateDelta).length });
                  await writer.write(new TextEncoder().encode(JSON.stringify(stepEvent) + '\n'));
                }

                // Update previous state for next diff
                previousStrategyState = currentStrategyState;
              }
            } else {
              // Forward raw chunk for debugging if unexpected shape
              const rawDebug = { type: 'run_graph_debug', label: 'unexpected_update', event: updateObj };
              await writer.write(new TextEncoder().encode(JSON.stringify(rawDebug) + '\n'));
            }
          } catch (inner) {
            console.log('@@RUN_GRAPH_STREAM_ERROR', { message: (inner as Error)?.message || String(inner) });
            const line = JSON.stringify({ type: 'error', message: (inner as Error)?.message || String(inner) }) + '\n';
            try { await writer.write(new TextEncoder().encode(line)); } catch { }
          }
        }
        // Emit graph_end once stream completes (only if start was sent)
        if (sentStart) {
          console.log('@@RUN_GRAPH_END');
          const endEvent = { type: 'graph_end' };
          const lineEnd = JSON.stringify(endEvent) + '\n';
          try { await writer.write(new TextEncoder().encode(lineEnd)); } catch { }
        }
      } catch (err) {
        const line = JSON.stringify({ type: 'error', message: (err as Error)?.message || String(err) }) + '\n';
        try { await writer.write(new TextEncoder().encode(line)); } catch { }
      } finally {
        clearTimeout(timeout);
        try { await writer.close(); } catch { }
        if (!controller.signal.aborted) controller.abort();
      }
    })();

    return new Response(readable, {
      status: 200,
      headers: {
        'Content-Type': 'application/x-ndjson; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
      },
    });
  } catch (e) {
    return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
  }
}
