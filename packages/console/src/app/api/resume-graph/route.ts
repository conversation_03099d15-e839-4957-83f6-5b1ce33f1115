import { NextResponse } from 'next/server';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const { threadId, userResponse } = (await req.json()) as { 
      threadId: string; 
      userResponse: string; 
    };
    
    console.log('Resuming graph with user response:', { threadId, userResponse });
    
    if (!threadId || !userResponse) {
      return NextResponse.json({ error: 'Missing threadId or userResponse' }, { status: 400 });
    }

    const apiKey = process.env.LANGCHAIN_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: 'Missing LANGCHAIN_API_KEY' }, { status: 500 });
    }

    const urlLocal = `http://localhost:2024`;
    const urlRemote = `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
    const apiUrl = process.env.LANGGRAPH_API_URL || urlLocal;
    const graphId = CONSTANTS.ENGINE.GraphRunWorkflow;

    const client = new Client({ apiUrl, apiKey });
    const remoteGraph = new RemoteGraph({ graphId, url: apiUrl, apiKey });

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30 * 60 * 1000);

    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();

    (async () => {
      try {
        // Resume the graph with user response
        const stream = await remoteGraph.stream(
          null, // Pass null to resume from interrupt
          {
            configurable: { 
              thread_id: threadId,
              // Pass the user response to resume the interrupt
              interrupt_value: userResponse
            },
            signal: controller.signal,
            streamMode: 'updates',
          }
        );

        // Emit resume debug event
        try {
          const resumeDebug = {
            type: 'resume_graph_debug',
            label: 'resumed',
            threadId,
            userResponse,
          };
          await writer.write(new TextEncoder().encode(JSON.stringify(resumeDebug) + '\n'));
        } catch { /* ignore debug write failures */ }

        let eventIdx = 0;
        for await (let event of stream) {
          try {
            eventIdx++;
            
            // Check for another interrupt
            if (event && typeof event === 'object' && 'event' in event && event.event === 'interrupt') {
              console.log('@@RESUME_GRAPH_INTERRUPT_DETECTED', event);
              const interruptEvent = { 
                type: 'interrupt', 
                message: event.value || 'Graph execution interrupted again',
                threadId 
              };
              await writer.write(new TextEncoder().encode(JSON.stringify(interruptEvent) + '\n'));
              break;
            }
            
            // Handle regular events (similar to run-graph route)
            if (Array.isArray(event) && event.length === 2 && typeof event[0] === 'string') {
              event = event[1];
            }
            
            const updateObj = (event && typeof event === 'object') ? (event as Record<string, unknown>) : {};
            const nodeKeys = Object.keys(updateObj);
            
            if (eventIdx <= 3) {
              console.log('@@RESUME_GRAPH_UPDATE_SHAPE', { nodeKeys });
              const shapeDebug = { type: 'resume_graph_debug', label: 'update_shape', nodeKeys };
              await writer.write(new TextEncoder().encode(JSON.stringify(shapeDebug) + '\n'));
            }
            
            // Forward the event to client
            const resumeEvent = { type: 'graph_update', event: updateObj };
            await writer.write(new TextEncoder().encode(JSON.stringify(resumeEvent) + '\n'));
            
          } catch (inner) {
            console.log('@@RESUME_GRAPH_STREAM_ERROR', { message: (inner as Error)?.message || String(inner) });
            const line = JSON.stringify({ type: 'error', message: (inner as Error)?.message || String(inner) }) + '\n';
            try { await writer.write(new TextEncoder().encode(line)); } catch { }
          }
        }
        
        // Emit graph_end once stream completes
        console.log('@@RESUME_GRAPH_END');
        const endEvent = { type: 'graph_end' };
        const lineEnd = JSON.stringify(endEvent) + '\n';
        try { await writer.write(new TextEncoder().encode(lineEnd)); } catch { }
        
      } catch (err) {
        const line = JSON.stringify({ type: 'error', message: (err as Error)?.message || String(err) }) + '\n';
        try { await writer.write(new TextEncoder().encode(line)); } catch { }
      } finally {
        clearTimeout(timeout);
        try { await writer.close(); } catch { }
        if (!controller.signal.aborted) controller.abort();
      }
    })();

    return new Response(readable, {
      status: 200,
      headers: {
        'Content-Type': 'application/x-ndjson; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
      },
    });
  } catch (e) {
    return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
  }
}
