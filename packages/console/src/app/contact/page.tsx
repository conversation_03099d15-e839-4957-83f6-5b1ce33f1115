'use client';

import Link from 'next/link';
import { PageLayout } from '@/components/_root/PageLayout';

export default function ContactPage() {
  return (
    <PageLayout>
      <div className="tp-container py-12">
        <div className="mx-auto max-w-4xl">
          <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019]">Contact us</h1>
          <p className="mt-3 text-sm text-[#7A0019]/80">We&apos;d love to hear from you.</p>

          <div className="mt-8 text-gray-700 space-y-4">
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam aliquet, ipsum ut facilisis vulputate,
              quam nibh sodales lectus, at efficitur nunc lorem sit amet enim. Integer id varius magna. Maecenas
              tristique feugiat arcu, at lobortis risus pulvinar non.
            </p>
            <p>
              <PERSON><PERSON><PERSON>ur iaculis, mi non gravida volutpat, neque lacus sodales purus, vitae pharetra ipsum lorem in
              libero. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Duis
              imperdiet, lacus at condimentum cursus, justo mauris mattis neque, non placerat arcu sapien sed nisl.
            </p>
            <p>
              Sed ultrices sem ac nibh congue, quis dictum libero condimentum. Donec a magna iaculis, tempor odio at,
              feugiat metus. Cras eu ex vitae nisi tempor interdum. Pellentesque a justo ut tortor sollicitudin
              eleifend.
            </p>
          </div>

          <div className="mt-8 flex gap-4">
            <Link href="mailto:<EMAIL>" className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-5 py-3 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors">
              Email us
            </Link>
            <Link href="/docs" className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-5 py-3 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors">
              Read documentation
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}