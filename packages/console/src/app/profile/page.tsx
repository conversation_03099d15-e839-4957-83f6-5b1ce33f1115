'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { PageLayout } from '@/components/_root/PageLayout'

export default function ProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7A0019]"></div>
        </div>
      </PageLayout>
    )
  }

  if (!session?.user) {
    return null
  }

  // Generate initials for avatar
  const getInitials = (name: string | null | undefined, email: string | null | undefined) => {
    if (name) {
      return name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    if (email) {
      return email[0].toUpperCase()
    }
    return 'U'
  }

  const initials = getInitials(session.user.name, session.user.email)

  return (
    <PageLayout>
      <div className="flex flex-col items-center justify-center px-6 py-12 min-h-[calc(100vh-200px)]">
        <div className="w-full max-w-2xl">
          <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019] mb-8 text-center">
            Profile
          </h1>

          <div className="bg-white border border-[#FFCC33] rounded-lg shadow-sm p-8">
            {/* Avatar Section */}
            <div className="flex flex-col items-center mb-8">
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-[#7A0019] to-[#FFCC33] flex items-center justify-center text-white text-4xl font-bold shadow-lg mb-4">
                {initials}
              </div>
              <h2 className="text-2xl font-bold text-[#7A0019]">
                {session.user.name || 'User'}
              </h2>
            </div>

            {/* Profile Details */}
            <div className="space-y-6">
              <div className="border-b border-[#FFCC33] pb-4">
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  Name
                </label>
                <p className="text-lg text-[#7A0019] font-medium">
                  {session.user.name || 'Not provided'}
                </p>
              </div>

              <div className="border-b border-[#FFCC33] pb-4">
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  Email
                </label>
                <p className="text-lg text-[#7A0019] font-medium">
                  {session.user.email || 'Not provided'}
                </p>
              </div>

              <div className="border-b border-[#FFCC33] pb-4">
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  User ID
                </label>
                <p className="text-sm text-gray-500 font-mono">
                  {session.user.id || 'Not available'}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-8 flex gap-4 justify-center">
              <button
                onClick={() => router.push('/')}
                className="px-6 py-2 border border-[#FFCC33] text-[#7A0019] rounded-md hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}

