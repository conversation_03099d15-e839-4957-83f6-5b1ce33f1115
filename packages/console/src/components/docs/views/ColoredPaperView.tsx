'use client';

import React from 'react';

// Types matching the exposed schema (simplified for UI)
export type ColoredPaperSection = {
    // Description is now always an array of paragraphs
    description?: string[];
    sections?: Record<string, ColoredPaperSection>;
};

export type ColoredPaper = {
    identity: 'white' | 'yellow' | 'red' | 'green' | 'blue';
    sections: Record<string, ColoredPaperSection>;
};

// Helper function to parse text and convert URLs to links
const parseTextWithLinks = (text: string): React.ReactNode[] => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;

    while ((match = urlRegex.exec(text)) !== null) {
        // Add text before the URL
        if (match.index > lastIndex) {
            parts.push(text.substring(lastIndex, match.index));
        }
        // Add the link
        const url = match[0];
        parts.push(
            <a
                key={match.index}
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#7A0019] underline decoration-[#FFCC33] hover:text-[#5A0013] hover:decoration-[#5A0013] transition-colors"
            >
                {url}
            </a>
        );
        lastIndex = match.index + url.length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
        parts.push(text.substring(lastIndex));
    }

    return parts.length > 0 ? parts : [text];
};

type SectionProps = {
    name: string; // section key acts as the title
    section: ColoredPaperSection;
    depth?: number;
};

const Heading: React.FC<{ depth: number; children: React.ReactNode }> = ({ depth, children }) => {
    const Tag = (['h2', 'h3', 'h4', 'h5', 'h6'] as const)[Math.min(Math.max(depth - 1, 0), 4)];
    const size = [
        'text-3xl', // h2
        'text-2xl', // h3
        'text-xl', // h4
        'text-lg', // h5
        'text-base', // h6
    ][Math.min(Math.max(depth - 1, 0), 4)];
    return (
        <Tag className={`${size} font-bold mt-12 mb-6 text-[#7A0019] first:mt-0`}>
            {children}
        </Tag>
    );
};

const Section: React.FC<SectionProps> = ({ name, section, depth = 2 }) => {
    const { description, sections } = section;
    const paragraphs = description ?? [];

    const entries: Array<[string, ColoredPaperSection]> = sections
        ? Object.entries(sections)
        : [];

    return (
        <section className="mt-8">
            {name && <Heading depth={depth}>{name.replace(/-/g, ' ')}</Heading>}
            {paragraphs.length > 0 && (
                <div className="space-y-6">
                    {paragraphs.map((p, idx) => (
                        <p key={idx} className="text-gray-700 leading-7 text-lg">
                            {parseTextWithLinks(p)}
                        </p>
                    ))}
                </div>
            )}
            {entries.length > 0 && (
                <div className="ml-0 md:ml-8 mt-8 pl-0 md:pl-6 border-l-0 md:border-l md:border-gray-200">
                    {entries.map(([childName, childSection]) => (
                        <Section key={childName} name={childName} section={childSection} depth={Math.min(depth + 1, 6)} />
                    ))}
                </div>
            )}
        </section>
    );
};

export const ColoredPaperView: React.FC<{ data: ColoredPaper; title?: string }> = ({ data, title }) => {
    const topEntries = Object.entries(data.sections ?? {});

    return (
        <article className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            {title && (
                <div className="bg-gradient-to-r from-[#7A0019] to-[#5A0013] px-8 py-10">
                    <h1 className="text-4xl font-bold text-white">
                        {title}
                    </h1>
                </div>
            )}
            <div className="px-8 py-10">
                {topEntries.map(([name, section]) => (
                    <Section key={name} name={name} section={section} />
                ))}
            </div>
        </article>
    );
};

export default ColoredPaperView;
