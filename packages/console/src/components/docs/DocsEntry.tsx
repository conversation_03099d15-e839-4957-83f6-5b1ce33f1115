'use client';

import { useState } from 'react';
import Whitepaper from './coloredPapers/Whitepaper.json';
import Yellowpaper from './coloredPapers/Yellowpaper.json';
import ColoredPaperView, { type ColoredPaper } from './views/ColoredPaperView';

type TabKey = 'whitepaper' | 'yellowpaper';

const tabs: { key: Tab<PERSON>ey; label: string }[] = [
    { key: 'whitepaper', label: 'Whitepaper' },
    // { key: 'yellowpaper', label: 'Yellowpaper' },
];

export default function DocsEntry() {
    const [active, setActive] = useState<TabKey>('whitepaper');

    return (
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-12 h-full overflow-y-auto">
            {/* Tabs */}
            <div className="mb-10">
                <nav className="flex gap-4 justify-center border-b border-gray-200" aria-label="Tabs">
                    {tabs.map((t) => {
                        const isActive = active === t.key;
                        return (
                            <button
                                key={t.key}
                                type="button"
                                onClick={() => setActive(t.key)}
                                className={
                                    `px-6 py-3 text-base font-medium transition-all duration-200 relative ` +
                                    (isActive
                                        ? 'text-[#7A0019]'
                                        : 'text-gray-500 hover:text-[#7A0019]')
                                }
                                aria-current={isActive ? 'page' : undefined}
                            >
                                {t.label}
                                {isActive && (
                                    <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#FFCC33]"></span>
                                )}
                            </button>
                        );
                    })}
                </nav>
            </div>

            {/* Content */}
            <div className="transition-opacity duration-300">
                {active === 'whitepaper' && (
                    <ColoredPaperView data={Whitepaper as ColoredPaper} title="Whitepaper" />
                )}
                {/* {active === 'yellowpaper' && (
                    <ColoredPaperView data={Yellowpaper as ColoredPaper} title="Yellowpaper" />
                )} */}
            </div>
        </div>
    );
}
