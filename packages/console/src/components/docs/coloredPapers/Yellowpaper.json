{"identity": "Yellowpaper v0.1", "sections": {"core": {"sections": {"shared": {"description": []}, "schema": {"description": [], "sections": {"isMeta": true, "meta": {"description": [], "sections": {"resource": {"description": ["A Resource is the basic unit of data in the ToolProof ecosystem. Fundamentally, all that ToolProof does is processing and producing Resources. While a Resource itself is just a file of a given file format, ToolProof layers a JSON-based type system on top to give Resources structured meaning inside the platform."]}, "clarifications": {"description": ["JSON and JSON Schema, which form the basis of ToolProof's type system, might seem straightforward at first glance, but there are some nuances and subtleties that are important to understand. In this section, we clarify some of these points to ensure a clear understanding of how ToolProof utilizes JSON and JSON Schema."], "sections": {"1": {"description": ["In the following, we'll be using Format as an example, but the same applies to any schema-instance pair.", "Whenever we say Format (in singular and not preceded by an article), we are referring to the schema Format, as defined in core/packages/_schemas/src/schemas/resourceShapes/format.", "Whenever we say 'a Format', 'the Format', 'this Format', or 'Formats' (in plural), we are referring to an instance of the schema Format, i.e. a specific Format."]}}}, "overlaying": {}}}, "resourceShapes": {"description": [], "sections": {"Format": {"description": ["@0Format is a schema whose instances describe file formats. It generalizes MIME types: every MIME type is a @1Format, but some @1Formats are more specific or expressive than standard MIME types.", "A @1Format represents a specific data format. Any file in that format (i.e., any Resource of that format) is considered to conform to that @1Format.", "@0Format sets the outer boundary of ToolProof’s JSON-based type system: it describes how files are resourceshaped before any extraction into JSON.", "There is one special @1Format, 'application/json'. Files of this format are already JSON; they are considered native to the ToolProof ecosystem."], "sections": {"id": {"description": ["The 'id' property of a @1Format is a unique identifier for the @1Format", "It's of the form REF-{8 random alphanumeric characters}."]}, "name": {"description": []}}}, "Type": {"description": ["@0Type is a schema whose instances define the resourceshape of a JSON-expressible subset of the underlying @1Format", "A @1Type specifies an 'extractionSchema' (a JSON Schema) and an 'extractor function'. A @1Type is defined when there exists a deterministic extractor function that maps any Resource of the associated @1Format to a JSON instance that validates against its 'extractionSchema'.", "For practical use, the extractor must be implemented in code and made available to the system.", "If the associated @1Format is 'application/json', the extractor is the identity function (it returns the file’s JSON content unchanged) and need not be implemented explicitly."], "sections": {"id": {"description": ["The 'id' property of a @1Type is a unique identifier for the @1Type", "It's of the form RET-{8 random alphanumeric characters}"]}, "name": {"description": []}, "resourceFormatId": {"description": ["The 'resourceFormatId' of a @1Type is a reference to the underlying @1Format.", "It's of the form REF-{8 random alphanumeric characters} (see @0Format.id)"]}, "extractionSchema": {"description": ["The 'extractionSchema' property of @1Type defines the structure of the JSON data extracted from Resources of the associated @1Format. Except for a few restrictions (see below), it can be any valid JSON Schema.", "When a Resource is processed by its extractor function, the resulting JSON instance must validate against this 'extractionSchema'. This ensures that the extracted data adheres to a predefined format, making it easier to work with and integrate into Workflows within the ToolProof ecosystem."], "sections": {"restrictions": {"description": [""], "sections": {}}, "semantics": {"description": [""], "sections": {"identity": {"description": ["The 'identity' property of extractionSchema is an optional property which, if present, implements unique identity at the semantic level for instances of the @1Type. While all Resources already have syntactic identity via their hash, semantic identity allows us to identify Resources that are semantically identical even if they differ syntactically.", "An example is a @1Type representing a molecular structure. Two Resources might represent the same molecule but have different syntactic representations (e.g., different atom ordering, different file formats like SDF vs. MOL). By defining a 'identity' property, we can assign a unique identifier to the molecule itself, allowing us to recognize that both Resources represent the same underlying entity.", "Note that the value assigned to identity must be present or derivable from the Resource content and produced deterministically by the extractor function."]}, "merit": {"description": ["The 'merit' property of extractionSchema is an optional property which, if present, assigns a non-negative numerical value to instances of the Type, indicating a 'merit' or 'score' at the semantic level.", "An example is a Type representing candidate solutions in an optimization problem. Each candidate solution can be evaluated based on certain criteria, and the 'merit' property can be used to assign a score to each solution. This score can then be used to rank or prioritize candidates during processing within the ToolProof ecosystem.", "Note that the value assigned to merit must be present or derivable from the Resource content and produced deterministically by the extractor function."]}}}}}}}, "Role": {"description": ["Role is a schema whose instances define the roles that Resources play in Jobs.", "A Role associates a Type with a 'direction' (either 'input' or 'output'). A Role is defined when there exists a Job that consumes Resources of the associated Type as inputs or produces them as outputs.", "Roles are used in Job definitions to specify what types of Resources a Job expects as inputs and what types it will produce as outputs."], "sections": {"id": {"description": ["The 'id' property of a Role is a unique identifier for the Role", "It's of the form RER-{8 random alphanumeric characters}"]}, "name": {"description": []}, "resourceTypeId": {"description": ["The 'resourceTypeId' of a Role is a reference to the underlying Type.", "It's of the form RET-{8 random alphanumeric characters} (see Type.id)"]}, "direction": {"description": ["The 'direction' property of a Role indicates whether the Role is used as an input or output in a Job.", "It can take one of two values: 'input' or 'output'.", "'input' means that the Role represents data that is consumed by a Job, while 'output' means that it represents data that is produced by a Job."]}, "isPredicate": {"description": ["The 'isPredicate' property of a Role is a boolean flag that indicates whether the Role is meant for use in PredicateJobs.", "When 'isPredicate' is set to true, only Jobs that have 'isPredicate' set to true can use this Role."]}}}, "Job": {"description": []}}}, "non-resourceShapes": {"description": []}}}, "validation": {"description": []}, "engine": {"sections": {"graphs": {"description": []}, "nodes": {"description": []}, "internal-predicates": {"description": []}, "agents": {"description": []}}}, "console": {"sections": {"builders": {"description": [], "sections": {"resourceshape-builders": {"description": ["The standard (non-XR) user interface for building ResourceShapes. It allows users to create and manage ResourceShapes. The four ResourceShapes of ToolProof are Formats, Types, Roles, and Jobs, the schemas of which are defined in core/packages/_schemas.", "Since the schemas for the ResourceShapes might change, the builder UIs should, as far as it's feasible, be generated from the corresponding schemas. If a schema property is a boolean, such as 'isPredicate' for <PERSON>, it should be rendered as a checkbox. For some properties, however, it's not that clear how to render the form element. For example, for resourceTypeId in Role, it's not obvious that it should be a dropdown and not just a text input. For this reason, we'll rely on custom keywords in the schemas that indicate how to render the form element. In each builder, we can pass a uiHints object to the validation function validating the user input against the schema. This object will be mutated by the validation function so that when the function returns, it contains a map from schema property names to UI hints. For example, for resourceTypeId in Role, the uiHints object could contain { resourceTypeId: { widget: 'dropdown' } } (NB: the exact protocol is not yet defined). The builders can then use these hints to render the form elements appropriately.", "An even more ambitious goal would be to generate the entire builders UI (at least for ResourceShapes) from the schemas. The we might need only one builder component that takes as input the schema of the ResourceShape to be built and generates the entire UI from it. For that to work, the validation function must populate the passed object with more than uiHints. Since all ResourceShapes except Format depends on another ResourceShape, it would also need to provide information about what data needs to be fetched."]}, "workflow-builder": {"description": []}}}, "explorer": {"description": []}, "docs": {"description": []}, "chat": {"description": []}, "_root": {"description": []}}}}}, "community": {"sections": {"_sdk": {"sections": {"metaverse": {"description": []}, "websocket": {"description": []}, "helpers_py": {"description": []}}}, "jobs": {"sections": {"statistical": {"sections": {"asi": {"description": []}}}, "numerical": {"sections": {"add": {"description": []}, "multiply": {"description": []}}}, "biological": {"sections": {"generate-candidate": {"description": []}, "dock-candidate": {"description": []}}}, "logical": {"description": []}}}}}, "configuration": {"sections": {"GoogleCloudPlatform": {"description": [], "sections": {"Firestore": {"description": []}, "CloudStorage": {"description": ["In CloudStorage, we store ResourceShapes (in tp-shapes) and Resources (in tp-resources)."], "sections": {"ResourceShapes": {"description": ["ResourceShapes are the building blocks of ToolProof..."]}, "Resources": {"description": ["Resources are the instances of Types. The content of a Resource is stored in a Content Addressable File Storage (CAFS) format in CloudStorage, while a corresponding metadata document is stored in Firestore. The metadata document has a path field that points to the location of the Resource in CloudStorage.", "Whenever a Resource is created, either manually by an Agent or programatically by a Job, its content is hashed with the SHA-256 hashing algorithm to generate a unique hash. If another Resource is created that is content-wise identical to an already existing Resource, it will generate the same hash. Instead of storing duplicate copies of identical Resources, the system only stores one copy and references it using its hash. This approach optimizes storage and ensures data integrity by preventing redundancy.", "Each Resource is identified uniquely by its hash, however, the hash is not part of the content of the Resource as that would create a circular dependency (it would change the Resource content so that we would need to generate a new hash).", "The corresponding metadata document is identified uniquely by a ResourceId. This is because there might be several metadata documents pointing to the same Resource. If a duplicate Resource is created, a new metadata document will be created with a new ResourceId, but it will point to a pre-existing Resource.", "A ResourceId complies with the schema ResourceId in core/packages/_schemas. A ResourceId is of the form {ResourceRoleId}__{JobStepId}, which means that it encodes the Role (and transitively the Type) of the Resource, as well as the JobStep (and transitively the Job) that created the Resource (even if it is a duplicate). This information is useful for tracing the provenance of a Resource. For manually created Resources, a dummy is used for the JobStepId.", "A metadata document complies with the schema ResourceMetaPassive in core/packages/_schemas, which implies that it contains only a path and a timestamp of creation, in addition to the ResourceId that is part of the document path.", "There's also a ResourceMetaActive schema that defines a Resource's entry in ResourceMaps during Workflow execution. ResourceMetaActive is similar to ResourceMetaPassive but has two nuances, both of which are applicable only during Workflow execution.", "First, ResourceMetaActive can have a pointer field instead of path, which lets it point to another Resource in ResourceSpace (the space of all Resources created by earlier Workflows and Resources that are to be created by the current Workflow). Since this can be a Resource that is not yet created (but will be created by the current Workflow), we cannot use path. However, when persisted (to ResourceMetaPassive), pointers are always resolved to paths.", "Second, ResourceMetaActive includes extractedData (i.e. a subset of the Resource exposed as json). Since extractedData can always be generated deterministically from the Resource content using the underlying Type's extractor function, it is not stored in Firestore."]}}}, "KubernetesEngine": {"description": []}}}, "LangGraph": {"description": []}, "GitHub": {"description": []}, "Vercel": {"description": []}, "TypeScript": {"description": []}}}, "coordination": {"description": []}, "control": {"description": []}}}