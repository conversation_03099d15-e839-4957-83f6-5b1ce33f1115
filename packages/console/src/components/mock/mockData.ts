/**
 * Mock data for Goals and Strategies demonstration
 * Based on StatelessStrategy schema from Genesis.json
 */

export interface MockGoal {
  id: string;
  name: string;
  description: string;
  strategies: MockStrategy[];
}

export interface MockStrategy {
  identity: string;
  name: string;
  description: string;
  steps: MockWorkStep[];
}

export interface MockWorkStep {
  kind: 'work';
  identity: string;
  jobName: string;
  jobId: string;
  description: string;
}

export const mockGoals: MockGoal[] = [
  {
    id: 'goal-cure-cancer',
    name: 'Cure Cancer',
    description: 'Develop novel therapeutic approaches to treat various forms of cancer',
    strategies: [
      {
        identity: 'STRATEGY-drug-discovery-001',
        name: 'Targeted Drug Discovery',
        description: 'Computational approach to identify and validate drug candidates',
        steps: [
          {
            kind: 'work',
            identity: 'WORKSTEP-001',
            jobName: 'Molecular Docking',
            jobId: 'JOB-autodock-vina',
            description: 'Predict binding affinity between drug candidates and target proteins'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-002',
            jobName: 'Molecular Dynamics Simulation',
            jobId: 'JOB-gromacs-md',
            description: 'Simulate molecular interactions over time to validate stability'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-003',
            jobName: 'ADMET Analysis',
            jobId: 'JOB-admet-predictor',
            description: 'Predict absorption, distribution, metabolism, excretion, and toxicity'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-004',
            jobName: 'Binding Affinity Scoring',
            jobId: 'JOB-binding-scorer',
            description: 'Calculate and rank drug-target binding scores'
          }
        ]
      },
      {
        identity: 'STRATEGY-protein-analysis-001',
        name: 'Protein Target Analysis',
        description: 'Identify and characterize cancer-specific protein targets',
        steps: [
          {
            kind: 'work',
            identity: 'WORKSTEP-101',
            jobName: 'Protein Structure Prediction',
            jobId: 'JOB-alphafold',
            description: 'Predict 3D structure of target proteins using AlphaFold'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-102',
            jobName: 'Binding Site Detection',
            jobId: 'JOB-fpocket',
            description: 'Identify potential drug binding pockets on protein surface'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-103',
            jobName: 'Sequence Alignment',
            jobId: 'JOB-blast-align',
            description: 'Compare protein sequences across species for conservation analysis'
          }
        ]
      }
    ]
  },
  {
    id: 'goal-climate-change',
    name: 'Reduce Global Warming',
    description: 'Develop strategies to mitigate climate change and reduce carbon emissions',
    strategies: [
      {
        identity: 'STRATEGY-carbon-capture-001',
        name: 'Carbon Capture Optimization',
        description: 'Optimize materials and processes for CO2 capture and sequestration',
        steps: [
          {
            kind: 'work',
            identity: 'WORKSTEP-201',
            jobName: 'Material Property Simulation',
            jobId: 'JOB-quantum-espresso',
            description: 'Simulate quantum mechanical properties of capture materials'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-202',
            jobName: 'Absorption Capacity Analysis',
            jobId: 'JOB-sorption-analyzer',
            description: 'Calculate CO2 absorption capacity of candidate materials'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-203',
            jobName: 'Process Flow Optimization',
            jobId: 'JOB-aspen-plus',
            description: 'Optimize industrial-scale carbon capture process parameters'
          }
        ]
      },
      {
        identity: 'STRATEGY-renewable-energy-001',
        name: 'Solar Cell Efficiency Enhancement',
        description: 'Design novel photovoltaic materials with improved efficiency',
        steps: [
          {
            kind: 'work',
            identity: 'WORKSTEP-301',
            jobName: 'Band Gap Calculation',
            jobId: 'JOB-vasp-dft',
            description: 'Calculate electronic band structure using density functional theory'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-302',
            jobName: 'Light Absorption Modeling',
            jobId: 'JOB-optical-sim',
            description: 'Model optical absorption properties across solar spectrum'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-303',
            jobName: 'Charge Transport Analysis',
            jobId: 'JOB-mobility-calc',
            description: 'Analyze electron and hole mobility in candidate materials'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-304',
            jobName: 'Stability Assessment',
            jobId: 'JOB-thermal-stability',
            description: 'Assess material stability under operational conditions'
          }
        ]
      }
    ]
  },
  {
    id: 'goal-alzheimers',
    name: 'Treat Alzheimer\'s Disease',
    description: 'Discover treatments to prevent or reverse cognitive decline',
    strategies: [
      {
        identity: 'STRATEGY-amyloid-targeting-001',
        name: 'Amyloid-Beta Targeting',
        description: 'Design molecules that prevent amyloid plaque formation',
        steps: [
          {
            kind: 'work',
            identity: 'WORKSTEP-401',
            jobName: 'Peptide Aggregation Simulation',
            jobId: 'JOB-md-aggregation',
            description: 'Simulate amyloid-beta peptide aggregation pathways'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-402',
            jobName: 'Inhibitor Screening',
            jobId: 'JOB-virtual-screening',
            description: 'Screen compound libraries for aggregation inhibitors'
          },
          {
            kind: 'work',
            identity: 'WORKSTEP-403',
            jobName: 'Blood-Brain Barrier Prediction',
            jobId: 'JOB-bbb-predictor',
            description: 'Predict ability of compounds to cross blood-brain barrier'
          }
        ]
      }
    ]
  }
];
