import * as THREE from 'three';

export interface StrategyAnimationConfig {
    /** Duration of pull-in phase in milliseconds */
    pullInDuration: number;
    /** Duration of pull-out phase in milliseconds */
    pullOutDuration: number;
    /** Pause between jobs in milliseconds */
    pauseBetweenJobs: number;
    /** Pause while job is inside the engine in milliseconds */
    pauseInside: number;
    /** How far into the engine the job should go (0 = surface, 1 = center) */
    pullInDepth: number;
    /** Easing function for pull-in */
    pullInEasing: (t: number) => number;
    /** Easing function for pull-out */
    pullOutEasing: (t: number) => number;
    /** Duration of wheel rotation animation in milliseconds (default: 800ms) */
    wheelRotationDuration?: number;
    /** Easing function for wheel rotation (default: easeInOutCubic) */
    wheelRotationEasing?: (t: number) => number;
    /** Color to highlight active job (optional) */
    highlightJobColor?: THREE.ColorRepresentation;
    /** Color to highlight engine while processing (optional) */
    highlightEngineColor?: THREE.ColorRepresentation;
    /** Callback when a job starts being animated - use to draw roles */
    onJobStartAnimation?: (jobId: string) => void;
    /** Callback when a job finishes being animated - use to clear roles */
    onJobEndAnimation?: (jobId: string) => void;
    /** Callback to show/hide role meshes and connectors */
    onToggleRoleVisibility?: (jobId: string, visible: boolean) => void;
    /** Callback when animation stops - use to re-enable interactions */
    onAnimationStop?: () => void;
    /** Callback to draw output role-resource connectors when job completes (PULLING_OUT) */
    onJobOutputConnectors?: (jobId: string) => void;
    /** Callback to show HUD when job animation starts (PULLING_IN) */
    onJobHUDShow?: (jobId: string) => void;
    /** Callback to hide HUD when job animation ends (after PULLING_OUT) */
    onJobHUDHide?: (jobId: string) => void;
    /** Optional resolver to fetch a resource mesh by resourceId */
    getResourceMeshById?: (resourceId: string) => THREE.Mesh | undefined;
    /** Optional resolver to fetch execution info for logging */
    getExecutionInfo?: (jobId: string) => { executionId?: string; stepIndex?: number } | undefined;
    /** Callback to get the wheel group (parent group containing jobs/engine) for rotation */
    getWheelGroup?: () => THREE.Group | null;
    /** Callback to get job mesh by ID for wheel rotation calculations */
    getJobMeshById?: (jobId: string) => THREE.Mesh | undefined;
    /** Callback to get TYPE_Natural mesh for wheel rotation alignment */
    getTypeNaturalMesh?: () => THREE.Mesh | undefined;
    /** Callback to get engine mesh for wheel rotation calculations */
    getEngineMesh?: () => THREE.Mesh | undefined;
    /** Enable debug logging for wheel rotation */
    debugWheel?: boolean;
}

export interface StrategyAnimationTarget {
    mesh: THREE.Mesh;
    originalPosition: THREE.Vector3;
    originalWorldPosition: THREE.Vector3; // Initial space position before any rotations
    originalColor: THREE.Color;
    engineJobConnector?: THREE.Line;
    jobRoleConnectors?: THREE.Line[]; // Lines connecting job to its input/output roles
    roleResourceInputConnectors?: THREE.Line[]; // Lines connecting input roles to bound resources
    roleResourceOutputConnectors?: THREE.Line[]; // Lines connecting output roles to bound resources (drawn on completion)
    highlightedInputResources?: Map<THREE.Mesh, { color: THREE.Color; intensity: number; outline?: THREE.LineSegments; outlineColor?: THREE.Color }>; // Track original emissive/intensity and outline
    highlightedOutputResources?: Map<THREE.Mesh, { color: THREE.Color; intensity: number; outline?: THREE.LineSegments; outlineColor?: THREE.Color }>; // Track original emissive/intensity and outline
}

enum AnimationPhase {
    PAUSING = 'pausing',
    PULLING_IN = 'pulling_in',
    INSIDE = 'inside',
    PULLING_OUT = 'pulling_out',
}

/**
 * Manages the animation of jobs being processed by the Engine.
 * Jobs are pulled in one at a time, then returned to their original positions.
 */
export class StrategyAnimation {
    private jobs: StrategyAnimationTarget[] = [];
    private currentJobIndex: number = -1; // -1 indicates no job is currently active
    private phase: AnimationPhase = AnimationPhase.PAUSING;
    private phaseStartTime: number = 0;
    // Elapsed ms stored when pausing to allow precise resume
    private pausedElapsedInPhase: number = 0;
    // Current mesh position state during animation (frozen on pause, restored on resume)
    private pausedJobPosition: THREE.Vector3 | null = null;
    private isRunning: boolean = false;
    // Prevent event handlers from advancing state during resume restoration
    private isResuming: boolean = false;
    // One-shot logging flag for first update after resume
    private logNextUpdate: boolean = false;
    // Timeline-driven mode bypasses internal phase timing and uses external ticks
    private timelineMode: boolean = false;
    private engineMesh: THREE.Mesh | null = null;
    private engineOriginalColor: THREE.Color | null = null;
    // Gate INSIDE phase until stepComplete is signaled for the active job
    private awaitingStepComplete: boolean = false;
    private stopRequested: boolean = false;
    // Optional explicit sequencing by execution/job order
    private jobOrderIds: string[] | null = null; // legacy: unique order only
    private jobSequenceIds: string[] | null = null; // new: full step sequence including repeats
    private sequencePointer: number = 0;
    // Buffer of completed jobs that may arrive before the animation reaches INSIDE
    private completedJobIds: Set<string> = new Set<string>();
    private unknownTimelineJobIdsLogged = new Set<string>();

    private matchesJobId(mesh: THREE.Mesh, jobId: string): boolean {
        const ud = mesh.userData as unknown as { id?: unknown };
        const primary = typeof ud?.id === 'string' ? ud.id : String(ud?.id ?? '');
        return primary === jobId;
    }
    // Gate to avoid repeated rotation requests within the same PAUSING segment
    private rotationRequestedForCurrentPause: boolean = false;
    // Wheel rotation animation state
    private wheelRotationTarget = 0;
    private wheelRotationCurrent = 0;
    private wheelRotationStartTime = 0;
    private wheelRotationDuration = 800; // ms
    private isRotatingWheel = false;
    private wheelRotationStartY = 0;
    private wheelRotationTargetY = 0;
    private wheelRotationTargetJobId: string | null = null;

    constructor(
        private enginePosition: THREE.Vector3,
        private config: StrategyAnimationConfig
    ) {
        // Apply wheel rotation duration from config if provided
        if (config.wheelRotationDuration !== undefined) {
            this.wheelRotationDuration = config.wheelRotationDuration;
        }
    }

    /**
     * Add a job to the animation sequence
     */
    public addJob(mesh: THREE.Mesh, engineJobConnector?: THREE.Line): void {
        // Store original emissive color
        const material = mesh.material as THREE.MeshStandardMaterial;
        const originalColor = material.emissive.clone();

        // Capture initial space position before any rotations
        const originalWorldPosition = new THREE.Vector3();
        mesh.getWorldPosition(originalWorldPosition);

        this.jobs.push({
            mesh,
            originalPosition: mesh.position.clone(),
            originalWorldPosition,
            originalColor,
            engineJobConnector,
            jobRoleConnectors: [], // Will be populated by setJobRoleConnectors
            roleResourceInputConnectors: [], // Will be populated by setRoleResourceInputConnectors
            roleResourceOutputConnectors: [], // Will be populated by setRoleResourceOutputConnectors
            highlightedInputResources: new Map(),
            highlightedOutputResources: new Map(),
        });
        // console.log('[StrategyAnimationAnimation.addJob] Added job, total jobs:', this.jobs.length, 'jobId:', mesh.userData?.id);
    }

    /**
     * Force advance the current job out of INSIDE if stalled.
     */
    public forceAdvanceCurrentJob(): void {
        if (!this.isRunning) return;
        const currentJob = this.jobs[this.currentJobIndex];
        if (!currentJob) return;
        if (this.phase === AnimationPhase.INSIDE) {
            this.awaitingStepComplete = false;
            this.phase = AnimationPhase.PULLING_OUT;
            this.phaseStartTime = performance.now();
            this.resetEngineHighlight();
            this.highlightJob(currentJob);
            // Draw output connectors when job completes
            const jobId = currentJob.mesh.userData?.id;
            if (jobId && this.config.onJobOutputConnectors) {
                this.config.onJobOutputConnectors(jobId);
            }
        }
    }
    /**
     * Set the engine mesh for highlighting
     */
    setEngineMesh(mesh: THREE.Mesh): void {
        this.engineMesh = mesh;
        const material = mesh.material as THREE.MeshStandardMaterial;
        this.engineOriginalColor = material.emissive.clone();
    }

    /**
     * Update the originalPosition for a job mesh (used after wheel rotation)
     */
    public updateJobOriginalPosition(mesh: THREE.Mesh, newPosition: THREE.Vector3): void {
        const job = this.jobs.find(j => j.mesh === mesh);
        if (job) {
            job.originalPosition.copy(newPosition);
        }
    }

    /**
     * Get the originalPosition for a job mesh (used for rotation calculations)
     */
    public getJobOriginalPosition(mesh: THREE.Mesh): THREE.Vector3 | null {
        const job = this.jobs.find(j => j.mesh === mesh);
        return job ? job.originalPosition.clone() : null;
    }

    /**
     * Get the initial space position for a job mesh (used for connector anchoring)
     */
    public getJobOriginalWorldPosition(mesh: THREE.Mesh): THREE.Vector3 | null {
        const job = this.jobs.find(j => j.mesh === mesh);
        return job ? job.originalWorldPosition.clone() : null;
    }

    /**
     * Set job-role connector lines for a specific job
     * These lines connect the job to its input/output roles
     */
    public setJobRoleConnectors(jobMesh: THREE.Mesh, connectors: THREE.Line[]): void {
        const jobId = jobMesh.userData?.id;
        // console.log('StrategyAnimationAnimation.setJobRoleConnectors: called for jobId', jobId, 'with', connectors.length, 'connectors');
        const job = this.jobs.find(j => j.mesh === jobMesh);
        if (job) {
            // console.log('StrategyAnimationAnimation.setJobRoleConnectors: found job, setting connectors');
            job.jobRoleConnectors = connectors;
        } else {
            // console.log('StrategyAnimationAnimation.setJobRoleConnectors: job NOT found for mesh');
        }
    }

    /**
     * Set role-resource input connector lines for a specific job
     * These lines connect the job's input roles to bound natural resources
     */
    public setRoleResourceInputConnectors(jobMesh: THREE.Mesh, connectors: THREE.Line[]): void {
        const jobId = jobMesh.userData.id as string;
        const job = this.jobs.find(j => j.mesh === jobMesh);
        if (job) {
            job.roleResourceInputConnectors = connectors;
        }
    }

    /**
     * Set role-resource output connector lines for a specific job
     * These lines connect the job's output roles to bound natural resources
     */
    public setRoleResourceOutputConnectors(jobMesh: THREE.Mesh, connectors: THREE.Line[]): void {
        const jobId = jobMesh.userData.id as string;
        const job = this.jobs.find(j => j.mesh === jobMesh);
        if (job) {
            job.roleResourceOutputConnectors = connectors;
            // Immediately show output connectors (they're created during PULLING_OUT phase)
            connectors.forEach(connector => connector.visible = true);
            // Highlight output resources when connectors become visible
            this.highlightOutputResources(job);
        }
    }

    /**
     * Clear all jobs from the animation
     */
    public clearJobs(): void {
        // Reset any active highlights
        this.resetHighlights();

        // Clear animation-controlled flags from all jobs
        for (const job of this.jobs) {
            if (job.mesh.userData.__animationControlled) {
                delete job.mesh.userData.__animationControlled;
            }
        }

        this.jobs = [];
        this.currentJobIndex = 0;
        this.phase = AnimationPhase.PAUSING;
        this.awaitingStepComplete = false;
        this.stopRequested = false;
        this.jobOrderIds = null;
        this.jobSequenceIds = null;
        this.sequencePointer = 0;
        this.completedJobIds.clear();
    }

    /**
     * Start the animation loop
     */
    public start(): void {
        /* console.log('[StrategyAnimationAnimation.start] Called, current state:', {
            isRunning: this.isRunning,
            jobCount: this.jobs.length,
            hasEngineMesh: !!this.engineMesh
        }); */
        if (this.isRunning) {
            // console.log('[StrategyAnimationAnimation.start] Already running, ignoring');
            return;
        }
        if (this.jobs.length === 0) {
            // console.log('[StrategyAnimationAnimation.start] No jobs to animate, ignoring');
            return;
        }

        // console.log('[StrategyAnimationAnimation.start] Starting animation with', this.jobs.length, 'jobs');
        this.isRunning = true;
        this.phaseStartTime = performance.now();
        this.awaitingStepComplete = false;
        this.stopRequested = false;
        this.completedJobIds.clear();
        this.rotationRequestedForCurrentPause = false;
    }

    /**
     * Stop the animation and reset all jobs to original positions
     */
    public stop(): void {
        this.isRunning = false;

        // Reset current job index to indicate no job is active
        this.currentJobIndex = -1;

        // Reset highlights
        this.resetHighlights();

        // Reset all jobs to original positions and clear animation-controlled flags
        for (const job of this.jobs) {
            job.mesh.position.copy(job.originalPosition);
            if (job.engineJobConnector) {
                this.updateEngineJobConnector(job.engineJobConnector, this.enginePosition, job.originalPosition);
            }
            // Clear animation-controlled flag to allow hover interaction when stopped
            if (job.mesh.userData.__animationControlled) {
                delete job.mesh.userData.__animationControlled;
            }
        }

        // Re-enable all entity interactions via callback
        if (this.config.onAnimationStop) {
            this.config.onAnimationStop();
        }
        this.rotationRequestedForCurrentPause = false;
    }

    

    /**
     * Pause the animation
     */
    public pause(): void {
        if (!this.isRunning) return;
        // Store elapsed time in current phase so we can resume exactly
        const now = performance.now();
        this.pausedElapsedInPhase = now - this.phaseStartTime;
        // Snapshot current job position to restore on resume
        const currentJob = this.resolveCurrentJob();
        if (currentJob) {
            this.pausedJobPosition = currentJob.mesh.position.clone();
            try {
                const jid = currentJob.mesh.userData?.id;
                console.log('[WFV.pause]', {
                    phase: this.phase,
                    pausedElapsedInPhase: this.pausedElapsedInPhase,
                    jobId: jid,
                    pos: { x: this.pausedJobPosition.x, y: this.pausedJobPosition.y, z: this.pausedJobPosition.z },
                    phaseDuration: this.getCurrentPhaseDuration(),
                });
            } catch { /* logging best-effort */ }
        }
        this.isRunning = false;
    }

    /**
     * Resume the animation
     */
    public resume(): void {
        if (this.isRunning) return;
        if (this.jobs.length === 0) return;
        // Set resuming flag to block event handlers from advancing state
        this.isResuming = true;
        this.isRunning = true;
        this.logNextUpdate = true;
        // Restore job position from pause snapshot (prevents jump)
        const currentJob = this.resolveCurrentJob();
        try {
            const jid = currentJob?.mesh.userData?.id;
            console.log('[WFV.resume:begin]', {
                phase: this.phase,
                pausedElapsedInPhase: this.pausedElapsedInPhase,
                jobId: jid,
                pausedPos: this.pausedJobPosition ? { x: this.pausedJobPosition.x, y: this.pausedJobPosition.y, z: this.pausedJobPosition.z } : null,
                phaseDuration: this.getCurrentPhaseDuration(),
            });
        } catch { }
        if (currentJob && this.pausedJobPosition) {
            currentJob.mesh.position.copy(this.pausedJobPosition);
            // Update engine-job connector if present
            if (currentJob.engineJobConnector) {
                this.updateEngineJobConnector(currentJob.engineJobConnector, this.enginePosition, this.pausedJobPosition);
            }
        }
        // Rebase phaseStartTime so elapsed continues from paused offset
        const now = performance.now();
        // Clamp pausedElapsedInPhase to duration of current phase to avoid overshoot
        const phaseDuration = this.getCurrentPhaseDuration();
        const clamped = Math.min(Math.max(this.pausedElapsedInPhase, 0), phaseDuration);
        this.phaseStartTime = now - clamped;
        // Clear paused snapshots
        this.pausedElapsedInPhase = 0;
        this.pausedJobPosition = null;
        try {
            const jid = currentJob?.mesh.userData?.id;
            const pos = currentJob?.mesh.position;
            console.log('[WFV.resume:end]', {
                phase: this.phase,
                jobId: jid,
                restoredPos: pos ? { x: pos.x, y: pos.y, z: pos.z } : null,
                phaseStartTime: this.phaseStartTime,
            });
        } catch { }
        // Defer clearing resuming flag to next tick to allow position to render
        setTimeout(() => { this.isResuming = false; }, 0);
        // awaitingStepComplete remains unchanged if we paused inside
    }

    // Helper: duration in ms for current phase (used to clamp resume offset)
    private getCurrentPhaseDuration(): number {
        switch (this.phase) {
            case AnimationPhase.PULLING_IN: return this.config.pullInDuration;
            case AnimationPhase.INSIDE: return this.config.pauseInside; // logical max pause
            case AnimationPhase.PULLING_OUT: return this.config.pullOutDuration;
            case AnimationPhase.PAUSING: return this.config.pauseBetweenJobs;
            default: return 0;
        }
    }

    /**
     * Check if animation is currently running
     */
    public isActive(): boolean {
        return this.isRunning;
    }

    /**
     * Get the ID of the currently animating job (if any)
     */
    public getCurrentJobId(): string | null {
        if (!this.isRunning || this.jobs.length === 0 || this.currentJobIndex < 0) return null;
        const currentJob = this.jobs[this.currentJobIndex];
        if (!currentJob) return null;
        return currentJob.mesh.userData?.id ?? null;
    }

    /**
     * Get the current sequence index (0-based position in the strategy steps)
     */
    public getCurrentSequenceIndex(): number {
        return this.sequencePointer;
    }

    /**
     * Update animation state (called from main render loop)
     */
    public update(): void {
        if (this.timelineMode) return;
        if (!this.isRunning || this.isResuming) return;

        const now = performance.now();
        const elapsed = now - this.phaseStartTime;

        const currentJob = this.resolveCurrentJob();
        if (!currentJob) {
            this.isRunning = false;
            return;
        }

        if (this.logNextUpdate) {
            try {
                const jid = currentJob.mesh.userData?.id;
                console.log('[WFV.update:firstAfterResume]', {
                    phase: this.phase,
                    elapsed,
                    jobId: jid,
                    phaseDuration: this.getCurrentPhaseDuration(),
                    pos: { x: currentJob.mesh.position.x, y: currentJob.mesh.position.y, z: currentJob.mesh.position.z },
                });
            } catch { }
            this.logNextUpdate = false;
        }

        switch (this.phase) {
            case AnimationPhase.PAUSING:
                if (elapsed >= this.config.pauseBetweenJobs) {
                    this.phase = AnimationPhase.PULLING_IN;
                    this.phaseStartTime = now;
                    const jobId = currentJob.mesh.userData?.id;
                    // Highlight and show roles (wheel rotation already animated by space)
                    this.highlightJob(currentJob);
                    if (jobId && this.config.onJobHUDShow) {
                        this.config.onJobHUDShow(jobId);
                    }
                }
                break;

            case AnimationPhase.PULLING_IN:
                if (elapsed >= this.config.pullInDuration) {
                    // Pull-in complete, pause inside
                    this.phase = AnimationPhase.INSIDE;
                    this.phaseStartTime = now;
                    this.awaitingStepComplete = true;
                    // Reset job highlight color only (keep connectors visible) and highlight engine
                    this.resetJobHighlightColor(currentJob);
                    this.highlightEngine();
                    // If the step already completed earlier, wait for pauseInside before advancing
                    const jid = currentJob.mesh.userData?.id as string | undefined;
                    if (jid && this.completedJobIds.has(jid)) {
                        this.completedJobIds.delete(jid);
                        this.awaitingStepComplete = false;
                        // Don't immediately advance - let INSIDE phase show the engine highlight
                    }
                } else {
                    // Animate pull-in
                    const t = elapsed / this.config.pullInDuration;
                    const easedT = this.config.pullInEasing(t);
                    this.updateJobPosition(currentJob, easedT);
                }
                break;

            case AnimationPhase.INSIDE:
                // Gate on external step-complete signal; optional time pause can be kept as fallback
                if (!this.awaitingStepComplete && elapsed >= this.config.pauseInside) {
                    // Inside pause complete, start pull-out
                    this.phase = AnimationPhase.PULLING_OUT;
                    this.phaseStartTime = now;
                    // Reset engine highlight and re-highlight job for pull-out
                    this.resetEngineHighlight();
                    this.highlightJob(currentJob);
                    // Draw output connectors when job completes
                    const jobId = currentJob.mesh.userData?.id;
                    if (jobId && this.config.onJobOutputConnectors) {
                        this.config.onJobOutputConnectors(jobId);
                    }
                }
                // Job stays at full pull-in depth during this phase
                break;

            case AnimationPhase.PULLING_OUT:
                if (elapsed >= this.config.pullOutDuration) {
                    // Pull-out complete, reset job and move to next
                    currentJob.mesh.position.copy(currentJob.originalPosition);
                    if (currentJob.engineJobConnector) {
                        this.updateEngineJobConnector(currentJob.engineJobConnector, this.enginePosition, currentJob.originalPosition);
                    }

                    // Reset job highlights and hide connectors/roles
                    this.resetJobHighlight(currentJob);

                    // Hide HUD for this job
                    const jobId = currentJob.mesh.userData?.id;
                    if (jobId && this.config.onJobHUDHide) {
                        this.config.onJobHUDHide(jobId);
                    }

                    // Sequence-aware advancement and optional deferred stop
                    const wasLastInSequence = this.jobSequenceIds && (this.sequencePointer >= (this.jobSequenceIds.length - 1));
                    this.advanceToNextInSequence();
                    this.awaitingStepComplete = false;
                    this.phase = AnimationPhase.PAUSING;
                    this.phaseStartTime = now;

                    // Request wheel rotation for next job (will animate during PAUSING)
                    const nextJob = this.resolveCurrentJob();
                    if (nextJob) {
                        const nextJobId = nextJob.mesh.userData?.id;
                        if (nextJobId) {
                            this.startWheelRotationAnimation(nextJobId);
                        }
                    }

                    if (this.stopRequested && wasLastInSequence) {
                        this.isRunning = false;
                        this.resetHighlights();
                        if (this.config.onAnimationStop) this.config.onAnimationStop();
                        return;
                    }
                } else {
                    // Animate pull-out
                    const t = elapsed / this.config.pullOutDuration;
                    const easedT = this.config.pullOutEasing(t);
                    this.updateJobPosition(currentJob, 1 - easedT);
                }
                break;
        }
    }

    /** Enable timeline-driven mode with an explicit job sequence. */
    public enableTimelineMode(jobSequence: string[]): void {
        this.timelineMode = true;
        this.setJobSequence(jobSequence);
        // Ensure a known baseline state
        this.isRunning = true;
        this.phase = AnimationPhase.PAUSING;
        this.pausedElapsedInPhase = 0;
        this.pausedJobPosition = null;
        this.rotationRequestedForCurrentPause = false;
    }

    /** Disable timeline mode and return to internal update loop control. */
    public disableTimelineMode(): void {
        this.timelineMode = false;
    }

    /** Apply a timeline tick: set job, phase, and interpolate by normalized t (0..1). */
    public applyTimelineTick(jobId: string, phaseName: 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT', t: number): void {
        if (!this.timelineMode) return;
        // Muted TIMELINE_TICK logs to reduce noise
        // Align current job to the provided jobId
        let currentJob = this.jobs[this.currentJobIndex];
        let jobChanged = false;
        if (!currentJob || !this.matchesJobId(currentJob.mesh, jobId)) {
            const idx = this.jobs.findIndex(j => this.matchesJobId(j.mesh, jobId));
            if (idx >= 0) {
                // On job switch, reset previous highlights
                if (currentJob) {
                    try { this.resetJobHighlight(currentJob); } catch { }
                }
                this.currentJobIndex = idx;
                currentJob = this.jobs[this.currentJobIndex];
                jobChanged = true;
                // Muted TIMELINE_TICK: Job changed
            } else {
                // Unknown job id. Log once per id to avoid console spam.
                if (!this.unknownTimelineJobIdsLogged.has(jobId)) {
                    this.unknownTimelineJobIdsLogged.add(jobId);
                    try {
                        const known = this.jobs.map(j => ({ id: j.mesh.userData?.id })).slice(0, 8);
                        console.warn('[StrategyAnimation] Unknown timeline jobId:', jobId, 'knownJobs(sample)=', known);
                    } catch { /* ignore */ }
                }
                return; // unknown job id
            }
        }

        // Map phase name to enum
        const nextPhase = ((): AnimationPhase => {
            switch (phaseName) {
                case 'PULLING_IN': return AnimationPhase.PULLING_IN;
                case 'INSIDE': return AnimationPhase.INSIDE;
                case 'PULLING_OUT': return AnimationPhase.PULLING_OUT;
                case 'PAUSING':
                default: return AnimationPhase.PAUSING;
            }
        })();

        // Handle phase transitions (one-time side effects)
        if (this.phase !== nextPhase) {
            const prevPhase = this.phase;
            this.phase = nextPhase;
            const jobIdCb = currentJob.mesh.userData?.id;
            // console.log(`[SEQUENCE] Phase transition: ${prevPhase} → ${nextPhase} for job ${jobIdCb}`);

            // Enter hooks
            if (nextPhase === AnimationPhase.PAUSING) {
                // Request rotation for this job; animate during PAUSING
                if (!this.rotationRequestedForCurrentPause) {
                    // console.log(`[SEQUENCE] 1. Entering PAUSING - requesting wheel rotation for job ${jobIdCb}`);
                    if (jobIdCb) {
                        this.startWheelRotationAnimation(jobIdCb);
                    }
                    this.rotationRequestedForCurrentPause = true;
                }
            } else if (nextPhase === AnimationPhase.PULLING_IN) {
                // Wheel rotation complete; show roles and highlight
                // console.log(`[SEQUENCE] 2. Entering PULLING_IN - showing roles and highlighting job ${jobIdCb}`);
                this.highlightJob(currentJob);
                if (jobIdCb && this.config.onJobHUDShow) this.config.onJobHUDShow(jobIdCb);
                this.rotationRequestedForCurrentPause = false;
            } else if (nextPhase === AnimationPhase.INSIDE) {
                // console.log(`[SEQUENCE] 3. Entering INSIDE - highlighting engine for job ${jobIdCb}`);
                this.resetJobHighlightColor(currentJob);
                this.highlightEngine();
                this.rotationRequestedForCurrentPause = false;
            } else if (nextPhase === AnimationPhase.PULLING_OUT) {
                // console.log(`[SEQUENCE] 4. Entering PULLING_OUT - highlighting job ${jobIdCb}`);
                this.resetEngineHighlight();
                this.highlightJob(currentJob);
                // Draw output connectors when job completes
                if (jobIdCb && this.config.onJobOutputConnectors) {
                    this.config.onJobOutputConnectors(jobIdCb);
                }
                this.rotationRequestedForCurrentPause = false;
            } else if (nextPhase === AnimationPhase.PAUSING) {
                // Completed previous job cycle
                // console.log(`[SEQUENCE] 5. Re-entering PAUSING (cycle complete) - hiding HUD for job ${jobIdCb}`);
                this.resetJobHighlight(currentJob);
                if (jobIdCb && this.config.onJobHUDHide) this.config.onJobHUDHide(jobIdCb);
                this.rotationRequestedForCurrentPause = false;
            }
            // Exit hooks for prevPhase already covered by enter of next or explicit resets
        }

        // Apply positional interpolation based on phase
        if (this.phase === AnimationPhase.PULLING_IN) {
            const easedT = this.config.pullInEasing(Math.max(0, Math.min(1, t)));
            this.updateJobPosition(currentJob, easedT);
        } else if (this.phase === AnimationPhase.INSIDE) {
            this.updateJobPosition(currentJob, 1);
        } else if (this.phase === AnimationPhase.PULLING_OUT) {
            const easedT = this.config.pullOutEasing(Math.max(0, Math.min(1, t)));
            this.updateJobPosition(currentJob, 1 - easedT);
        } else if (this.phase === AnimationPhase.PAUSING) {
            // Return to original position
            currentJob.mesh.position.copy(currentJob.originalPosition);
            if (currentJob.engineJobConnector) this.updateEngineJobConnector(currentJob.engineJobConnector, this.enginePosition, currentJob.originalPosition);
        }
    }

    /**
     * Signal that the current job's step has completed; advance out of INSIDE.
     * Optionally verify by execution/job id (when provided).
     */
    public onStepComplete(executionOrJobId?: string): void {
        if (!this.isRunning || this.isResuming) return;
        const currentJob = this.resolveCurrentJob();
        if (!currentJob) return;
        const currentJobId = currentJob.mesh.userData?.id as string | undefined;
        if (executionOrJobId && currentJobId && executionOrJobId !== currentJobId) {
            // Buffer completion for when that job reaches INSIDE
            this.completedJobIds.add(executionOrJobId);
            return;
        }
        if (this.phase === AnimationPhase.INSIDE) {
            this.awaitingStepComplete = false;
            // Don't immediately transition - let the INSIDE phase complete its duration
            // The engine will stay highlighted until INSIDE→PULLING_OUT transition in update()
        } else if (currentJobId) {
            // Not yet inside: mark as completed to consume later
            this.completedJobIds.add(currentJobId);
        }
    }

    /**
     * Reorder jobs to match a desired jobId sequence (e.g., from strategy steps).
     */
    public setJobOrder(jobIdsInOrder: string[]): void {
        this.jobOrderIds = [...jobIdsInOrder];
        // Stable sort jobs by the index of their jobId in provided order, unknowns at end
        const indexOf = (mesh: THREE.Mesh): number => {
            const ud = mesh.userData as unknown as { id?: unknown };
            const primary = typeof ud?.id === 'string' ? ud.id : String(ud?.id ?? '');
            const idx = this.jobOrderIds ? this.jobOrderIds.indexOf(primary) : -1;
            return idx >= 0 ? idx : Number.MAX_SAFE_INTEGER - 1;
        };
        this.jobs.sort((a, b) => indexOf(a.mesh) - indexOf(b.mesh));
        // Reset to start of sequence
        this.currentJobIndex = 0;
    }

    /**
     * Provide the full step sequence including repeated jobIds.
     */
    public setJobSequence(jobIdsInSequence: string[]): void {
        this.jobSequenceIds = [...jobIdsInSequence];
        this.sequencePointer = 0;
        // Ensure currentJobIndex points at the first job in sequence
        const first = this.jobSequenceIds[0];
        if (first) {
            const idx = this.jobs.findIndex(j => this.matchesJobId(j.mesh, first));
            if (idx >= 0) this.currentJobIndex = idx;
        }
    }

    /** Resolve job target given current pointers (sequence-aware). */
    private resolveCurrentJob(): StrategyAnimationTarget | undefined {
        if (this.jobSequenceIds && this.jobSequenceIds.length > 0) {
            const targetId = this.jobSequenceIds[this.sequencePointer] || this.jobSequenceIds[this.jobSequenceIds.length - 1];
            const idx = this.jobs.findIndex(j => this.matchesJobId(j.mesh, targetId));
            return idx >= 0 ? this.jobs[idx] : this.jobs[this.currentJobIndex];
        }
        return this.jobs[this.currentJobIndex];
    }

    /** Advance to next job per step sequence; fallback to unique cycle when absent. */
    private advanceToNextInSequence(): void {
        if (this.jobSequenceIds && this.jobSequenceIds.length > 0) {
            this.sequencePointer = Math.min(this.sequencePointer + 1, this.jobSequenceIds.length - 1);
            const nextId = this.jobSequenceIds[this.sequencePointer];
            const idx = this.jobs.findIndex(j => this.matchesJobId(j.mesh, nextId));
            if (idx >= 0) this.currentJobIndex = idx;
        } else {
            this.currentJobIndex = (this.currentJobIndex + 1) % this.jobs.length;
        }
    }

    public requestStopAfterSequenceEnd(): void {
        this.stopRequested = true;
    }

    /**
     * Highlight the active job
     */
    private highlightJob(job: StrategyAnimationTarget): void {
        if (!this.config.highlightJobColor) {
            // console.log('StrategyAnimationAnimation: No highlightJobColor configured');
            return;
        }

        const material = job.mesh.material as THREE.MeshStandardMaterial;

        // Mark mesh as animation-controlled to prevent XrInteractor from overriding
        job.mesh.userData.__animationControlled = true;

        material.emissive.set(this.config.highlightJobColor);

        // Trigger callback to draw roles (creates connectors & meshes)
        const jobId = job.mesh.userData.id;
        if (jobId && this.config.onJobStartAnimation) {
            this.config.onJobStartAnimation(jobId);
        }

        // Show job-role connectors
        if (job.jobRoleConnectors) {
            job.jobRoleConnectors.forEach(connector => {
                connector.visible = true;
            });
        }

        // Show role-resource INPUT connectors (outputs shown later during PULLING_OUT)
        if (job.roleResourceInputConnectors) {
            job.roleResourceInputConnectors.forEach(connector => {
                connector.visible = true;
            });
            // Highlight input resources
            this.highlightInputResources(job);
        }

        // Show role meshes
        if (jobId && this.config.onToggleRoleVisibility) {
            this.config.onToggleRoleVisibility(jobId, true);
        }
    }

    /**
     * Highlight input resources connected by the given connectors
     */
    private highlightInputResources(job: StrategyAnimationTarget): void {
        if (!job.roleResourceInputConnectors) return;
        
        for (const connector of job.roleResourceInputConnectors) {
            const resMesh = connector.userData.resourceMesh as THREE.Mesh | undefined;
            if (!resMesh) continue;
            
            const material = resMesh.material as THREE.MeshStandardMaterial;
            // Store original values if not already highlighted
            if (!job.highlightedInputResources!.has(resMesh)) {
                // Try to find matching outline sibling
                let outline: THREE.LineSegments | undefined;
                const resId = resMesh.userData?.id;
                const parent = resMesh.parent as THREE.Group | null;
                if (parent) {
                    for (const child of parent.children) {
                        if (child instanceof THREE.LineSegments && child.userData?.isOutline && child.userData?.parentId === resId) {
                            outline = child as THREE.LineSegments;
                            break;
                        }
                    }
                }
                const outlineMat = outline ? (outline.material as THREE.LineBasicMaterial) : undefined;
                // Prefer baseline values if present
                const baseColor: THREE.Color = resMesh.userData?.__baselineEmissiveColor ?? material.emissive.clone();
                const baseIntensity: number = typeof resMesh.userData?.__baselineEmissiveIntensity === 'number' ? resMesh.userData.__baselineEmissiveIntensity : (material.emissiveIntensity ?? 1);
                const baselineOutlineColor = outline && outline.userData ? outline.userData.__baselineOutlineColor as THREE.Color | undefined : undefined;
                job.highlightedInputResources!.set(resMesh, {
                    color: baseColor.clone(),
                    intensity: baseIntensity,
                    outline,
                    outlineColor: outlineMat ? (baselineOutlineColor ? baselineOutlineColor.clone() : outlineMat.color.clone()) : undefined,
                });
            }
                        // Also tint outline to match
                        const info = job.highlightedInputResources!.get(resMesh);
                        if (info?.outline) {
                            const om = info.outline.material as THREE.LineBasicMaterial;
                            om.color.set(0x00cc66);
                        }
            
            // Mark mesh as animation-controlled to prevent XrInteractor from overriding
            resMesh.userData.__animationControlled = true;
            
            // Apply green highlight (matching input connector color)
            material.emissive.set(0x00cc66);
            // Emissive pulse for clarity
            if (typeof material.emissiveIntensity === 'number') material.emissiveIntensity = 2.0;

            // Debug log: input resource highlighted
            try {
                const jobId = job.mesh.userData?.id;
                const resId = resMesh.userData?.id;
                const stepInfo = this.config.getExecutionInfo ? this.config.getExecutionInfo(jobId) : undefined;
                const stepStr = stepInfo && typeof stepInfo.stepIndex === 'number' ? ` step=${stepInfo.stepIndex}` : '';
                console.log(`[Highlight/Input] jobId=${jobId}${stepStr} resourceId=${resId} meshName=${resMesh.name}`);
            } catch { /* noop */ }
        }
    }

    /**
     * Highlight output resources connected by the given connectors
     */
    private highlightOutputResources(job: StrategyAnimationTarget): void {
        if (!job.roleResourceOutputConnectors) return;

        for (const connector of job.roleResourceOutputConnectors) {
            let resMesh = connector.userData.resourceMesh as THREE.Mesh | undefined;
            // Fallback: resolve via resourceId if mesh was not embedded
            if (!resMesh && this.config.getResourceMeshById && connector.userData.resourceId) {
                resMesh = this.config.getResourceMeshById(connector.userData.resourceId as string);
            }
            if (!resMesh) continue;

            const material = resMesh.material as THREE.MeshStandardMaterial;
            if (!material) continue;

            // Store original values if not already highlighted
            if (!job.highlightedOutputResources!.has(resMesh)) {
                // Try to find matching outline sibling
                let outline: THREE.LineSegments | undefined;
                const resId = resMesh.userData?.id;
                const parent = resMesh.parent as THREE.Group | null;
                if (parent) {
                    for (const child of parent.children) {
                        if (child instanceof THREE.LineSegments && child.userData?.isOutline && child.userData?.parentId === resId) {
                            outline = child as THREE.LineSegments;
                            break;
                        }
                    }
                }
                const outlineMat = outline ? (outline.material as THREE.LineBasicMaterial) : undefined;
                // Prefer baseline values if present
                const baseColor: THREE.Color = resMesh.userData?.__baselineEmissiveColor ?? material.emissive.clone();
                const baseIntensity: number = typeof resMesh.userData?.__baselineEmissiveIntensity === 'number' ? resMesh.userData.__baselineEmissiveIntensity : (material.emissiveIntensity ?? 1);
                const baselineOutlineColorOut = outline && outline.userData ? outline.userData.__baselineOutlineColor as THREE.Color | undefined : undefined;
                job.highlightedOutputResources!.set(resMesh, {
                    color: baseColor.clone(),
                    intensity: baseIntensity,
                    outline,
                    outlineColor: outlineMat ? (baselineOutlineColorOut ? baselineOutlineColorOut.clone() : outlineMat.color.clone()) : undefined,
                });
            }
            // Also tint outline to match
            const info = job.highlightedOutputResources!.get(resMesh);
            if (info?.outline) {
                const om = info.outline.material as THREE.LineBasicMaterial;
                om.color.set(0xcc0033);
            }

            // Mark mesh as animation-controlled to prevent XrInteractor from overriding
            resMesh.userData.__animationControlled = true;

            // Apply red highlight (matching output connector color)
            material.emissive.set(0xcc0033);
            // Emissive pulse for clarity
            if (typeof material.emissiveIntensity === 'number') material.emissiveIntensity = 2.0;

            // Debug log: output resource highlighted
            try {
                const jobId = job.mesh.userData?.id;
                const resId = resMesh.userData?.id || connector.userData?.resourceId;
                const stepInfo = this.config.getExecutionInfo ? this.config.getExecutionInfo(jobId) : undefined;
                const stepStr = stepInfo && typeof stepInfo.stepIndex === 'number' ? ` step=${stepInfo.stepIndex}` : '';
                console.log(`[Highlight/Output] jobId=${jobId}${stepStr} resourceId=${resId} meshName=${resMesh.name}`);
            } catch { /* noop */ }
        }
    }

    /**
     * Highlight the engine
     */
    private highlightEngine(): void {
        if (!this.config.highlightEngineColor) {
            return;
        }
        if (!this.engineMesh) {
            return;
        }

        const material = this.engineMesh.material as THREE.MeshStandardMaterial;

        // Mark mesh as animation-controlled to prevent XrInteractor from overriding
        this.engineMesh.userData.__animationControlled = true;

        material.emissive.set(this.config.highlightEngineColor);
    }

    /**
     * Reset job highlight color only (keep connectors/roles visible)
     */
    private resetJobHighlightColor(job: StrategyAnimationTarget): void {
        const material = job.mesh.material as THREE.MeshStandardMaterial;
        material.emissive.copy(job.originalColor);
        // Allow XrInteractor to control this mesh again
        delete job.mesh.userData.__animationControlled;
    }

    /**
     * Reset resource highlights for a job
     */
    private resetResourceHighlights(job: StrategyAnimationTarget): void {
        // Reset input resource highlights
        if (job.highlightedInputResources) {
            for (const [resMesh, original] of job.highlightedInputResources) {
                const material = resMesh.material as THREE.MeshStandardMaterial;
                material.emissive.copy(original.color);
                if (typeof material.emissiveIntensity === 'number') material.emissiveIntensity = original.intensity;
                // Allow XrInteractor to control this mesh again
                delete resMesh.userData.__animationControlled;
                // Restore outline
                if (original.outline && original.outlineColor) {
                    const om = original.outline.material as THREE.LineBasicMaterial;
                    om.color.copy(original.outlineColor);
                }
            }
            try {
                console.log(`[Highlight/Clear/Input] jobId=${job.mesh.userData?.id} count=${job.highlightedInputResources.size}`);
            } catch { /* noop */ }
            job.highlightedInputResources.clear();
        }

        // Reset output resource highlights
        if (job.highlightedOutputResources) {
            for (const [resMesh, original] of job.highlightedOutputResources) {
                const material = resMesh.material as THREE.MeshStandardMaterial;
                material.emissive.copy(original.color);
                if (typeof material.emissiveIntensity === 'number') material.emissiveIntensity = original.intensity;
                // Allow XrInteractor to control this mesh again
                delete resMesh.userData.__animationControlled;
                // Restore outline
                if (original.outline && original.outlineColor) {
                    const om = original.outline.material as THREE.LineBasicMaterial;
                    om.color.copy(original.outlineColor);
                }
            }
            try {
                console.log(`[Highlight/Clear/Output] jobId=${job.mesh.userData?.id} count=${job.highlightedOutputResources.size}`);
            } catch { /* noop */ }
            job.highlightedOutputResources.clear();
        }
    }

    /**
     * Reset job highlight and hide its connectors/roles
     */
    private resetJobHighlight(job: StrategyAnimationTarget): void {
        this.resetJobHighlightColor(job);

        // Reset resource highlights
        this.resetResourceHighlights(job);

        // Hide job-role connectors for this job
        if (job.jobRoleConnectors) {
            job.jobRoleConnectors.forEach(connector => connector.visible = false);
        }

        // Hide role-resource input connectors for this job
        if (job.roleResourceInputConnectors) {
            job.roleResourceInputConnectors.forEach(connector => connector.visible = false);
        }

        // Hide output connectors as well since the roles are being cleared
        if (job.roleResourceOutputConnectors) {
            job.roleResourceOutputConnectors.forEach(connector => connector.visible = false);
        }

        // Trigger callback to clear roles
        const jobId = job.mesh.userData.id;
        if (jobId && this.config.onJobEndAnimation) {
            this.config.onJobEndAnimation(jobId);
        }

        // Hide role meshes
        if (jobId && this.config.onToggleRoleVisibility) {
            this.config.onToggleRoleVisibility(jobId, false);
        }
    }

    /**
     * Reset engine highlight to original color
     */
    private resetEngineHighlight(): void {
        if (this.engineMesh && this.engineOriginalColor) {
            const material = this.engineMesh.material as THREE.MeshStandardMaterial;
            material.emissive.copy(this.engineOriginalColor);
            // Allow XrInteractor to control this mesh again
            delete this.engineMesh.userData.__animationControlled;
        }
    }

    /**
     * Reset all highlights to original colors (convenience method)
     */
    private resetHighlights(): void {
        const currentJob = this.jobs[this.currentJobIndex];
        if (currentJob) {
            this.resetJobHighlight(currentJob);
        }
        this.resetEngineHighlight();
    }

    /**
     * Update job position based on animation progress
     * @param job The job to update
     * @param t Progress from 0 (original position) to 1 (inside engine)
     */
    private updateJobPosition(job: StrategyAnimationTarget, t: number): void {
        // Calculate target position (interpolate between original position and engine position)
        const targetPos = new THREE.Vector3().lerpVectors(
            job.originalPosition,
            this.enginePosition,
            t * this.config.pullInDepth
        );

        const jobId = job.mesh.userData?.id;
        if (t === 0 || t === 1) {
            // console.log(`[PULL_IN] Job ${jobId} at t=${t}: originalPos=${job.originalPosition.toArray()}, enginePos=${this.enginePosition.toArray()}, targetPos=${targetPos.toArray()}`);
        }

        job.mesh.position.copy(targetPos);

        // Update engine-job connector if it exists
        if (job.engineJobConnector) {
            this.updateEngineJobConnector(job.engineJobConnector, this.enginePosition, targetPos);
        }

        // Update role connector lines if they exist
        if (job.jobRoleConnectors) {
            // Compute job space-scale radius once
            if (!job.mesh.geometry.boundingSphere) job.mesh.geometry.computeBoundingSphere();
            const localRadius = job.mesh.geometry.boundingSphere?.radius ?? 0;
            const worldScale = new THREE.Vector3();
            job.mesh.getWorldScale(worldScale);
            const radiusSpace = localRadius * Math.max(worldScale.x, worldScale.y, worldScale.z);

            // Get the job's current space position (accounts for parent rotation)
            const jobSpacePos = new THREE.Vector3();
            job.mesh.getWorldPosition(jobSpacePos);

            for (const connector of job.jobRoleConnectors) {
                const positions = connector.geometry.attributes.position.array as Float32Array;
                // Second endpoint is the role position (space)
                const roleX = positions[3];
                const roleY = positions[4];
                const roleZ = positions[5];
                const rolePos = new THREE.Vector3(roleX, roleY, roleZ);
                // Anchor on the job surface (space position) toward the role position
                const dir = rolePos.clone().sub(jobSpacePos);
                const fromPt = dir.lengthSq() < 1e-9
                    ? jobSpacePos.clone()
                    : jobSpacePos.clone().add(dir.normalize().multiplyScalar(radiusSpace));
                positions[0] = fromPt.x;
                positions[1] = fromPt.y;
                positions[2] = fromPt.z;
                connector.geometry.attributes.position.needsUpdate = true;
            }
        }
    }

    /**
     * Update engine-job connector geometry to connect engine to job
     */
    private updateEngineJobConnector(connector: THREE.Line, enginePos: THREE.Vector3, jobPos: THREE.Vector3): void {
        const positions = new Float32Array([
            enginePos.x, enginePos.y, enginePos.z,
            jobPos.x, jobPos.y, jobPos.z,
        ]);
        connector.geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        connector.geometry.attributes.position.needsUpdate = true;
    }

    /**
     * Start wheel rotation animation to align job with TYPE_Natural
     * Returns the rotation duration in ms
     */
    public startWheelRotationAnimation(jobId: string): number {
        const debugWheel = this.config.debugWheel ?? false;
        if (debugWheel) {
            console.log('[WHEEL_ROTATION] startWheelRotationAnimation called for jobId:', jobId);
        }

        // Single-flight: ignore if a rotation is already in progress
        if (this.isRotatingWheel) {
            if (debugWheel) {
                console.log('[WHEEL_ROTATION] Rotation already in progress; ignoring duplicate request for jobId:', jobId);
            }
            return 0;
        }

        // Get wheel group and required meshes
        const wheelGroup = this.config.getWheelGroup?.();
        if (!wheelGroup) {
            if (debugWheel) console.log('[WHEEL_ROTATION] No wheel group found!');
            return 0;
        }

        const jobMesh = this.config.getJobMeshById?.(jobId);
        const intMesh = this.config.getTypeNaturalMesh?.();
        const engineMesh = this.config.getEngineMesh?.();

        if (!jobMesh || !intMesh || !engineMesh) {
            if (debugWheel) {
                console.log('[WHEEL_ROTATION] Missing meshes - job:', !!jobMesh, 'int:', !!intMesh, 'engine:', !!engineMesh);
            }
            return 0;
        }

        // Get job's ORIGINAL position (not animated position during pull-in/out)
        const jobOriginalPos = this.getJobOriginalPosition(jobMesh);
        if (!jobOriginalPos) {
            console.log('[WHEEL_ROTATION] Could not get job original position');
            return 0;
        }

        // Temporarily set job to original position to get accurate space position for rotation calc
        const savedPosition = jobMesh.position.clone();
        jobMesh.position.copy(jobOriginalPos);
        jobMesh.updateMatrixWorld(true);

        // Compute angles around origin (engine is at origin, wheel group rotates around Y)
        const enginePos = new THREE.Vector3();
        engineMesh.getWorldPosition(enginePos);
        const jobPos = new THREE.Vector3();
        jobMesh.getWorldPosition(jobPos);
        const intPos = new THREE.Vector3();
        intMesh.getWorldPosition(intPos);

        // Restore job's animated position
        jobMesh.position.copy(savedPosition);
        jobMesh.updateMatrixWorld(true);

        const jobVec = jobPos.clone().sub(enginePos);
        jobVec.y = 0;
        const intVec = intPos.clone().sub(enginePos);
        intVec.y = 0;

        if (jobVec.lengthSq() < 1e-6 || intVec.lengthSq() < 1e-6) {
            console.log('[WHEEL_ROTATION] Zero-length vectors');
            return 0;
        }

        const jobAngle = Math.atan2(jobVec.z, jobVec.x);
        const intAngle = Math.atan2(intVec.z, intVec.x);
        const delta = this.normalizeAngle(intAngle - jobAngle);

        // If there's effectively no rotation needed, we're already aligned
        if (Math.abs(delta) < 1e-4) {
            console.log('[WHEEL_ROTATION] Job already aligned (delta ~0); skipping rotation animation');
            return 0;
        }

        // Start animation: store target and current state
        // CRITICAL: Negate delta because rotating the parent wheel group rotates children in opposite direction
        this.wheelRotationTarget = -delta;
        this.wheelRotationCurrent = 0;
        this.wheelRotationStartTime = performance.now();
        this.wheelRotationStartY = wheelGroup.rotation.y;
        this.wheelRotationTargetY = this.wheelRotationStartY - delta;
        this.wheelRotationTargetJobId = jobId;
        this.isRotatingWheel = true;

        return this.wheelRotationDuration;
    }

    /**
     * Update wheel rotation animation (call every frame)
     */
    public updateWheelRotation(): void {
        if (!this.isRotatingWheel) return;

        const wheelGroup = this.config.getWheelGroup?.();
        if (!wheelGroup) {
            console.log('[WHEEL_ROTATION] Update called but no wheel group');
            this.isRotatingWheel = false;
            return;
        }

        const elapsed = performance.now() - this.wheelRotationStartTime;
        const t = Math.min(elapsed / this.wheelRotationDuration, 1);
        
        // Use configured easing or default to quintic ease-in-out
        const easingFn = this.config.wheelRotationEasing ?? ((t: number) => {
            return (t < 0.5)
                ? 16 * t * t * t * t * t
                : 1 - Math.pow(-2 * t + 2, 5) / 2;
        });
        const easedT = easingFn(t);
        
        const desiredY = this.wheelRotationStartY + easedT * (this.wheelRotationTargetY - this.wheelRotationStartY);
        this.wheelRotationCurrent = easedT * this.wheelRotationTarget;

        const debugWheel = this.config.debugWheel ?? false;
        if (debugWheel) {
            console.log('[WHEEL_ROTATION] Update - t:', t.toFixed(3), 'easedT:', easedT.toFixed(3), 'wheelRotY:', wheelGroup.rotation.y.toFixed(6));
        }

        // Apply absolute rotation target to avoid drift and ensure exact final alignment
        wheelGroup.rotation.y = desiredY;

        if (t >= 1) {
            // Snap to exact final target
            wheelGroup.rotation.y = this.wheelRotationTargetY;
            console.log('[SEQUENCE] Wheel rotation complete - final rotation:', wheelGroup.rotation.y);
            this.isRotatingWheel = false;
            this.wheelRotationTargetJobId = null;
        }
    }

    /**
     * Check if wheel rotation animation is currently active
     */
    public isWheelRotating(): boolean {
        return this.isRotatingWheel;
    }

    /**
     * Normalize angle to [-PI, PI]
     */
    private normalizeAngle(a: number): number {
        let ang = a;
        while (ang > Math.PI) ang -= 2 * Math.PI;
        while (ang < -Math.PI) ang += 2 * Math.PI;
        return ang;
    }
}

/**
 * Common easing functions
 */
export const EasingFunctions = {
    /** Linear easing (no acceleration) */
    linear: (t: number): number => t,

    /** Ease in (accelerate from zero velocity) */
    easeInQuad: (t: number): number => t * t,

    /** Ease out (decelerate to zero velocity) */
    easeOutQuad: (t: number): number => t * (2 - t),

    /** Ease in-out (accelerate then decelerate) */
    easeInOutQuad: (t: number): number => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),

    /** Ease in cubic (stronger acceleration) */
    easeInCubic: (t: number): number => t * t * t,

    /** Ease out cubic (stronger deceleration) */
    easeOutCubic: (t: number): number => (--t) * t * t + 1,

    /** Ease in-out cubic */
    easeInOutCubic: (t: number): number => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),

    /** Elastic ease in */
    easeInElastic: (t: number): number => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
    },

    /** Elastic ease out */
    easeOutElastic: (t: number): number => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    },
};

/**
 * Default animation configuration
 */
export const defaultStrategyAnimationConfig: StrategyAnimationConfig = {
    pullInDuration: 1600,        // 0.8 seconds to pull in
    pullOutDuration: 600,       // 0.6 seconds to pull out
    pauseBetweenJobs: 2000,     // 2.0 seconds pause between jobs
    pauseInside: 1000,           // 0.4 seconds pause while inside
    pullInDepth: 0.95,          // Pull job 95% of the way to engine center
    pullInEasing: EasingFunctions.easeInOutCubic,
    pullOutEasing: EasingFunctions.easeOutCubic,
};
