import type { Loader } from '@toolproof-npm/visualization';
import type { CosmosSpaceData } from '@/spaces/cosmos/_lib/types';
import type { InteractionContext } from '@toolproof-npm/visualization';
import { PersistentSelector } from '@toolproof-npm/visualization';
import { SwitchingInteractor } from '@toolproof-npm/visualization';
import { makeInteractorConfig } from '@toolproof-npm/visualization';
import { makeExplorerConfig } from '@toolproof-npm/visualization';
import { makeCosmosConfig } from '@/spaces/cosmos/_lib/config';
import { CosmosSpace } from '@/components/spaces/cosmos/CosmosSpace';
import { cosmosDisplayTargetSelector } from '@/spaces/cosmos/interaction/interactionHelpers';
import * as THREE from 'three';

export interface CosmosLoaderProps {
    cosmosSpaceData: CosmosSpaceData;
    onSpaceReady?: (space: CosmosSpace) => void;
    disableVRButton?: boolean;
}

/**
 * Creates a type-safe loader for CosmosSpace.
 * Encapsulates all React hooks and config creation logic.
 * 
 * @param props - The cosmos space data, optional strategy spec, and optional space ready callback
 * @returns Loader for CosmosSpace
 */
export function createCosmosLoader(
    props: CosmosLoaderProps
): Loader<CosmosSpace, ReturnType<typeof makeCosmosConfig>, CosmosSpaceData, CosmosLoaderProps> {
    // Stable function/object identities to avoid re-renders causing XR jank
    const interactionPredicate = (obj: THREE.Object3D) => {
        const isTarget = true;
        if (isTarget) {
            // console.log('interactionPredicate found target:', obj.name, obj);
        }
        return isTarget;
    };

    // Keep persistent selector (selection via click); hover highlights are handled separately in space logic
    const activeSelector = new PersistentSelector();

    const interactorFactory = (ctx: InteractionContext) => new SwitchingInteractor(ctx, { disableVRButton: props.disableVRButton });

    const interactorConfig = makeInteractorConfig(
        { interactionPredicate, selector: activeSelector, interactorFactory },
        {
            // Optional per-page overrides go here
            // formatTest0TypesInnerHemisphere: false, // already default in baseInteractorConfig
            // Cosmos-specific: prioritize role objects for tooltip display
            displayTargetSelector: cosmosDisplayTargetSelector,
        }
    );

    const cosmosConfig = makeCosmosConfig();

    const spaceFactory = (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
        const explorerConfig = makeExplorerConfig(interactorConfig, cosmosConfig);
        return new CosmosSpace(
            explorerConfig,
            scene,
            renderer
        );
    };

    const dataTransform = (loaderProps: CosmosLoaderProps): Partial<CosmosSpaceData> => {
        return loaderProps.cosmosSpaceData;
    };

    return {
        spaceFactory,
        dataTransform,
        props,
        onSpaceReady: props.onSpaceReady,
    };
}
