'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useMemo } from 'react';
import type { ResourceTypeIdJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { useResources } from '@/_lib/client/firebaseWebHelpers';
import type { CosmosSpaceData } from '@/spaces/cosmos/_lib/types';

export type CosmosDataContextValue = {
  cosmosSpaceData: CosmosSpaceData;
  loading: boolean;
  error: Error | null;
};

const CosmosDataContext = createContext<CosmosDataContextValue | null>(null);

export function useCosmosData(): CosmosDataContextValue {
  const context = useContext(CosmosDataContext);
  if (!context) {
    throw new Error('useCosmosData must be used within a CosmosDataProvider');
  }
  return context;
}

interface CosmosDataProviderProps {
  children: ReactNode;
}

export default function CosmosDataProvider({ children }: CosmosDataProviderProps) {

  const resourceTypeIds = [ // ATTENTION: hardcoded
    'TYPE-ResourceFormat',
    'TYPE-ResourceType',
    'TYPE-Job',
    'TYPE-Natural',
    'TYPE-Boolean',
  ]

  const { items: resourceMap, loading: resourceMapLoading, error: resourceMapError } = useResources(resourceTypeIds);

  const loading = resourceMapLoading;
  const error = resourceMapError ?? null;

  /* console.log(
    '[CosmosDataProvider] Loaded Cosmos data:',
    JSON.stringify({
      resourceMap: resourceMap,
    }, null, 2)
  ); */

  const cosmosSpaceData: CosmosSpaceData = useMemo(() => ({
    resourceMap,
  }), [resourceMap]);

  const value: CosmosDataContextValue = useMemo(() => ({
    cosmosSpaceData,
    loading,
    error,
  }), [cosmosSpaceData, loading, error]);

  return (
    <CosmosDataContext.Provider value={value}>
      {children}
    </CosmosDataContext.Provider>
  );
}
