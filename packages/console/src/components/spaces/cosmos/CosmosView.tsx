'use client';
import { ExplorerHost } from '@toolproof-npm/visualization';
import type { Loader, LoaderMap } from '@toolproof-npm/visualization';
import type { SpaceInterface } from '@toolproof-npm/visualization';
import { createCosmosLoader } from '@/spaces/cosmos/integration/createCosmosLoader';
import { createGenesisLoader } from '@/spaces/genesis/integration/createGenesisLoader';
import CosmosExplanationHUD from '@/spaces/cosmos/ui/CosmosExplanationHUD';
import CosmosAnimationControls from '@/spaces/cosmos/ui/CosmosAnimationControls';
import type { CosmosSpace } from '@/spaces/cosmos/CosmosSpace';
import { useCosmosData } from '@/spaces/cosmos/integration/CosmosDataProvider';
import { useGenesisData } from '@/spaces/genesis/integration/GenesisDataProvider';
import { useMemo, useRef } from 'react';

interface CosmosViewProps {
  showOverlays?: boolean;
  disableVRButton?: boolean;
}

export default function CosmosView({ showOverlays = true, disableVRButton = false }: CosmosViewProps) {
  const { cosmosSpaceData, loading: cosmosLoading, error: cosmosError } = useCosmosData();
  const { genesisSpaceData, loading: genesisLoading, error: genesisError } = useGenesisData();
  const spaceRef = useRef<CosmosSpace | null>(null);

  const loaderMap = useMemo(() => {
    const cosmosLoader = createCosmosLoader({
      cosmosSpaceData,
      onSpaceReady: (space) => { spaceRef.current = space; },
      disableVRButton
    });

    const genesisLoader = createGenesisLoader({
      genesisSpaceData,
      disableVRButton
    });

    // Multi-space case: map with two entries
    // Type widens automatically when creating the map
    const map: LoaderMap = new Map();
    map.set('cosmos', cosmosLoader as Loader<SpaceInterface, unknown, unknown, unknown>);
    map.set('genesis', genesisLoader as Loader<SpaceInterface, unknown, unknown, unknown>);
    return map;
  }, [cosmosSpaceData, genesisSpaceData, disableVRButton]);

  const loading = cosmosLoading || genesisLoading;
  const error = cosmosError || genesisError;

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center text-sm text-gray-400">
        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-transparent" />
        Loading space data…
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full items-center justify-center px-4 text-sm text-red-500">
        Failed to load space data: {error instanceof Error ? error.message : String(error)}
      </div>
    );
  }

  return (
    <ExplorerHost
      loaderMap={loaderMap}
      initialSpaceKey="cosmos"
    >
      {showOverlays && (
        <>
          <CosmosExplanationHUD />
          <CosmosAnimationControls spaceRef={spaceRef} />
        </>
      )}
    </ExplorerHost>
  );
}
