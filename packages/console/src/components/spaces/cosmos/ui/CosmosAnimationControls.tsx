'use client';
import type { CosmosSpace } from '@/components/spaces/cosmos/CosmosSpace';
import { useState, useEffect, useCallback } from 'react';

interface CosmosAnimationControlsProps {
  spaceRef: React.RefObject<CosmosSpace | null>;
}

export default function CosmosAnimationControls({ spaceRef }: CosmosAnimationControlsProps) {
  const [isPaused, setIsPaused] = useState(true);
  const [hasStarted, setHasStarted] = useState(false);

  // Listen for animation reset event from CosmosSpace
  useEffect(() => {
    const handleAnimationReset = () => {
      setIsPaused(true);
      setHasStarted(false);
    };
    window.addEventListener('cosmos-animation-reset', handleAnimationReset);
    return () => window.removeEventListener('cosmos-animation-reset', handleAnimationReset);
  }, []);

  const togglePause = useCallback(() => {
    const space = spaceRef.current;
    if (space && 'toggleAnimationPause' in space && typeof space.toggleAnimationPause === 'function') {
      try {
        // Useful for diagnosing why start/resume does nothing.
        console.log('[CosmosAnimationControls] Start/Resume/Pause clicked');
      } catch { /* ignore */ }
      const newPausedState = space.toggleAnimationPause();
      setIsPaused(newPausedState);
      if (!hasStarted) {
        setHasStarted(true);
      }
    }
  }, [spaceRef, hasStarted]);

  return (
    <button
      onClick={togglePause}
      style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        padding: '8px 16px',
        fontSize: '14px',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        border: '2px solid white',
        borderRadius: '6px',
        cursor: 'pointer',
        zIndex: 1000,
        touchAction: 'manipulation',
        minWidth: '80px',
      }}
      className="active:scale-95 transition-transform"
    >
      {!hasStarted ? '▶ Start' : isPaused ? '▶ Resume' : '⏸ Pause'}
    </button>
  );
}
