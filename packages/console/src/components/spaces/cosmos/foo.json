{"resourceMap": {"TYPE-Job": [{"identity": "RESOURCE-UciZcEbbdyXlTDE5jcSE", "resourceTypeId": "TYPE-Job", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-qBP0VrEz1k1xyq5zAqtI"}, "kind": "materialized", "path": "TYPE-Job/1e57344390713bbe8c13ad7dbe7c7a1e6ddcf83c800d0ab69d58e69588577e9d", "timestamp": "2025-12-14T15:07:15.034Z", "extractedData": {"identity": "JOB-7dbtyPecCP99l4qVFQnh", "name": "Multiply", "description": "dummy-description", "implementation": "http://************/multiply", "roles": {"inputMap": {"ROLE-PjU1nJWLlVq4ziNHMNns": {"resourceTypeId": "TYPE-Natural", "name": "Multiplicand", "description": "dummy-description"}, "ROLE-liF7CNfPMS6s2UNgzuHC": {"resourceTypeId": "TYPE-Natural", "name": "Multiplier", "description": "dummy-description"}}, "outputMap": {"ROLE-BV6CQLJdQcBEBKON8Odi": {"resourceTypeId": "TYPE-Natural", "name": "Product", "description": "dummy-description"}}}}}, {"identity": "RESOURCE-WHvmBrdkCEDycbEs25st", "resourceTypeId": "TYPE-Job", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-9qTo4hSSK6bKSPm5YWHO"}, "kind": "materialized", "path": "TYPE-Job/b551f32e71137ab3924aa70ee0e81f276756c0a76a091ffd5efd3d801f158aaf", "timestamp": "2025-12-14T15:06:08.243Z", "extractedData": {"identity": "JOB-diBPzKkn6HSLpODTdOFB", "name": "Subtract", "description": "dummy-description", "implementation": "http://************/subtract", "roles": {"inputMap": {"ROLE-4CWFUUsHuoTza0vbhSxc": {"resourceTypeId": "TYPE-Natural", "name": "Minuend", "description": "dummy-description"}, "ROLE-b09eC8WlauVeI8swHDU4": {"resourceTypeId": "TYPE-Natural", "name": "Subtrahend", "description": "dummy-description"}}, "outputMap": {"ROLE-M6YKhIDfJZ4HbPNYxeIx": {"resourceTypeId": "TYPE-Natural", "name": "Difference", "description": "dummy-description"}}}}}, {"identity": "RESOURCE-ontpa4TFOaRFegKAaXZE", "resourceTypeId": "TYPE-Job", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-LqxFQHBVEJdcNxUOUT3Q"}, "kind": "materialized", "path": "TYPE-Job/b5b556a83666e5a4a773c358a540838cd85c9b9007f6c047d016375b8d44c7b5", "timestamp": "2025-12-14T15:03:44.463Z", "extractedData": {"identity": "JOB-s945dsUlDnwe72WYLCtR", "name": "Add", "description": "dummy-description", "implementation": "https://************/add", "roles": {"inputMap": {"ROLE-22l2GgttEtvnnQSYdg6h": {"resourceTypeId": "TYPE-Natural", "name": "AddendOne", "description": "dummy-description"}, "ROLE-c6VOzBwzoF6MbeVGL5xE": {"resourceTypeId": "TYPE-Natural", "name": "AddendTwo", "description": "dummy-description"}}, "outputMap": {"ROLE-IMRry7yMBc1fe0Pur4xl": {"resourceTypeId": "TYPE-Natural", "name": "Sum", "description": "dummy-description"}}}}}, {"identity": "RESOURCE-xe0H4Rq5p6JPkDukyZqJ", "resourceTypeId": "TYPE-Job", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-4fXW2jSgEOCYnnByC3Fp"}, "kind": "materialized", "path": "TYPE-Job/ec4e0f50a5b74cbc72cad2befec064528c5fcd849dc225964b302725cc1c8880", "timestamp": "2025-12-14T15:08:21.795Z", "extractedData": {"identity": "JOB-ifBKJAwH1DoF1y7kJYoy", "name": "Divide", "description": "dummy-description", "implementation": "http://************/divide", "roles": {"inputMap": {"ROLE-Cr2V4G6wY7FizvAlBjZM": {"resourceTypeId": "TYPE-Natural", "name": "Dividend", "description": "dummy-description"}, "ROLE-4D2Wg3NqqzPcof3yalb0": {"resourceTypeId": "TYPE-Natural", "name": "Divisor", "description": "dummy-description"}}, "outputMap": {"ROLE-m4wO3hlCzU3teNCkf7jt": {"resourceTypeId": "TYPE-Natural", "name": "Quotient", "description": "dummy-description"}, "ROLE-WhpTH4xcHoukL693gWfq": {"resourceTypeId": "TYPE-Natural", "name": "<PERSON><PERSON><PERSON>", "description": "dummy-description"}}}}}], "TYPE-ResourceFormat": [{"identity": "RESOURCE-gyH1wU78GRYIGOHlrknV", "resourceTypeId": "TYPE-ResourceFormat", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-YAawXaqwvliuY05WUoEx"}, "kind": "materialized", "path": "TYPE-ResourceFormat/4e1292f437e262dbaa6e866aa853fef09aaa8483c0b1bd0067c99a99c6caf838", "timestamp": "2025-12-14T14:26:09.300Z", "extractedData": {"identity": "FORMAT-ApplicationJson", "name": "application/json", "description": "dummy-description"}}, {"identity": "RESOURCE-1MQlJofkRoGC5LFbCPVk", "resourceTypeId": "TYPE-ResourceFormat", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-y6Lm87529C2eRPOXXuXs"}, "kind": "materialized", "path": "TYPE-ResourceFormat/b1457e6ac4c1dff4f2bf2b7fa5a06d55db8753db478c7ef1338049bf5effaf90", "timestamp": "2025-12-14T14:50:45.182Z", "extractedData": {"identity": "FORMAT-ApplicationJob", "name": "application/job", "description": "dummy-description"}}], "TYPE-ResourceType": [{"identity": "RESOURCE-MgMrvGnzDfPOMToAAhb9", "resourceTypeId": "TYPE-ResourceType", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-BrNmltipTk2BgCqdPsHa"}, "kind": "materialized", "path": "TYPE-ResourceType/206d0563abcd52571f873d69cfab9ba7ebc6dc2e0ea31ad936d0cfcf68388a9f", "timestamp": "2025-12-14T14:53:53.817Z", "extractedData": {"identity": "TYPE-Natural", "name": "Natural", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Natural", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentityValueId"}}, "required": ["identity"], "$defs": {"IdentityValueId": {"type": "integer"}}, "additionalProperties": false}}}, {"identity": "RESOURCE-F1sdlDWYKCWTLJW5nOIe", "resourceTypeId": "TYPE-ResourceType", "creationContext": {"resourceRoleId": "ROLE-Builder", "executionId": "EXECUTION-C1mjTK2vbxV2rhNeAzj2"}, "kind": "materialized", "path": "TYPE-ResourceType/823428284c3c7c046294aa91cb986894ad0e5d15b095f8a9617d12f1e37bd943", "timestamp": "2025-12-14T14:57:19.122Z", "extractedData": {"identity": "TYPE-Boolean", "name": "Boolean", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Boolean", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentityValueId"}}, "required": ["identity"], "$defs": {"IdentityValueId": {"type": "boolean"}}, "additionalProperties": false}}}]}}