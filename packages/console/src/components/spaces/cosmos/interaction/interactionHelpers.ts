import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as THREE from 'three';


/**
 * Cosmos-specific display target selector that prioritizes role objects for tooltip display.
 * This implements the Cosmos-specific behavior where hovering over a role should show
 * the role tooltip even if a job is selected.
 * 
 * @param hovered - The currently hovered object (or null)
 * @param selected - The currently selected object (or null)
 * @returns The object that should be displayed in the tooltip
 */
export function cosmosDisplayTargetSelector(
    hovered: THREE.Object3D | null,
    selected: THREE.Object3D | null
): THREE.Object3D | null {
    // Check if the hovered object is a role
    const isRole = (obj: THREE.Object3D | null): boolean => {
        if (!obj) return false;
        const ent = String(obj.userData?.entity ?? '').toLowerCase();
        // Check if entity matches the roles constant (case-insensitive)
        return ent.includes(CONSTANTS.ROLES.roles.toLowerCase());
    };

    // Prefer hovered role over selected job for tooltip display
    if (isRole(hovered)) {
        return hovered;
    }

    // Default behavior: selected || hovered
    return selected || hovered;
}

