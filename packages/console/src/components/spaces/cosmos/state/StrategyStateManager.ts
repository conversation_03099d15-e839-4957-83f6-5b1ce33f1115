import type { StatefulStrategyJson } from '@toolproof-npm/schema';
import type { TimelineSegment } from '@/spaces/cosmos/timeline/TimelineTypes';
import * as THREE from 'three';

/**
 * Manages scene graph lifecycle (create/destroy meshes) based on strategy execution state.
 * This is separate from animation (which interpolates transforms of existing objects).
 * 
 * Responsibilities:
 * - Create/destroy Natural resource panels
 * - Create/destroy role-resource connectors
 * - Create/destroy role meshes
 * - Track resource state (_createdNaturals, _naturalIdentityByResourceId, etc.)
 * - Update HUD display
 */
export class StrategyStateManager {
    private statefulStrategy: StatefulStrategyJson | null = null;
    private createdNaturals = new Set<number>([1]); // Start with Natural value 1
    private naturalIdentityByResourceId = new Map<string, number>();
    private mockPanelByNatural = new Map<number, THREE.Mesh>();
    private previousStepOutputs: Record<string, string> = {};
    private hudShowOutputForJob = new Set<string>();

    // Execution info cache for HUD display
    private stepExecutionCache = new Map<string, {
        jobId: string;
        executionId: string;
        stepIndex: number;
        totalSteps: number;
        inputBindingMap: Record<string, string>;
        outputBindingMap: Record<string, string>;
    }>();

    constructor(
        private root: THREE.Group,
        private callbacks: {
            getNames: () => { formats: string; types: string; resources: string; jobs: string; roles: string };
            getSpecials: () => { TYPE_Job: string; TYPE_Natural: string; TYPE_Boolean: string; BOOLEAN_true: string; BOOLEAN_false: string; FORMAT_ApplicationJson: string; JOB_Engine: string };
            createRoleResourceOutputConnectors: (outputBindingMap: Record<string, string>, visible: boolean) => THREE.Line[];
            drawNaturals: () => void;
            updateActiveStepHUD: (jobId: string, executionId: string, stepIndex: number, totalSteps: number, inputBindingMap: Record<string, string>, outputBindingMap: Record<string, string>) => void;
            clearActiveStepHUD: () => void;
            clearPreviousOutputHighlights: () => void;
            setJobRoleConnectors?: (jobMesh: THREE.Mesh, connectors: THREE.Line[]) => void;
            setRoleResourceOutputConnectors?: (jobMesh: THREE.Mesh, connectors: THREE.Line[]) => void;
            meshEntriesFromEntityGroup: (groupName: string) => Array<{ id: string; mesh: THREE.Mesh }>;
        }
    ) { }

    /**
     * Update the strategy spec (used for resource map lookups)
     */
    public setStatefulStrategy(spec: StatefulStrategyJson): void {
        this.statefulStrategy = spec;
        this.populateStepExecutionCache();
    }

    /**
     * Handle phase change events from timeline
     */
    public onPhaseChange(segment: TimelineSegment): void {
        const { phase, jobId } = segment;

        if (phase === 'PULLING_OUT') {
            // Job is completing - create output connectors and natural panels
            this.createOutputArtifacts(jobId);
        }
    }

    /**
     * Handle tick events (for continuous updates if needed)
     */
    public onTick(segment: TimelineSegment, t: number): void {
        // Currently no per-tick scene mutations needed
        // All discrete changes happen on phase transitions
    }

    /**
     * Reset to initial state (called when animation completes or resets)
     */
    public reset(): void {
        const n = this.callbacks.getNames();

        // Remove all dynamic scene objects
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            // Remove connectors
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-') ||
                child.name === 'job-role-connectors') {
                toRemove.push(child);
            }

            // Remove natural panels (except value 1)
            if (child.userData?.id?.startsWith('TYPE-Natural/mock-')) {
                const mockId = child.userData.id;
                const resourceIdPart = mockId.replace('TYPE-Natural/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }

            // Remove outlines for natural panels (except value 1)
            if (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Natural/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }

            // Remove labels for natural panels (except value 1)
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Natural/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }

            // Remove type-resource connectors
            if (child.userData?.isConnector) {
                toRemove.push(child);
            }
        });

        // Dispose and remove objects
        toRemove.forEach(obj => {
            obj.removeFromParent();
            if (obj instanceof THREE.Mesh) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Line || obj instanceof THREE.LineSegments) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Sprite) {
                if (obj.material) {
                    const spriteMat = obj.material as THREE.SpriteMaterial;
                    if (spriteMat.map) spriteMat.map.dispose();
                    spriteMat.dispose();
                }
            }
        });

        // Reset tracking state
        const resourceIdsToRemove: string[] = [];
        const intValuesToRemove: number[] = [];
        this.naturalIdentityByResourceId.forEach((value, resourceId) => {
            if (value !== 1) {
                resourceIdsToRemove.push(resourceId);
                intValuesToRemove.push(value);
            }
        });
        resourceIdsToRemove.forEach(id => this.naturalIdentityByResourceId.delete(id));
        intValuesToRemove.forEach(value => this.mockPanelByNatural.delete(value));

        this.createdNaturals.clear();
        this.createdNaturals.add(1);
        this.previousStepOutputs = {};
        this.hudShowOutputForJob.clear();

        // Clear highlights
        this.callbacks.clearPreviousOutputHighlights();

        // Clear HUD
        this.callbacks.clearActiveStepHUD();

        // Redraw baseline natural resources (value 1)
        this.callbacks.drawNaturals();
    }

    /**
     * Create output artifacts (connectors, panels) when a job completes
     */
    private createOutputArtifacts(jobId: string): void {
        const n = this.callbacks.getNames();
        const execInfo = this.getExecutionInfoForJob(jobId);
        if (!execInfo) return;

        // Track newly created natural values from outputs
        this.updateCreatedNaturals(execInfo.executionId, execInfo.outputBindingMap);

        // Create output role-resource connectors
        const outputConnectors = this.callbacks.createRoleResourceOutputConnectors(
            execInfo.outputBindingMap,
            false // visible immediately when created (during PULLING_OUT)
        );

        if (outputConnectors.length > 0) {
            // Add to scene
            const outputGroup = new THREE.Group();
            outputGroup.name = `role-resource-output-connectors-${execInfo.executionId}`;
            outputConnectors.forEach(line => outputGroup.add(line));
            this.root.add(outputGroup);

            // Register with animation system (if callback available)
            const jobMeshEntry = this.callbacks.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
            if (jobMeshEntry && this.callbacks.setRoleResourceOutputConnectors) {
                this.callbacks.setRoleResourceOutputConnectors(jobMeshEntry.mesh, outputConnectors);
            }
        }

        // Mark this job to show outputs in HUD
        this.hudShowOutputForJob.add(jobId);
        this.callbacks.updateActiveStepHUD(
            execInfo.jobId,
            execInfo.executionId,
            execInfo.stepIndex,
            execInfo.totalSteps,
            execInfo.inputBindingMap,
            execInfo.outputBindingMap
        );

        // Redraw natural panels to show new values
        this.callbacks.drawNaturals();
    }

    /**
     * Update created naturals set from output bindings
     */
    private updateCreatedNaturals(executionId: string, outputBindingMap: Record<string, string>): void {
        const sfs = this.statefulStrategy as unknown as { strategyState?: Record<string, Record<string, unknown>> } | null;
        const execRoleMap = sfs?.strategyState?.[executionId];
        if (!execRoleMap) return;

        for (const [roleId, resourceId] of Object.entries(outputBindingMap)) {
            const record = execRoleMap[roleId] as { extractedData?: { identity?: unknown } } | undefined;
            const maybeValue = record?.extractedData?.identity;
            if (typeof maybeValue !== 'number') continue;

            this.createdNaturals.add(maybeValue);
            this.naturalIdentityByResourceId.set(resourceId, maybeValue);
        }
    }

    /**
     * Populate step execution cache from strategy spec
     */
    private populateStepExecutionCache(): void {
        this.stepExecutionCache.clear();

        const sls = this.statefulStrategy?.statelessStrategy;
        const steps = sls?.steps;
        if (!Array.isArray(steps)) return;

        const totalSteps = steps.length;

        steps.forEach((step: unknown, index: number) => {
            const exec = (step as unknown as {
                execution?: {
                    identity?: string;
                    jobId?: string;
                    roleBindings?: {
                        inputBindingMap?: Record<string, string>;
                        outputBindingMap?: Record<string, string>;
                    };
                };
            }).execution;
            const jobId = exec?.jobId ? String(exec.jobId) : undefined;
            const executionId = exec?.identity ? String(exec.identity) : undefined;

            if (jobId && executionId) {
                // Extract input/output bindings from the schema-defined execution.roleBindings.
                // This is the canonical source; do not infer direction from role id strings.
                const inputBindingMap: Record<string, string> = { ...(exec?.roleBindings?.inputBindingMap ?? {}) };
                const outputBindingMap: Record<string, string> = { ...(exec?.roleBindings?.outputBindingMap ?? {}) };

                this.stepExecutionCache.set(jobId, {
                    jobId,
                    executionId,
                    stepIndex: index + 1,
                    totalSteps,
                    inputBindingMap,
                    outputBindingMap,
                });
            }
        });
    }

    /**
     * Get execution info for a job (for HUD display)
     */
    private getExecutionInfoForJob(jobId: string) {
        return this.stepExecutionCache.get(jobId) || null;
    }

    /**
     * Get tracking state (for external access if needed)
     */
    public getState() {
        return {
            createdNaturals: new Set(this.createdNaturals),
            naturalIdentityByResourceId: new Map(this.naturalIdentityByResourceId),
            mockPanelByNatural: new Map(this.mockPanelByNatural),
        };
    }

    /**
     * Get the set of created natural values
     */
    public getCreatedNaturals(): Set<number> {
        return this.createdNaturals;
    }

    /**
     * Get natural identity by resource ID
     */
    public getNaturalIdentity(resourceId: string): number | undefined {
        return this.naturalIdentityByResourceId.get(resourceId);
    }

    /**
     * Set natural identity mapping
     */
    public setNaturalIdentity(resourceId: string, identity: number): void {
        this.naturalIdentityByResourceId.set(resourceId, identity);
    }

    /**
     * Get mock panel by natural value
     */
    public getMockPanel(naturalValue: number): THREE.Mesh | undefined {
        return this.mockPanelByNatural.get(naturalValue);
    }

    /**
     * Set mock panel mapping
     */
    public setMockPanel(naturalValue: number, panel: THREE.Mesh): void {
        this.mockPanelByNatural.set(naturalValue, panel);
    }

    /**
     * Add created natural value
     */
    public addCreatedNatural(naturalValue: number): boolean {
        const wasNew = !this.createdNaturals.has(naturalValue);
        this.createdNaturals.add(naturalValue);
        return wasNew;
    }

    /**
     * Check if natural value exists
     */
    public hasNatural(naturalValue: number): boolean {
        return this.createdNaturals.has(naturalValue);
    }

    /**
     * Reset state to baseline (keep only natural value 1)
     */
    public resetToBaseState(): void {
        // Clear all except value 1
        const toRemove: string[] = [];
        this.naturalIdentityByResourceId.forEach((value, resourceId) => {
            if (value !== 1) {
                toRemove.push(resourceId);
            }
        });
        toRemove.forEach(id => this.naturalIdentityByResourceId.delete(id));

        // Clear panel map except value 1
        const panelsToRemove: number[] = [];
        this.mockPanelByNatural.forEach((_, value) => {
            if (value !== 1) {
                panelsToRemove.push(value);
            }
        });
        panelsToRemove.forEach(value => this.mockPanelByNatural.delete(value));

        // Reset created naturals to base state
        this.createdNaturals.clear();
        this.createdNaturals.add(1);

        // Clear other state
        this.previousStepOutputs = {};
        this.hudShowOutputForJob.clear();
        this.stepExecutionCache.clear();
    }
}
