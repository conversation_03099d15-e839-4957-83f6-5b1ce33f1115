import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Resource_ResourceType<PERSON><PERSON>, <PERSON>_Job<PERSON>, Job<PERSON>son, StatefulStrategyJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import type { CosmosConfig, CosmosSpaceData } from '@/spaces/cosmos/_lib/types';
import type { ExplorerConfig } from '@toolproof-npm/visualization';
import type { EntityMeshMap } from '@toolproof-npm/visualization';
import { BaseSpace, drawRing, findMeshById, createPortalMesh } from '@toolproof-npm/visualization';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { computeFrameworkBaseRadius } from '@/spaces/cosmos/_lib/utils';
import { iterShapesMetaMap } from '@/spaces/cosmos/_lib/utils';
import { StrategyAnimation, defaultStrategyAnimationConfig, EasingFunctions } from '@/components/spaces/cosmos/animations/StrategyAnimation';
import { <PERSON>lineRun<PERSON> } from '@/spaces/cosmos/timeline/TimelineRunner';
import { buildTimelineFromSequence } from '@/components/spaces/cosmos/timeline/StrategyTimeline';
import { type TimelineSegment } from '@/spaces/cosmos/timeline/TimelineTypes';
import { StrategyEventNormalizer } from '@/components/spaces/cosmos/timeline/StrategyEventNormalizer';
import { StrategyStateManager } from '@/components/spaces/cosmos/state/StrategyStateManager';
import type { StrategyTimeline } from '@/spaces/cosmos/timeline/TimelineTypes';
import * as THREE from 'three';

// ATTENTION_REMOVE: just forwards data
// Helpers to resolve names/special ids from config with CONSTANTS fallbacks
const getNames = () => {
    return {
        formats: CONSTANTS.SHAPES.formats,
        types: CONSTANTS.SHAPES.types,
        resources: CONSTANTS.RESOURCES.resources,
        jobs: CONSTANTS.RESOURCES.jobs,
        roles: CONSTANTS.ROLES.roles,
    };
};

// ATTENTION_REMOVE: just forwards data
const getSpecials = () => {
    return {
        TYPE_Job: CONSTANTS.SPECIALS.TYPE_Job,
        TYPE_Natural: CONSTANTS.SPECIALS.TYPE_Natural,
        TYPE_Boolean: CONSTANTS.SPECIALS.TYPE_Boolean,
        BOOLEAN_true: CONSTANTS.SPECIALS.BOOLEAN_true,
        BOOLEAN_false: CONSTANTS.SPECIALS.BOOLEAN_false,
        FORMAT_ApplicationJson: CONSTANTS.SPECIALS.FORMAT_ApplicationJson,
        JOB_Engine: CONSTANTS.SPECIALS.JOB_Engine,
    };
};

// ATTENTION: what about other connectors?
// Build a removable-name set per draw based on config
function buildRemovableSet(cfg: CosmosConfig): ReadonlySet<string> {
    const n = getNames();
    const s = getSpecials();
    const names: string[] = [
        n.formats,
        n.types,
        n.resources,
        n.jobs,
        `${n.jobs}-engine-job-connectors`,
        n.roles,
        `${n.roles}-ring-guide`,
        'job-role-connectors',
        s.JOB_Engine,
    ];
    return new Set<string>(names);
}

export class CosmosSpace extends BaseSpace<CosmosConfig, CosmosSpaceData> {
    private resourceMap: ResourceMap = {} as ResourceMap;
    private statefulStrategy: StatefulStrategyJson | null = null;
    private entityMeshMap: EntityMeshMap = {} as EntityMeshMap;
    // Cache to avoid redundant redraws of roles per-frame
    private lastRolesJobId: string | null = null;
    // Sticky highlighter disabled - we use role visualization instead
    // private stickyHighlighter?: StickySelectionHighlighter;
    private strategyAnimation?: StrategyAnimation;
    private _timelineRunner?: TimelineRunner;
    private eventNormalizer?: StrategyEventNormalizer;
    private stateManager?: StrategyStateManager;
    private _animationPaused = true;
    private _animationHasStarted = false; // Track if animation has ever been started (different from paused/running)
    private _previousStepOutputs: Record<string, string> = {}; // Track previous step's output bindings for highlighting
    private _currentJobIndex = 0; // Track current position in job sequence for timeline mode
    // Removed role hover tooltip state; DomInteractor now handles role display text directly.
    // Debug controls
    private _debug = { wheel: false, timeline: false, hud: false, roles: false };
    private _hudShowOutputForJob = new Set<string>(); // Track which jobs should show outputs in HUD
    // Parent group for engine-jobs wheel (Engine + jobs + engine-job connectors)
    private _engineJobsWheelGroup: THREE.Group | null = null;
    private stepExecutionCache: Map<string, { jobId: string; executionId: string; stepIndex: number; totalSteps: number; inputBindingMap: Record<string, string>; outputBindingMap: Record<string, string> }> = new Map();
    // Portal to other spaces
    private portalMesh: THREE.Group | null = null;

    constructor(
        explorerConfig: ExplorerConfig<CosmosConfig>,
        scene: THREE.Scene,
        renderer: THREE.WebGLRenderer
    ) {
        super(explorerConfig, scene, renderer);
        // Attach space reference to scene for interactor access
        this.scene.userData = this.scene.userData || {};
        this.scene.userData.space = this;
        // Disable sticky highlighter (side panel) - use role visualization instead
        // try {
        //     this.stickyHighlighter = new StickySelectionHighlighter(this.scene, this);
        // } catch { /* optional */ }

        // Initialize timeline configuration for event normalization
        const animConfig = explorerConfig.space.animations?.strategyAnimation;
        const timelineConfig = {
            pullInDuration: animConfig?.pullInDuration ?? defaultStrategyAnimationConfig.pullInDuration,
            pullOutDuration: animConfig?.pullOutDuration ?? defaultStrategyAnimationConfig.pullOutDuration,
            pauseBetweenJobs: animConfig?.pauseBetweenJobs ?? defaultStrategyAnimationConfig.pauseBetweenJobs,
            pauseInside: animConfig?.pauseInside ?? defaultStrategyAnimationConfig.pauseInside,
        };

        // Initialize event normalizer for live strategies
        this.eventNormalizer = new StrategyEventNormalizer(timelineConfig);

        // Initialize scene state manager for mesh lifecycle
        this.stateManager = new StrategyStateManager(
            this.root,
            {
                getNames: () => getNames(),
                getSpecials: () => getSpecials(),
                createRoleResourceOutputConnectors: this.createRoleResourceOutputConnectors.bind(this),
                drawNaturals: this.drawNaturals.bind(this),
                updateActiveStepHUD: this.updateActiveStepHUD.bind(this),
                clearActiveStepHUD: this.clearActiveStepHUD.bind(this),
                clearPreviousOutputHighlights: this.clearPreviousOutputHighlights.bind(this),
                meshEntriesFromEntityGroup: this.meshEntriesFromEntityGroup.bind(this),
            }
        );

        // Initialize job processing animation (Engine at origin) with config
        this.strategyAnimation = new StrategyAnimation(
            new THREE.Vector3(0, 0, 0),
            {
                pullInDuration: animConfig?.pullInDuration ?? defaultStrategyAnimationConfig.pullInDuration,
                pullOutDuration: animConfig?.pullOutDuration ?? defaultStrategyAnimationConfig.pullOutDuration,
                pauseBetweenJobs: animConfig?.pauseBetweenJobs ?? defaultStrategyAnimationConfig.pauseBetweenJobs,
                pauseInside: animConfig?.pauseInside ?? defaultStrategyAnimationConfig.pauseInside,
                pullInDepth: animConfig?.pullInDepth ?? defaultStrategyAnimationConfig.pullInDepth,
                pullInEasing: animConfig?.pullInEasing ?? EasingFunctions.easeInOutCubic,
                pullOutEasing: animConfig?.pullOutEasing ?? EasingFunctions.easeOutCubic,
                wheelRotationDuration: animConfig?.wheelRotationDuration ?? 800,
                wheelRotationEasing: animConfig?.wheelRotationEasing,
                highlightJobColor: animConfig?.highlightJobColor,
                highlightEngineColor: animConfig?.highlightEngineColor,
                debugWheel: this._debug.wheel,
                onJobStartAnimation: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobStartAnimation: drawing roles for job ${jobId}`);
                    // Draw roles for the animating job
                    this.drawRoles(jobId, { animation: true });
                    // Update entity filter to include roles for XR interaction
                    this.updateInteractorEntityFilter();
                },
                onJobEndAnimation: (jobId: string) => {
                    // Clear roles when animation ends (roles are removed in drawRoles on next hover)
                    // The role lines are already hidden by jobRoleConnectors visibility control
                },
                onToggleRoleVisibility: (jobId: string, visible: boolean) => {
                    // console.log(`[SEQUENCE] onToggleRoleVisibility: ${visible ? 'showing' : 'hiding'} roles for job ${jobId}`);
                    // Toggle visibility of role meshes for the animating job
                    // Role meshes are stored directly in entityMeshMap by their role ID
                    // We need to find all meshes that belong to roles for this job
                    Object.values(this.entityMeshMap).forEach(mesh => {
                        if (mesh.userData?.entity === getNames().roles) {
                            mesh.visible = visible;
                        }
                    });
                },
                onAnimationStop: () => {
                    // Re-enable all entity interactions when animation stops
                    this.updateInteractorEntityFilter();
                    // Clear HUD content when animation stops (before next loop iteration)
                    try { this.clearActiveStepHUD(); } catch { }
                    // Clear output tracking
                    this._hudShowOutputForJob.clear();
                    // Reset to initial paused state for non-looping animation
                    this.resetToInitialState();
                },
                onJobOutputConnectors: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobOutputConnectors: drawing output connectors for job ${jobId}`);
                    // Draw output role-resource connectors when job completes
                    this.drawOutputRoleResourceConnectors(jobId);
                    // Mark this job to show outputs in HUD and refresh
                    this._hudShowOutputForJob.add(jobId);
                    const exec = this.getExecutionInfoForJob(jobId);
                    if (exec) {
                        this.updateActiveStepHUD(exec.jobId, exec.executionId, exec.stepIndex, exec.totalSteps, exec.inputBindingMap, exec.outputBindingMap);
                    }
                },
                onJobHUDShow: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobHUDShow: displaying HUD for job ${jobId}`);
                    // Show HUD when job starts pulling in
                    try {
                        const exec = this.getExecutionInfoForJob(jobId);
                        if (exec) {
                            this.updateActiveStepHUD(exec.jobId, exec.executionId, exec.stepIndex, exec.totalSteps, exec.inputBindingMap, exec.outputBindingMap);
                        } else if (this._debug.hud) {
                            // console.log(`[HUD] No execution info found for jobId=${jobId}`);
                        }
                    } catch (err) {
                        console.error('[HUD] Error in onJobHUDShow:', err);
                    }
                },
                onJobHUDHide: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobHUDHide: hiding HUD for job ${jobId}`);
                    // Clear HUD content when job finishes (will be updated when next job shows)
                    try { this.clearActiveStepHUD(); } catch { }
                },
                getResourceMeshById: (resourceId: string) => {
                    return this.getResourceMeshById(resourceId);
                },
                getExecutionInfo: (jobId: string) => {
                    const info = this.getExecutionInfoForJob(jobId);
                    if (!info) return undefined;
                    return { executionId: info.executionId, stepIndex: info.stepIndex };
                },
                getWheelGroup: () => this._engineJobsWheelGroup,
                getJobMeshById: (jobId: string) => {
                    const n = getNames();
                    return this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId)?.mesh;
                },
                getTypeNaturalMesh: () => {
                    const n = getNames();
                    const s = getSpecials();
                    return this.meshEntriesFromEntityGroup(n.types).find(e => e.id === String(s.TYPE_Natural))?.mesh;
                },
                getEngineMesh: () => {
                    const s = getSpecials();
                    return this.meshEntriesFromEntityGroup(s.JOB_Engine)[0]?.mesh;
                },
            }
        );

        // Setup timeline update handler for real-time visualization
        this.eventNormalizer.onTimelineUpdate((timeline, sfs) => {
            try {
                const steps = sfs?.statelessStrategy?.steps?.length ?? 0;
                console.log('[CosmosSpace] onTimelineUpdate:', 'segments=', timeline?.segments?.length ?? 0, 'durationMs=', timeline?.totalDurationMs ?? 0, 'steps=', steps, 'runnerExists=', !!this._timelineRunner);
            } catch { /* ignore */ }
            if (!this._timelineRunner) {
                // First event: Create runner and start visualization
                this.startTimelineExecution(timeline, sfs);
            } else {
                // Subsequent events: Update timeline while runner is playing
                this._timelineRunner.updateTimeline(timeline);
            }
        });

        // Subscribe to realtime graph events from StrategyBuilder via CustomEvent
        if (typeof window !== 'undefined') {
            const handler = (e: Event) => {
                const detail = (e as CustomEvent).detail;
                try {
                    // Normalize event and pass to event normalizer
                    const normalized = StrategyEventNormalizer.normalizeEvent(detail);
                    // Rename typeLabel to type to match processLiveEvent signature
                    this.eventNormalizer?.processLiveEvent({ type: normalized.typeLabel, label: normalized.label, payload: normalized.payload });
                } catch (err) {
                    // Swallow errors to avoid breaking render loop
                    console.error('[CosmosSpace] Event processing error:', err);
                }
            };
            window.addEventListener('toolproof:graphEvent', handler as EventListener);

            // Clean up on dispose
            const prevDispose = this.dispose.bind(this);
            this.dispose = () => {
                try { window.removeEventListener('toolproof:graphEvent', handler as EventListener); } catch { }
                prevDispose();
            };

            // If no statefulStrategy is preloaded, load from window or hardcoded
            if (!this.statefulStrategy) {
                try {
                    const liveSpec = (window as unknown as { latestStatefulStrategy?: StatefulStrategyJson })?.latestStatefulStrategy;
                    if (liveSpec) {
                        this.statefulStrategy = liveSpec;
                        this.eventNormalizer?.loadRecordedStrategy(liveSpec);
                    } else {
                        // Load hardcoded strategy for demo
                        this.loadHardcodedStrategy();
                    }
                } catch {
                    // Fallback: load hardcoded strategy
                    this.loadHardcodedStrategy();
                }
            }
        }
    }

    /**
     * Timeline specs sometimes reference the Job Resource envelope id (RESOURCE-...).
     * The visualization/interaction uses the domain Job identity (JOB-...) from extractedData.
     * This maps timeline job ids to the JOB-* identity when possible.
     */
    private mapTimelineJobIdToJobIdentity(jobId: string): string {
        const jid = String(jobId ?? '');
        if (!jid) return jid;
        if (jid.startsWith('JOB-')) return jid;

        const TYPE_Job_ID = getSpecials().TYPE_Job;
        const items = (this.resourceMap?.[TYPE_Job_ID] ?? []) as Resource_JobJson[];
        // Timeline segments commonly use the Job *resource envelope* id (RESOURCE-*).
        // The envelope id field is typically `id`, not `identity`.
        const found = items.find(it => {
            const anyIt = it as unknown as { id?: unknown; identity?: unknown };
            const envelopeId = String(anyIt.id ?? anyIt.identity ?? '');
            return envelopeId === jid;
        });
        const extracted = found?.extractedData as JobJson | undefined;
        const mapped = String(extracted?.identity ?? '');
        return mapped || jid;
    }

    /**
     * Start timeline execution with the given timeline and strategy spec
     * This is the unified entry point for both live and recorded strategies
     */
    private startTimelineExecution(timeline: StrategyTimeline, statefulStrategy: StatefulStrategyJson): void {
        try {
            this.statefulStrategy = statefulStrategy;

            try {
                const stepCount = statefulStrategy?.statelessStrategy?.steps?.length ?? 0;
                console.log('[CosmosSpace.startTimelineExecution] timeline segments:', timeline?.segments?.length ?? 0, 'durationMs:', timeline?.totalDurationMs ?? 0, 'steps:', stepCount, 'paused:', this._animationPaused);
            } catch { /* ignore */ }

            // Set strategy spec on state manager for resource lookups
            this.stateManager?.setStatefulStrategy(statefulStrategy);

            // Pre-populate execution cache for HUD
            try { this.populateStepExecutionCache(); } catch { }

            // Get job sequence from timeline
            const seq = timeline.segments.map(s => this.mapTimelineJobIdToJobIdentity(s.jobId));
            if (seq.length > 0) {
                this.strategyAnimation?.enableTimelineMode(seq);
            }

            // Pre-rotate wheel to align first job if needed
            if (seq.length > 0 && this._engineJobsWheelGroup) {
                this.preRotateWheelToFirstJob(seq[0]);
            }

            // Create timeline runner
            this._timelineRunner = new TimelineRunner(timeline, { loop: false, loopBreakMs: 3000 });

            try {
                console.log('[CosmosSpace.startTimelineExecution] TimelineRunner created');
            } catch { /* ignore */ }

            // Handle phase transitions
            this._timelineRunner.onPhaseChange((prev, next) => {
                this._currentJobIndex = next.jobIndex;

                // Detect loop restart
                if (prev && next && prev.index > next.index) {
                    this.resetStrategyState();
                }
            });

            // Handle timeline ticks
            this._timelineRunner.onTick(({ segment, t }) => {
                try {
                    const clampedT = (segment.phase === 'PULLING_IN' && this.strategyAnimation?.isWheelRotating()) ? 0 : t;
                    const jobIdentity = this.mapTimelineJobIdToJobIdentity(segment.jobId);
                    this.strategyAnimation?.applyTimelineTick(jobIdentity, segment.phase, clampedT);

                    // Detect completion
                    if (segment.phase === 'PULLING_OUT' && t >= 0.99) {
                        const timelineObj = (this._timelineRunner as unknown as { timeline: { segments: TimelineSegment[] } }).timeline;
                        if (timelineObj?.segments) {
                            const lastSegment = timelineObj.segments[timelineObj.segments.length - 1];
                            if (segment.index === lastSegment.index) {
                                this.resetStrategyState();
                                this.strategyAnimation?.stop();
                                this._animationHasStarted = false;
                            }
                        }
                    }
                } catch { }
            });

            // Start timeline if not paused
            if (!this._animationPaused) {
                try {
                    console.log('[CosmosSpace.startTimelineExecution] Starting TimelineRunner now');
                } catch { /* ignore */ }
                this._timelineRunner.start();
            } else {
                try {
                    console.log('[CosmosSpace.startTimelineExecution] Not starting TimelineRunner because paused=true');
                } catch { /* ignore */ }
            }
        } catch (err) {
            console.error('[CosmosSpace] Error starting timeline execution:', err);
        }
    }

    /**
     * Load hardcoded strategy for demo purposes
     */
    private loadHardcodedStrategy(): void {
        import('./hardcoded/strategyExecution.json').then((mod) => {
            try {
                const execution = (mod as unknown as { default: { session: { statefulStrategy?: StatefulStrategyJson } } }).default;
                const sfs = execution?.session?.statefulStrategy as StatefulStrategyJson | undefined;
                if (sfs) {
                    this.statefulStrategy = sfs;
                    this.eventNormalizer?.loadRecordedStrategy(sfs);
                }
            } catch { /* ignore */ }
        }).catch(() => { /* ignore */ });
    }

    /**
     * Pre-rotate the wheel to align the first job to TYPE_Natural position
     */
    private preRotateWheelToFirstJob(firstJobId: string): void {
        try {
            const n = getNames();
            const s = getSpecials();
            const firstJobEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === firstJobId);
            const intMesh = this.meshEntriesFromEntityGroup(n.types).find(e => e.id === String(s.TYPE_Natural))?.mesh;
            const engineEntry = this.meshEntriesFromEntityGroup(s.JOB_Engine)[0];

            if (firstJobEntry?.mesh && intMesh && engineEntry?.mesh && this._engineJobsWheelGroup) {
                const jobOriginalPos = this.strategyAnimation?.getJobOriginalPosition(firstJobEntry.mesh);
                if (jobOriginalPos) {
                    const savedPosition = firstJobEntry.mesh.position.clone();
                    firstJobEntry.mesh.position.copy(jobOriginalPos);
                    firstJobEntry.mesh.updateMatrixWorld(true);

                    const enginePos = new THREE.Vector3(); engineEntry.mesh.getWorldPosition(enginePos);
                    const jobPos = new THREE.Vector3(); firstJobEntry.mesh.getWorldPosition(jobPos);
                    const intPos = new THREE.Vector3(); intMesh.getWorldPosition(intPos);

                    const jobVec = jobPos.clone().sub(enginePos); jobVec.y = 0;
                    const intVec = intPos.clone().sub(enginePos); intVec.y = 0;

                    if (jobVec.lengthSq() > 1e-6 && intVec.lengthSq() > 1e-6) {
                        const jobAngle = Math.atan2(jobVec.z, jobVec.x);
                        const intAngle = Math.atan2(intVec.z, intVec.x);
                        let delta = intAngle - jobAngle;
                        while (delta > Math.PI) delta -= 2 * Math.PI;
                        while (delta < -Math.PI) delta += 2 * Math.PI;
                        this._engineJobsWheelGroup.rotation.y -= delta;
                    }

                    firstJobEntry.mesh.position.copy(savedPosition);
                }
            }
        } catch { /* ignore pre-rotation errors */ }
    }

    /**
     * Reset stragegyState (called on loop restart or completion)
     */
    private resetStrategyState(): void {
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-') ||
                child.name === 'job-role-connectors' ||
                child.name === getNames().roles ||
                child.name === `${getNames().roles}-ring-guide`) {
                toRemove.push(child);
            }
            if (child.userData?.id?.startsWith('TYPE-Natural/mock-') ||
                (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) ||
                child.userData?.isConnector) {
                toRemove.push(child);
            }
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) {
                toRemove.push(child);
            }
        });
        toRemove.forEach(o => o.parent?.remove(o));
        // Clear existing roles from entityMeshMap
        const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
            const mesh = this.entityMeshMap[key];
            return mesh?.userData?.entity === getNames().roles;
        });
        rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
        this.clearPreviousOutputHighlights();
        this._previousStepOutputs = {};
        this._hudShowOutputForJob.clear();
        try { this.clearActiveStepHUD(); } catch { }
        try { this.drawNaturals(); } catch { }
    }

    // Compute job order from statefulStrategy steps
    private getJobOrderFromStatefulStrategy(): string[] {
        const sfs = this.statefulStrategy;
        if (!sfs?.statelessStrategy?.steps) return [];
        const order: string[] = [];
        for (const step of sfs.statelessStrategy.steps) {
            const exec = (step as unknown as { execution?: { jobId?: string } }).execution;
            const jid = exec?.jobId ? String(exec.jobId) : undefined;
            if (jid) order.push(jid);
        }
        return order;
    }

    // Resolve resource panel mesh by resourceId by scanning the materialized strategy state (strategyState)
    private getResourceMeshById(resourceId: string): THREE.Mesh | undefined {
        // console.log(`[getResourcePanelMeshByResourceId] Looking up: ${resourceId}`);
        const sfs = this.statefulStrategy;
        const ss = sfs as unknown as { strategyState?: Record<string, Record<string, unknown>> } | null;
        const strategyState = ss?.strategyState;
        if (!strategyState || Object.keys(strategyState).length === 0) {
            // console.log(`[getResourcePanelMeshByResourceId] NO statefulStrategy.strategyState!`);
            return undefined;
        }
        // console.log(`[getResourcePanelMeshByResourceId] strategyState executions: ${Object.keys(strategyState).length}`);
        let path: string | undefined;
        let naturalIdentity: number | undefined;
        let found = false;
        try {
            for (const v of Object.values(strategyState)) {
                if (v && typeof v === 'object') {
                    const roleMap = v as Record<string, unknown>;
                    for (const rm of Object.values(roleMap)) {
                        const r = rm as { id?: unknown; identity?: unknown; path?: string; extractedData?: { identity?: unknown } };
                        const rid = typeof r.id === 'string' ? r.id : (typeof r.identity === 'string' ? r.identity : undefined);
                        if (rid === resourceId) {
                            if (typeof r.path === 'string' && r.path.length > 0) {
                                path = r.path;
                            }
                            // Capture naturalidentity for mock panel lookup fallback
                            if (typeof r.extractedData?.identity === 'number') {
                                // console.log(`[getResourcePanelMeshByResourceId] Found naturalIdentity=${r.extractedData.identity} for ${resourceId}`);
                                naturalIdentity = r.extractedData.identity;
                            }
                            found = true;
                            break;
                        }
                    }
                }
                if (found) break;
            }
        } catch { /* best-effort scan */ }

        const n = getNames();

        // For naturals, prioritize naturalIdentity over path (path might not match mock panel IDs)
        // If we have an naturalidentity, attempt to map to mock panel id FIRST
        if (typeof naturalIdentity === 'number') {
            // console.log(`[getResourcePanelMeshByResourceId] Has naturalIdentity=${naturalIdentity}, checking panel map...`);
            const mockId = `TYPE-Natural/mock-${naturalIdentity}`;
            const resourcesGroupForIdentity = this.root.getObjectByName(n.resources) as THREE.Group;
            if (resourcesGroupForIdentity) {
                for (const child of resourcesGroupForIdentity.children) {
                    if (child instanceof THREE.Mesh && child.userData?.id === mockId) {
                        // console.log(`[getResourcePanelMeshByResourceId] Found via scene search for mockId=${mockId}`);
                        return child;
                    }
                }
            }
            // Direct lookup via panel map (more robust)
            const directPanel = this.stateManager?.getMockPanel(naturalIdentity);
            // console.log(`[getResourcePanelMeshByResourceId] Panel map has ${naturalIdentity}? ${!!directPanel}`);
            if (directPanel) return directPanel;
        }

        // ATTENTION
        // If we found a path and haven't returned yet, try it (for non-natural resources)
        if (path) {
            const meshByPath = this.getMeshById(n.resources, path);
            if (meshByPath) return meshByPath;
        }

        // Otherwise, check if this is a mock panel ID (e.g., "TYPE-Natural/mock-1")
        // Search the resources group directly by userData.id
        const resourcesGroup = this.root.getObjectByName(n.resources) as THREE.Group;
        if (resourcesGroup) {
            for (const child of resourcesGroup.children) {
                if (child instanceof THREE.Mesh && child.userData?.id === resourceId) {
                    return child;
                }
            }
        }

        // Final attempt: if resourceId itself is a real id but indexed to an naturalIdentity, use panel map
        const indexedInt = this.stateManager?.getNaturalIdentity(resourceId);
        // console.log(`[getResourcePanelMeshByResourceId] Indexed int for ${resourceId}: ${indexedInt}`);
        if (typeof indexedInt === 'number') {
            const panel = this.stateManager?.getMockPanel(indexedInt);
            // console.log(`[getResourcePanelMeshByResourceId] Panel from map for ${indexedInt}:`, !!panel);
            if (panel) return panel;
        }

        // console.log(`[getResourcePanelMeshByResourceId] NOT FOUND for ${resourceId}`);
        return undefined;
    }

    // Highlight resource panels that were outputs from previous steps
    private highlightPreviousOutputResources(outputBindingMap: Record<string, string>): void {
        for (const resourceId of Object.values(outputBindingMap || {})) {
            const resMesh = this.getResourceMeshById(resourceId);
            if (!resMesh) continue;

            // Store original emissive color if not already stored
            if (!resMesh.userData.__originalEmissive) {
                const mat = resMesh.material as THREE.MeshStandardMaterial;
                resMesh.userData.__originalEmissive = mat.emissive.clone();
                resMesh.userData.__originalEmissiveIntensity = mat.emissiveIntensity ?? 1;
            }

            // Apply faded highlight (reduced opacity red glow)
            const mat = resMesh.material as THREE.MeshStandardMaterial;
            mat.emissive.set(0xcc0033); // Red color matching output connectors
            mat.emissiveIntensity = 0.3; // Subtle glow
            resMesh.userData.__previousOutput = true;
        }
    }

    // Clear all previous output highlights from resource panels
    private clearPreviousOutputHighlights(): void {
        const n = getNames();
        this.root.traverse(child => {
            if (child.userData?.__previousOutput && child instanceof THREE.Mesh) {
                const mat = child.material as THREE.MeshStandardMaterial;
                if (child.userData.__originalEmissive) {
                    mat.emissive.copy(child.userData.__originalEmissive);
                    mat.emissiveIntensity = child.userData.__originalEmissiveIntensity ?? 1;
                    delete child.userData.__originalEmissive;
                    delete child.userData.__originalEmissiveIntensity;
                }
                delete child.userData.__previousOutput;
            }
        });
    }

    // Create role-resource INPUT connectors from roles to bound naturalresources (returns array for animation system)
    private createRoleResourceInputConnectors(
        inputBindingMap: Record<string, string>,
        initiallyHidden: boolean
    ): THREE.Line[] {
        const lines: THREE.Line[] = [];

        const makeCurvedLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            // Create a quadratic Bézier curve that arcs upward
            const distance = from.distanceTo(to);
            const midpoint = new THREE.Vector3().addVectors(from, to).multiplyScalar(0.5);
            // Lift the control point upward (Y axis) to create an arc
            const controlPoint = midpoint.clone();
            controlPoint.y += distance * 0.3; // Arc height is 30% of horizontal distance

            // Generate curve points
            const curve = new THREE.QuadraticBezierCurve3(from, controlPoint, to);
            const points = curve.getPoints(32); // 32 segments for smooth curve

            const geom = new THREE.BufferGeometry().setFromPoints(points);
            const mat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.95, depthWrite: false, depthTest: false });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10001;
            line.frustumCulled = false;
            line.visible = !initiallyHidden;
            return line;
        };

        // Inputs: connect role down to bound resource panel (green)
        for (const [resourceRoleId, resourceId] of Object.entries(inputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[resourceRoleId];
            const resMesh = this.getResourceMeshById(resourceId);
            // console.log(`[createRoleResourceInputConnectors] resourceRoleId=${resourceRoleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue;
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            // Connect to center of resource panel instead of edge
            const toPt = resCenter;
            const line = makeCurvedLine(fromPt, toPt, 0x00cc66);
            // Store resource mesh reference for highlighting
            line.userData.resourceMesh = resMesh;
            lines.push(line);
        }

        return lines;
    }

    // Create role-resource OUTPUT connectors from roles to bound naturalresources (returns array for animation system)
    private createRoleResourceOutputConnectors(
        outputBindingMap: Record<string, string>,
        initiallyHidden: boolean
    ): THREE.Line[] {
        const lines: THREE.Line[] = [];

        const makeCurvedLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            // Create a quadratic Bézier curve that arcs downward for outputs
            const distance = from.distanceTo(to);
            const midpoint = new THREE.Vector3().addVectors(from, to).multiplyScalar(0.5);
            // Push the control point downward (Y axis) to create a downward arc
            const controlPoint = midpoint.clone();
            controlPoint.y -= distance * 0.3; // Arc depth is 30% of horizontal distance

            // Generate curve points
            const curve = new THREE.QuadraticBezierCurve3(from, controlPoint, to);
            const points = curve.getPoints(32); // 32 segments for smooth curve

            const geom = new THREE.BufferGeometry().setFromPoints(points);
            const mat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.95, depthWrite: false, depthTest: false });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10001;
            line.frustumCulled = false;
            line.visible = !initiallyHidden;
            return line;
        };

        // Outputs: connect role up to bound resource panel (red)
        for (const [resourceRoleId, resourceId] of Object.entries(outputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[resourceRoleId];
            const resMesh = this.getResourceMeshById(resourceId);
            // console.log(`[createRoleResourceOutputConnectors] resourceRoleId=${resourceRoleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue;
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            // Connect to center of resource panel instead of edge
            const toPt = resCenter;
            const line = makeCurvedLine(fromPt, toPt, 0xcc0033);
            // Mark output connectors so we can identify them later for opacity fade
            line.userData.isOutput = true;
            line.userData.resourceId = resourceId;
            // Store resource mesh reference for highlighting
            line.userData.resourceMesh = resMesh;
            lines.push(line);
        }

        return lines;
    }

    // Store step execution info for later HUD retrieval
    private storeStepExecutionInfo(jobId: string, executionId: string, stepIndex: number, totalSteps: number, inputBindingMap: Record<string, string>, outputBindingMap: Record<string, string>): void {
        this.stepExecutionCache.set(executionId, { jobId, executionId, stepIndex, totalSteps, inputBindingMap, outputBindingMap });
    }

    // Retrieve stored execution info for a job (used by HUD callbacks)
    private getExecutionInfoForJob(jobId: string): { jobId: string; executionId: string; stepIndex: number; totalSteps: number; inputBindingMap: Record<string, string>; outputBindingMap: Record<string, string> } | null {
        // Use jobIndex to find the correct execution for this occurrence of the job
        if (this._currentJobIndex >= 0) {
            const sfs = this.statefulStrategy;
            if (sfs?.statelessStrategy?.steps && this._currentJobIndex < sfs.statelessStrategy.steps.length) {
                const step = sfs.statelessStrategy.steps[this._currentJobIndex];
                const exec = (step as unknown as { execution?: { id?: string } }).execution;
                if (exec?.id) {
                    const cached = this.stepExecutionCache.get(String(exec.id));
                    if (cached) {
                        if (this._debug.hud) {
                            console.log(`[HUD] Found execution for jobId=${jobId} at jobIndex=${this._currentJobIndex}:`, cached);
                        }
                        return cached;
                    }
                }
            }
        }
        // Fallback: look up cached execution by jobId (first match)
        for (const cached of this.stepExecutionCache.values()) {
            if (cached.jobId === jobId) {
                if (this._debug.hud) {
                    console.log(`[HUD] Found execution for jobId=${jobId}:`, cached);
                }
                return cached;
            }
        }
        if (this._debug.hud) {
            console.log(`[HUD] No execution info found for jobId=${jobId}`);
        }
        return null;
    }

    // Pre-populate execution cache from statefulStrategy at graph_start
    private populateStepExecutionCache(): void {
        const sfs = this.statefulStrategy;
        if (!sfs?.statelessStrategy?.steps) return;

        const totalSteps = sfs.statelessStrategy.steps.length;
        sfs.statelessStrategy.steps.forEach((step, index) => {
            const exec = (step as unknown as { execution?: { id?: string; jobId?: string; roleBindings?: { inputBindingMap?: Record<string, string>; outputBindingMap?: Record<string, string> } } }).execution;
            if (!exec?.id || !exec?.jobId) return;

            const executionId = String(exec.id);
            const jobId = String(exec.jobId);
            const inputBindingMap = (exec.roleBindings?.inputBindingMap || {}) as Record<string, string>;
            const outputBindingMap = (exec.roleBindings?.outputBindingMap || {}) as Record<string, string>;

            // Store with 1-based stepIndex for HUD display
            this.storeStepExecutionInfo(jobId, executionId, index + 1, totalSteps, inputBindingMap, outputBindingMap);
        });
    }

    // Calculator HUD: show active job operation (e.g., 1 + 1 = 2)
    private hudGroupName = 'hud-active-step';
    private hudGroup: THREE.Group | null = null;
    private hudSprite: THREE.Sprite | null = null;

    private clearActiveStepHUD(): void {
        // Show 'Calculator' label when not displaying an operation
        if (this.hudSprite) {
            this.updateTextSpriteContent(this.hudSprite, 'Calculator');
        }
    }

    private initializeHUD(): void {
        // Check if HUD is enabled in config
        const hudEnabled = this.explorerConfig.space.hud?.isVisible ?? true;
        if (!hudEnabled) return;

        // Create calculator HUD group if it doesn't exist
        if (!this.hudGroup) {
            this.hudGroup = new THREE.Group();
            this.hudGroup.name = this.hudGroupName;
            this.root.add(this.hudGroup);
        }

        // Create calculator HUD sprite if it doesn't exist
        if (!this.hudSprite) {
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 512;
            const texture = new THREE.CanvasTexture(canvas);
            const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
            this.hudSprite = new THREE.Sprite(material);
            this.hudSprite.renderOrder = 10002;
            this.hudSprite.scale.set(1.5, 1.5, 1);
            this.hudSprite.position.set(600, 30, 0);
            this.hudGroup.add(this.hudSprite);
            // Initialize with 'Calculator' label
            this.updateTextSpriteContent(this.hudSprite, 'Calculator');
        }
    }

    private updateActiveStepHUD(
        jobId: string,
        executionId: string,
        current: number,
        total: number,
        inputBindingMap: Record<string, string>,
        outputBindingMap: Record<string, string>
    ): void {
        // Check if HUD is enabled in config
        const hudEnabled = this.explorerConfig.space.hud?.isVisible ?? true;
        if (!hudEnabled) {
            // HUD is disabled, skip rendering
            return;
        }

        if (this._debug.hud) {
            console.log(`[HUD] updateActiveStepHUD called: jobId=${jobId}, step=${current}/${total}`);
        }

        // Ensure HUD is initialized (should already be done in drawScene, but double-check)
        if (!this.hudSprite) {
            this.initializeHUD();
            if (!this.hudSprite) return; // Still null, bail out
        }

        // Get job operation from the job mesh/data
        const getJobOperation = (jobId: string): string => {
            const jobMesh = this.meshEntriesFromEntityGroup(getNames().jobs).find(e => e.id === jobId);
            const jobName = jobMesh?.mesh.userData?.name;
            if (typeof jobName === 'string') {
                // Map job identity to operation symbol
                if (jobName.toLowerCase().includes('add') || jobName.toLowerCase().includes('+')) return '+';
                if (jobName.toLowerCase().includes('multiply') || jobName.toLowerCase().includes('*')) return '*';
                if (jobName.toLowerCase().includes('subtract') || jobName.toLowerCase().includes('-')) return '-';
                if (jobName.toLowerCase().includes('divide') || jobName.toLowerCase().includes('/')) return '/';
            }
            return '+'; // default fallback
        };

        // Get resource identity value
        const getResourceIdentity = (resId: string): string | null => {
            const resMesh = this.getResourceMeshById(resId);
            return resMesh?.userData?.name ?? null;
        };

        // Collect input values
        const inputValues: string[] = [];
        for (const [resourceRoleId, resId] of Object.entries(inputBindingMap || {})) {
            const identity = getResourceIdentity(resId);
            if (identity !== null) {
                inputValues.push(identity);
            }
        }

        // Get operation symbol from job
        const operation = getJobOperation(jobId);

        // Format operation string
        let operationText = '';
        if (inputValues.length >= 2) {
            operationText = `${inputValues[0]} ${operation} ${inputValues[1]}`;

            // Only show output if this job has reached the output connector phase
            if (this._hudShowOutputForJob.has(jobId)) {
                const outputValues: string[] = [];
                for (const [resourceRoleId, resId] of Object.entries(outputBindingMap || {})) {
                    const identity = getResourceIdentity(resId);
                    if (identity !== null) {
                        outputValues.push(identity);
                    }
                }
                if (outputValues.length > 0) {
                    operationText += ` = ${outputValues[0]}`;
                }
            }
        }

        if (this._debug.hud) {
            console.log(`[HUD] Operation text: "${operationText}"`);
        }
        // Update the existing sprite's texture with operation text
        this.updateTextSpriteContent(this.hudSprite, operationText);
    }

    // Update an existing text sprite's content without recreating it
    private updateTextSpriteContent(sprite: THREE.Sprite, text: string): void {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        const padding = 20;
        const fontSize = 72;
        const lineGap = 6;
        const lines = text ? text.split('\n') : [];

        // Use fixed dimensions to prevent size changes
        const fixedWidth = 500;
        const fixedHeight = 180;
        canvas.width = fixedWidth;
        canvas.height = fixedHeight;
        ctx.font = `${fontSize}px sans-serif`;

        // Draw rounded rectangle background with white border (matching explanation HUD style)
        const radius = 20;
        // Fill background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(0, 0, fixedWidth, fixedHeight, radius);
            ctx.fill();
        } else {
            // Fallback for older browsers
            ctx.beginPath();
            ctx.moveTo(radius, 0);
            ctx.arcTo(fixedWidth, 0, fixedWidth, fixedHeight, radius);
            ctx.arcTo(fixedWidth, fixedHeight, 0, fixedHeight, radius);
            ctx.arcTo(0, fixedHeight, 0, 0, radius);
            ctx.arcTo(0, 0, fixedWidth, 0, radius);
            ctx.closePath();
            ctx.fill();
        }

        // Draw white border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 4;
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(2, 2, fixedWidth - 4, fixedHeight - 4, radius);
            ctx.stroke();
        } else {
            // Fallback for older browsers
            ctx.beginPath();
            ctx.moveTo(radius, 2);
            ctx.arcTo(fixedWidth - 2, 2, fixedWidth - 2, fixedHeight - 2, radius);
            ctx.arcTo(fixedWidth - 2, fixedHeight - 2, 2, fixedHeight - 2, radius);
            ctx.arcTo(2, fixedHeight - 2, 2, 2, radius);
            ctx.arcTo(2, 2, fixedWidth - 2, 2, radius);
            ctx.closePath();
            ctx.stroke();
        }

        // Always draw text (including 'Calculator' label)
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        // Center text vertically in the canvas
        const totalTextHeight = lines.length * fontSize + (lines.length - 1) * lineGap;
        let y = (fixedHeight - totalTextHeight) / 2 + fontSize / 2;
        for (const line of lines) {
            ctx.fillText(line, fixedWidth / 2, y);
            y += fontSize + lineGap;
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;
        if (sprite.material.map) sprite.material.map.dispose();
        sprite.material.map = texture;
        sprite.material.needsUpdate = true;
        const aspect = fixedWidth / fixedHeight;
        const scale = 18;
        sprite.scale.set(scale * aspect, scale, 1);
    }

    public updateData(payload: Partial<CosmosSpaceData>): void {
        try {

            let changed = false;
            const resourceMap = payload.resourceMap as ResourceMap | undefined;
            if (resourceMap && resourceMap !== this.resourceMap) {
                this.resourceMap = resourceMap;
                changed = true;
            }

            // console.log('[CosmosSpace] resourceMap: ', JSON.stringify(this.resourceMap, null, 2));
            // console.log('[CosmosSpace] statefulStrategy: ', JSON.stringify(this.statefulStrategy, null, 2));

            if (!changed) return; // Nothing to do

            // Check if animation should be restarted after redraw
            const shouldRestartAnimation = this.strategyAnimation?.isActive() ?? false;

            // Stop animation before redrawing scene
            this.strategyAnimation?.stop();

            // Re-render scene when new data arrives
            this.drawScene();

            // Retry any pending role draw if previously deferred due to missing data
            if (this.pendingRolesJobId && this.pendingRolesJobId !== this.lastRolesJobId) {
                const drew = this.drawRoles(this.pendingRolesJobId, { animation: false });
                if (drew) {
                    this.lastRolesJobId = this.pendingRolesJobId;
                    this.pendingRolesJobId = null;
                }
            }

            // Restart animation after scene is ready if it was running before
            if (shouldRestartAnimation && this.strategyAnimation) {
                this.strategyAnimation.start();
            }
        } catch (e) {
            console.error('Error in updateData/drawScene:', e);
            // Re-throw to see the actual error instead of silently ignoring
            throw e;
        }
    }

    // Toggle animation pause state and return new paused state
    public toggleAnimationPause(): boolean {
        try {
            console.log('[CosmosSpace.toggleAnimationPause] invoked. paused:', this._animationPaused, 'hasStarted:', this._animationHasStarted, 'runner:', !!this._timelineRunner, 'runnerRunning:', this._timelineRunner?.isRunning?.(), 'runnerPaused:', this._timelineRunner?.isPaused?.());
        } catch { /* ignore */ }
        if (this._animationPaused) {
            // Resume: clear pause flag and resume timeline/simulator
            this._animationPaused = false;
            this._animationHasStarted = true; // Mark that animation has been started

            // Clear interaction state when starting/resuming animation
            console.log('[CosmosSpace.toggleAnimationPause] Clearing interaction before start/resume');
            try {
                if (this.interactionView?.clearInteraction) {
                    this.interactionView.clearInteraction();
                    console.log('[CosmosSpace.toggleAnimationPause] clearInteraction called successfully');
                }
            } catch (e) {
                console.error('[CosmosSpace.toggleAnimationPause] Error calling clearInteraction:', e);
            }
            // Reset internal tracking state
            this.lastRolesJobId = null;
            this.pendingRolesJobId = null;

            // DON'T restrict interaction filter here - it will be restricted in updateOnHover when isRunning becomes true
            // This allows jobs to remain clickable until animation actually starts running
            try {
                if (this._timelineRunner) {
                    if (this._timelineRunner.isPaused()) {
                        try { console.log('[CosmosSpace.toggleAnimationPause] Runner is paused -> resume()'); } catch { }
                        this._timelineRunner.resume();
                    } else {
                        // Start timeline runner if it was never started (initial paused state)
                        try { console.log('[CosmosSpace.toggleAnimationPause] Runner exists -> start()'); } catch { }
                        this._timelineRunner.start();
                    }
                } else {
                    try {
                        console.warn('[CosmosSpace.toggleAnimationPause] No TimelineRunner yet. Waiting for timeline update (graph_start / recorded strategy load).');
                    } catch { /* ignore */ }
                }
            } catch { }
            // Animation is driven by timeline runner
            this._animationPaused = false;
            return false;
        } else {
            // Pause: set flag and pause timeline runner
            this._animationPaused = true;
            // Restore full interaction filter to allow hover text on all entities while paused
            // (but __animationControlled flags keep animation highlights intact)
            this.updateInteractorEntityFilter();
            try {
                if (this._timelineRunner) {
                    console.log('[CosmosSpace.toggleAnimationPause] Pausing TimelineRunner');
                    this._timelineRunner.pause();
                }
            } catch { }
            return true;
        }
    }

    // Public, lightweight updater: redraw role rings based on current hover job.
    // Clears prior role rings only when hover target changes.
    public override updateOnHover(): void {
        // Per-frame log muted
        const isRunning = this.strategyAnimation?.isActive() && !this._animationPaused;

        // Manage interaction filter based on state
        if (this._animationHasStarted) {
            // When animation has been started (running or paused), restrict to only types/roles/resources (exclude jobs/engine)
            this.setInteractionEntityFilter(new Set([getNames().types, getNames().roles, getNames().resources]));
        } else {
            // When not started, allow all entities to be interactable
            const n = getNames();
            const jobsExist = !!this.root.getObjectByName(n.jobs);
            if (jobsExist) {
                this.updateInteractorEntityFilter();
            }
        }

        // Don't process job hover/selection if animation has been started (running or paused)
        // Roles can still be hovered via the interaction filter
        if (this._animationHasStarted) {
            // Animation is active or paused - skip job-specific hover processing
            return;
        }

        // Disable sticky highlighter behavior (side panel for job selection)
        // Update sticky highlighter with current selection, but disabled for now
        // try {
        //     const selected = this.getSelectedObject();
        //     this.stickyHighlighter?.onSelection(selected);
        //     this.stickyHighlighter?.tick();
        // } catch { /* ignore */ }

        // Determine current hovered job id
        const hovered = this.gethoveredObject();
        const selected = this.getSelectedObject();
        // console.log('[CosmosSpace.updateOnHover] Hovered object:', hovered?.name, hovered?.userData);
        // console.log('[CosmosSpace.updateOnHover] Selected object:', selected?.name, selected?.userData);
        const jobGroup = getNames().jobs;
        let hoveredJobId: string | null = null;
        let selectedJobId: string | null = null;
        let cursor: THREE.Object3D | null = hovered;

        // Find hovered job
        while (cursor) {
            const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
            if (ud?.entity === jobGroup && typeof ud.id === 'string') { hoveredJobId = ud.id; break; }
            cursor = cursor.parent;
        }

        // Find selected (clicked) job
        cursor = selected;
        while (cursor) {
            const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
            if (ud?.entity === jobGroup && typeof ud.id === 'string') { selectedJobId = ud.id; break; }
            cursor = cursor.parent;
        }

        // When not running, prefer selected job; fallback to hovered job
        const jobId: string | null = isRunning ? null : (selectedJobId || hoveredJobId);

        // Per-frame log muted: Detected jobId details
        const jobChanged = jobId !== this.lastRolesJobId;
        if (jobChanged) {
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === getNames().roles ||
                    child.name === `${getNames().roles}-ring-guide` ||
                    child.name === 'job-role-connectors'
                ) toRemove.push(child);
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === getNames().roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
            if (jobId) {
                const drew = this.drawRoles(jobId, { animation: false });
                if (drew) this.lastRolesJobId = jobId;
                // Update entity filter to include roles for XR interaction
                this.updateInteractorEntityFilter();
            }
        }

        // Removed custom role hover tooltip methods; using DomInteractor tooltip instead.
    }

    // Explicit selection handler: called when user clicks an object. Prioritizes showing roles for selected job
    // only when animation has not started (no current animating job). During active or paused animation, roles
    // continue to reflect the animating job state.
    public updateOnSelection(selected: THREE.Object3D | null): void {
        try {
            const isRunning = this.strategyAnimation?.isActive() && !this._animationPaused;
            // Ignore selection only while animation actively running; paused state always allows override
            if (isRunning) return;

            if (!selected) return;
            const jobGroup = getNames().jobs;
            let cursor: THREE.Object3D | null = selected;
            let selectedJobId: string | null = null;
            while (cursor) {
                const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
                if (ud?.entity === jobGroup && typeof ud.id === 'string') { selectedJobId = ud.id; break; }
                cursor = cursor.parent;
            }
            if (!selectedJobId) return;
            // Always redraw on selection: clear previous roles if same job to ensure connectors/visual refresh
            // Remove existing roles groups unconditionally before draw
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === getNames().roles ||
                    child.name === `${getNames().roles}-ring-guide` ||
                    child.name === 'job-role-connectors'
                ) toRemove.push(child);
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === getNames().roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
            const drew = this.drawRoles(selectedJobId, { animation: false });
            if (drew) this.lastRolesJobId = selectedJobId; else this.pendingRolesJobId = selectedJobId;
        } catch { /* ignore selection errors */ }
    }

    // Per-frame update for animations
    public update(_delta: number): void {
        // Update wheel rotation animation (now owned by StrategyAnimationAnimation)
        this.strategyAnimation?.updateWheelRotation();
        // Update job processing animation
        this.strategyAnimation?.update();
        // Animate portal
        if (this.portalMesh?.userData?.animate) {
            this.portalMesh.userData.animate(_delta);
        }
    }

    // Ensure job label sprites have enough vertical room to avoid clipping
    private boostJobLabelSpriteHeights(): void {
        const n = getNames();
        const jobsGroup = this.root.getObjectByName(n.jobs) as THREE.Group;
        if (!jobsGroup) return;
        jobsGroup.traverse((child) => {
            if (child instanceof THREE.Sprite && (child.userData?.__jobLabelSprite || child.name === 'job-label')) {
                const map = (child.material as THREE.SpriteMaterial).map as THREE.CanvasTexture | null;
                if (map && map.image) {
                    // Add headroom to texture by re-rendering onto a taller canvas if needed
                    const img = map.image as HTMLCanvasElement;
                    const extraHeadroom = Math.max(24, Math.ceil(img.height * 0.4));
                    const newCanvas = document.createElement('canvas');
                    newCanvas.width = img.width;
                    newCanvas.height = img.height + extraHeadroom;
                    const ctx = newCanvas.getContext('2d');
                    if (ctx) {
                        ctx.clearRect(0, 0, newCanvas.width, newCanvas.height);
                        // Draw original centered vertically with added symmetric padding
                        const offsetY = Math.floor(extraHeadroom / 2);
                        ctx.drawImage(img, 0, offsetY);
                        const newTex = new THREE.CanvasTexture(newCanvas);
                        newTex.needsUpdate = true;
                        // dispose old texture to avoid leaks
                        try { (child.material as THREE.SpriteMaterial).map?.dispose(); } catch { }
                        (child.material as THREE.SpriteMaterial).map = newTex;
                        (child.material as THREE.SpriteMaterial).needsUpdate = true;
                        // Increase vertical scale for display headroom similar to natural labels
                        child.scale.set(child.scale.x, child.scale.y + 4, 1);
                    }
                } else {
                    // Fallback: just bump vertical scale
                    child.scale.set(child.scale.x, child.scale.y + 4, 1);
                }
            }
        });
    }

    drawScene() {
        // console.log('=== Starting drawScene ===');
        const removable = buildRemovableSet(this.explorerConfig.space);
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (removable.has(child.name)) {
                toRemove.push(child);
            }
        });
        toRemove.forEach(o => o.parent?.remove(o));
        this.entityMeshMap = {} as EntityMeshMap;
        // Clear wheel group reference so it gets recreated
        this._engineJobsWheelGroup = null;
        // console.log('Cleared entityMeshMap, about to draw components...');
        // this.drawFormats();
        try {
            this.drawEngine();
            // console.log('Finished drawEngine');
        } catch (e) {
            console.error('Error in drawEngine:', e);
        }

        try {
            this.drawJobs();
            // console.log('Finished drawJobs');
        } catch (e) {
            console.error('Error in drawJobs:', e);
        }

        try {
            this.drawTypes();
            // console.log('Finished drawTypes');
        } catch (e) {
            console.error('Error in drawTypes:', e);
        }

        try {
            // this.drawRoles();
            // console.log('Finished drawRoles');
        } catch (e) {
            console.error('Error in drawRoles:', e);
        }

        try {
            this.drawNaturals();
            // console.log('Finished drawNaturals');
        } catch (e) {
            console.error('Error in drawNaturals:', e);
        }
        // console.log('About to call updateInteractorEntityFilter...');
        this.updateInteractorEntityFilter();
        try { this.initializeHUD(); } catch { /* ignore HUD init errors */ }
        // Align animation sequence with statefulStrategy after scene draw
        try {
            const seq = this.getJobOrderFromStatefulStrategy();
            if (seq && seq.length) this.strategyAnimation?.setJobSequence(seq);
        } catch { /* ignore sequencing failures */ }

        // Create portal to Genesis space
        this.drawPortal();

        // console.log('=== Finished drawScene ===');
    }

    private updateInteractorEntityFilter() {
        // After jobs are drawn, make sure their label sprites have extra height
        try { this.boostJobLabelSpriteHeights(); } catch { }
        // Extract unique entity types from all meshes in entityMeshMap
        const entityTypes = new Set<string>();
        for (const mesh of Object.values(this.entityMeshMap)) {
            if (mesh.userData?.entity) {
                entityTypes.add(mesh.userData.entity);
            }
        }
        const groups: string[] = Array.from(entityTypes);
        const n = getNames();
        // Ensure key interaction groups are present even if entityMeshMap not yet populated
        if (this.root.getObjectByName(n.resources)) groups.push(n.resources);
        if (this.root.getObjectByName(n.jobs)) groups.push(n.jobs);
        if (this.root.getObjectByName(n.roles)) groups.push(n.roles);
        // console.log('[CosmosSpace.updateInteractorEntityFilter] Setting filter to:', groups);
        this.setInteractionEntityFilter(new Set(groups));
    }

    private meshEntriesFromEntityGroup(groupName: string): Array<{ id: string; mesh: THREE.Mesh }> {
        const res: Array<{ id: string; mesh: THREE.Mesh }> = [];
        for (const id of Object.keys(this.entityMeshMap)) {
            const mesh = this.entityMeshMap[id];
            if (mesh.userData?.entity === groupName) {
                res.push({ id, mesh });
            }
        }
        return res;
    }

    private makeMaterialFromConfig(cfg: typeof this.explorerConfig.space.panels.resources.material | typeof this.explorerConfig.space.rings.jobs.mesh.material) {
        return new THREE.MeshStandardMaterial({
            color: cfg.color,
            metalness: cfg.metalness,
            roughness: cfg.roughness,
            emissive: cfg.emissive,
            side: cfg.side,
            transparent: cfg.transparent,
            opacity: cfg.opacity,
            depthWrite: cfg.depthWrite,
            depthTest: cfg.depthTest,
        });
    }

    // Compute a point on a mesh's bounding sphere facing toward a world-space target
    private pointOnMeshBoundingSphere(mesh: THREE.Mesh, towardSpace: THREE.Vector3): THREE.Vector3 {
        const center = new THREE.Vector3();
        mesh.getWorldPosition(center);
        const dir = towardSpace.clone().sub(center);
        if (dir.lengthSq() < 1e-9) return center.clone();
        if (!mesh.geometry.boundingSphere) mesh.geometry.computeBoundingSphere();
        const r = mesh.geometry.boundingSphere?.radius ?? 0;
        return center.clone().add(dir.normalize().multiplyScalar(r));
    }

    // Compute intersection point from mesh center to its axis-aligned bounding box surface, in direction toward target.
    // Assumes mesh has no rotation relative to space (true for Engine box here).
    private pointOnMeshAABBSurface(mesh: THREE.Mesh, towardSpace: THREE.Vector3): THREE.Vector3 {
        const center = new THREE.Vector3();
        mesh.getWorldPosition(center);
        const d = towardSpace.clone().sub(center);
        if (d.lengthSq() < 1e-9) return center.clone();
        if (!mesh.geometry.boundingBox) mesh.geometry.computeBoundingBox();
        const bb = mesh.geometry.boundingBox!;
        const hx = (bb.max.x - bb.min.x) / 2;
        const hy = (bb.max.y - bb.min.y) / 2;
        const hz = (bb.max.z - bb.min.z) / 2;
        const sx = hx > 0 ? Math.abs(d.x) / hx : 0;
        const sy = hy > 0 ? Math.abs(d.y) / hy : 0;
        const sz = hz > 0 ? Math.abs(d.z) / hz : 0;
        const denom = Math.max(sx, sy, sz, 1e-9);
        const s = 1.0 / denom;
        return center.clone().add(d.multiplyScalar(s));
    }

    // Exact surface hit using raycasting from origin toward target. Returns null if no hit within segment length.
    private intersectMeshSurface(mesh: THREE.Mesh, origin: THREE.Vector3, target: THREE.Vector3): THREE.Vector3 | null {
        const dir = target.clone().sub(origin);
        const dist = dir.length();
        if (dist < 1e-9) return null;
        mesh.updateWorldMatrix(true, false);
        const raycaster = new THREE.Raycaster(origin, dir.normalize(), 0, dist + 1e-4);
        const hits = raycaster.intersectObject(mesh, true);
        if (hits && hits.length > 0) return hits[0].point.clone();
        return null;
    }

    private drawPortal(): void {
        // Remove existing portal if any
        if (this.portalMesh) {
            this.root.remove(this.portalMesh);
            this.portalMesh = null;
        }

        // Calculate portal position at eye level on the side
        const baseRadius = computeFrameworkBaseRadius(this.explorerConfig.space);
        const portalRadius = baseRadius * 0.05; // Larger portal size for visibility

        // Position portal to the side at eye level, facing center
        const portalPosition = new THREE.Vector3(baseRadius * 1.5 -450, 20, 0);

        // Rotate portal to face the center
        const portalRotation = new THREE.Euler(0, -Math.PI / 2, 0);

        this.portalMesh = createPortalMesh({
            radius: portalRadius,
            targetSpace: 'genesis',
            color: 0xff1111, 
            position: portalPosition,
            rotation: portalRotation,
            glowIntensity: 3,
        });

        this.root.add(this.portalMesh);
        console.log(`[CosmosSpace] Created portal to Genesis space at position (${portalPosition.x.toFixed(2)}, ${portalPosition.y.toFixed(2)}, ${portalPosition.z.toFixed(2)}) radius: ${portalRadius.toFixed(2)}`);
    }

    private drawEngine(): void {
        const s = getSpecials();
        const engineCfg = this.explorerConfig.space.engine;
        if (!engineCfg?.isVisible) return;

        // Create or reuse the engine-jobs wheel group
        if (!this._engineJobsWheelGroup) {
            this._engineJobsWheelGroup = new THREE.Group();
            this._engineJobsWheelGroup.name = 'engine-jobs-wheel';
            this.root.add(this._engineJobsWheelGroup);
        }

        const groupName = s.JOB_Engine;
        const group = new THREE.Group();
        group.name = groupName;
        this._engineJobsWheelGroup.add(group);

        // Geometry handling (currently supports box; extendable for sphere)
        const geomCfg: typeof engineCfg.geometry = engineCfg.geometry; // relax type to allow future extension
        let geom: THREE.BufferGeometry;
        if (geomCfg && geomCfg.kind === 'box') {
            const w = geomCfg.width ?? 1;
            const h = geomCfg.height ?? 1;
            const d = geomCfg.depth ?? 1;
            const ws = geomCfg.widthSegments ?? 1;
            const hs = geomCfg.heightSegments ?? 1;
            const ds = geomCfg.depthSegments ?? 1;
            geom = new THREE.BoxGeometry(w, h, d, ws, hs, ds);
        } else {
            throw new Error('Unsupported geometry kind for engine');
        }

        const matCfg = engineCfg.material;
        const material = new THREE.MeshStandardMaterial({
            color: matCfg.color,
            metalness: matCfg.metalness,
            roughness: matCfg.roughness,
            emissive: matCfg.emissive,
            side: matCfg.side,
            transparent: matCfg.transparent,
            opacity: matCfg.opacity,
            depthWrite: matCfg.depthWrite,
            depthTest: matCfg.depthTest,
        });

        const mesh = new THREE.Mesh(geom, material);
        mesh.position.set(0, 0, 0);
        mesh.userData = { entity: groupName, id: s.JOB_Engine, name: 'Engine' };
        group.add(mesh);

        this.entityMeshMap[s.JOB_Engine] = mesh;

        // Register engine mesh with animation system for highlighting
        this.strategyAnimation?.setEngineMesh(mesh);

        // Add fixed Engine label
        const labelCanvas = document.createElement('canvas');
        const labelCtx = labelCanvas.getContext('2d')!;
        const padding = 32;
        const fontSize = 82;
        const text = 'Engine';
        labelCtx.font = `bold ${fontSize}px sans-serif`;
        const metrics = labelCtx.measureText(text);
        const width = Math.ceil(metrics.width) + padding * 2;
        const height = Math.ceil(fontSize * 1.5) + padding * 2;
        labelCanvas.width = width;
        labelCanvas.height = height;
        labelCtx.font = `bold ${fontSize}px sans-serif`;
        labelCtx.clearRect(0, 0, width, height);
        labelCtx.shadowColor = 'rgba(0,0,0,0.3)';
        labelCtx.shadowBlur = 5;
        labelCtx.shadowOffsetX = 2;
        labelCtx.shadowOffsetY = 2;
        labelCtx.fillStyle = '#ffffff';
        labelCtx.textAlign = 'center';
        labelCtx.textBaseline = 'middle';
        labelCtx.fillText(text, width / 2, height / 2);
        labelCtx.lineWidth = 1;
        labelCtx.strokeStyle = 'rgba(0,0,0,0.7)';
        labelCtx.strokeText(text, width / 2, height / 2);
        const labelTex = new THREE.CanvasTexture(labelCanvas);
        const labelSprite = new THREE.Sprite(new THREE.SpriteMaterial({
            map: labelTex,
            transparent: true,
            depthTest: false,
            depthWrite: false
        }));
        const aspect = width / height;
        const scaleY = 5.5;
        labelSprite.scale.set(scaleY * aspect, scaleY, 1);
        labelSprite.position.set(0, 8.0, 0);
        labelSprite.renderOrder = 10002;
        group.add(labelSprite);
    }

    private drawJobs(): void {
        const n = getNames();
        const s = getSpecials();
        // Use drawRing to render Job resources in an XZ ring.
        const TYPE_Job_ID = s.TYPE_Job;
        // console.log('Drawing jobs - TYPE_Job_ID:', TYPE_Job_ID);
        // console.log('Available resource data keys:', Object.keys(this.resourceMap || {}));
        const items = (this.resourceMap?.[TYPE_Job_ID] ?? []) as Resource_JobJson[];
        console.log('Job items found:', items.length);
        if (!items.length) return;

        const groupName = n.jobs;
        // console.log('Job groupName:', groupName);
        // Project resource items into the entity shape expected by drawRing.
        const ringEntities = items.map(it => {
            const extractedData = it.extractedData as JobJson;
            // CRITICAL: job identity is on the extracted data (JOB-...), not on the resource envelope.
            // We must use extractedData.identity as the entity id so hover/selection lines up with drawRoles.
            const id = String(extractedData?.identity ?? '');
            const name = (() => {
                // JobJson has identity (string) and name (from Documented mixin)
                if (extractedData?.name) return String(extractedData.name);
                if (extractedData?.identity) return String(extractedData.identity);
                return id;
            })();
            const description = extractedData?.description ?? '';
            // Reset lastRolesJobId so hover/role state is clean for animation
            this.lastRolesJobId = null;

            return { id, name, description };
        });

        const center = new THREE.Vector3(0, 0, 0);
        const basis = { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) }; // XZ ring like types
        const ringCfg = this.explorerConfig.space.rings.jobs; // use configured radius directly

        // Ensure wheel group exists
        if (!this._engineJobsWheelGroup) {
            this._engineJobsWheelGroup = new THREE.Group();
            this._engineJobsWheelGroup.name = 'engine-jobs-wheel';
            this.root.add(this._engineJobsWheelGroup);
        }

        const delta = drawRing(
            this._engineJobsWheelGroup,
            groupName,
            ringEntities,
            center,
            ringCfg,
            basis,
            {
                orientationMode: 'given',
                guideConfig: {
                    ...ringCfg.ringGuide,
                    isVisible: false,
                }
            }
        );

        // Merge returned meshes into entityMeshMap for interaction filtering.
        // console.log('Delta from drawRing:', Object.keys(delta));
        Object.assign(this.entityMeshMap, delta);
        // console.log('EntityMeshMap keys after drawJobs:', Object.keys(this.entityMeshMap));

        // Draw engine-job connectors from Engine to each Job
        const enginePos = new THREE.Vector3(0, 0, 0); // Fallback if engine mesh not resolved
        const engineJobConnectorsGroup = new THREE.Group();
        engineJobConnectorsGroup.name = `${groupName}-engine-job-connectors`;
        this._engineJobsWheelGroup.add(engineJobConnectorsGroup);

        // Create engine-job connector material
        const connectorCfg = this.explorerConfig.space.lines.engineJobConnector;
        const connectorMat = new THREE.LineBasicMaterial({
            color: connectorCfg.material.color,
            transparent: true,
            opacity: connectorCfg.material.opacity,
            depthWrite: connectorCfg.material.depthWrite,
            depthTest: connectorCfg.material.depthTest,
        });

        // Try to resolve engine mesh to hit box edges instead of center
        const engineMesh: THREE.Mesh | undefined = this.entityMeshMap[s.JOB_Engine];
        const engineCenter = new THREE.Vector3().copy(enginePos);
        if (engineMesh) {
            engineMesh.getWorldPosition(engineCenter);
        }

        // Draw an engine-job connector to each job and register with animation
        const jobMeshes = this.meshEntriesFromEntityGroup(groupName);

        // Clear previous animation jobs
        this.strategyAnimation?.clearJobs();

        for (const { mesh } of jobMeshes) {
            const jobCenter = new THREE.Vector3();
            mesh.getWorldPosition(jobCenter);

            // Prefer exact intersections via raycasting; fall back to analytic approximations.
            let fromPt: THREE.Vector3;
            let toPt: THREE.Vector3;

            if (engineMesh) {
                // Hit engine surface by casting from job toward engine
                const hitEngine = this.intersectMeshSurface(engineMesh, jobCenter, engineCenter);
                fromPt = hitEngine ?? this.pointOnMeshAABBSurface(engineMesh, jobCenter);
                // Hit job surface by casting from engine center (or hit point) toward job
                const jobHitOrigin = hitEngine ?? engineCenter;
                const hitJob = this.intersectMeshSurface(mesh, jobHitOrigin, jobCenter);
                toPt = hitJob ?? this.pointOnMeshBoundingSphere(mesh, fromPt);
            } else {
                fromPt = enginePos.clone();
                toPt = this.pointOnMeshBoundingSphere(mesh, fromPt);
            }

            const engineJobConnectorGeom = new THREE.BufferGeometry().setFromPoints([fromPt, toPt]);
            const engineJobConnector = new THREE.Line(engineJobConnectorGeom, connectorMat.clone());
            engineJobConnector.renderOrder = 9998;
            engineJobConnectorsGroup.add(engineJobConnector);

            // Register job with animation system
            this.strategyAnimation?.addJob(mesh, engineJobConnector);

            // Add or update a name label sprite attached to the job
            try {
                // Resolve display name from resource data map by job id
                const jobId = String(mesh.userData?.id ?? '');
                let displayName: string | undefined = mesh.userData?.name;
                const jobItems = (this.resourceMap?.[getSpecials().TYPE_Job] ?? []) as Resource_JobJson[];
                if (!displayName && jobId) {
                    const found = jobItems.find(it => String((it.extractedData as JobJson | undefined)?.identity ?? '') === jobId);
                    if (found?.extractedData) {
                        const jobData = found.extractedData as JobJson;
                        displayName = jobData.name || jobData.identity || jobId;
                    }
                }
                if (!displayName) displayName = jobId;

                // Reuse existing sprite if present
                let sprite: THREE.Sprite | undefined = mesh.userData?.__jobLabelSprite as THREE.Sprite | undefined;
                if (!sprite) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 512;
                    canvas.height = 256;
                    const texture = new THREE.CanvasTexture(canvas);
                    const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
                    sprite = new THREE.Sprite(material);
                    sprite.renderOrder = 10002;
                    mesh.add(sprite);
                    mesh.userData.__jobLabelSprite = sprite;
                }

                // Draw text into sprite texture
                const drawText = (spr: THREE.Sprite, text: string) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d')!;
                    const padding = 32;
                    const fontSize = 68;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    const metrics = ctx.measureText(text);
                    const width = Math.ceil(metrics.width) + padding * 2;
                    const height = Math.ceil(fontSize * 1.5) + padding * 2;
                    canvas.width = width;
                    canvas.height = height;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    // Transparent background; just draw crisp white text with subtle shadow
                    ctx.clearRect(0, 0, width, height);
                    ctx.shadowColor = 'rgba(0,0,0,0.3)';
                    ctx.shadowBlur = 5;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;
                    ctx.fillStyle = '#ffffff';
                    // Use middle baseline like natural labels to prevent clipping
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, width / 2, height / 2);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                    ctx.strokeText(text, width / 2, height / 2);
                    const tex = new THREE.CanvasTexture(canvas);
                    tex.needsUpdate = true;
                    if (spr.material.map) spr.material.map.dispose();
                    spr.material.map = tex;
                    spr.material.needsUpdate = true;
                    const aspect = width / height;
                    const scaleY = 5.5;
                    spr.scale.set(scaleY * aspect, scaleY, 1);
                };

                drawText(sprite, displayName);

                // Position the sprite to the job's right/outward side so it sits next to the mesh
                // Compute yaw around origin to derive local right/outward directions
                const spacePos = new THREE.Vector3();
                mesh.getWorldPosition(spacePos);
                const yaw = Math.atan2(spacePos.z, spacePos.x);
                const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
                const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
                // Use fixed offsets to avoid PanelConfig geometry dependency
                const offsetRight = 2.4;
                const offsetOut = 1.0;
                const lift = 4.5;
                const localOffset = new THREE.Vector3()
                    .addScaledVector(right, offsetRight)
                    .addScaledVector(outward, offsetOut)
                    .add(new THREE.Vector3(0, lift, 0));
                // Convert world-space offset to mesh-local since sprite is child of mesh
                const invMatrix = new THREE.Matrix4().copy(mesh.matrixWorld).invert();
                const localPos = spacePos.clone().add(localOffset).applyMatrix4(invMatrix);
                sprite.position.copy(localPos);
                // Match visibility lifecycle: label visible when job mesh is visible
                sprite.visible = mesh.visible !== false;
            } catch { /* ignore label errors to avoid breaking draw */ }
        }

        // Animation will be started by graph_start event handler (not here)
        // so the sequence is properly set before the first frame.
        const animEnabled = this.explorerConfig.space.animations?.strategyAnimation?.enabled ?? true;

        // Ensure interactor entity filters include newly drawn jobs when animation is disabled
        if (!animEnabled) {
            try {
                this.updateInteractorEntityFilter();
            } catch { /* ignore */ }
        }
    }

    private drawTypes() {
        const n = getNames();
        const s = getSpecials();
        const FORMAT_ApplicationJson_ID = s.FORMAT_ApplicationJson;
        const TYPE_Natural_ID = s.TYPE_Natural;
        const TYPE_Boolean_ID = s.TYPE_Boolean;
        const TYPE_ResourceType_ID = 'TYPE-ResourceType';

        // Extract all ResourceType resources from resourceMap.
        // Each entry is a Resource envelope (Resource_ResourceTypeJson), and the inner extractedData is ResourceTypeJson.
        const typeItems = (this.resourceMap?.[TYPE_ResourceType_ID] ?? []) as Resource_ResourceTypeJson[];

        // Map to the ring entity format.
        // IMPORTANT: the ring entity `id` must be the actual ResourceType identity (e.g. "TYPE-Natural"),
        // not the resource envelope identity (e.g. "RESOURCE-...").
        const allTypes = typeItems.map((item) => {
            const extractedData = item.extractedData as ResourceTypeJson;
            return {
                id: extractedData.identity,
                name: extractedData.name,
                description: extractedData.description,
                resourceFormatId: extractedData.resourceFormatId,
            };
        });

        // Find fixed-position specials
        const natural = allTypes.find(t => t.id === TYPE_Natural_ID);
        const bool = allTypes.find(t => t.id === TYPE_Boolean_ID);
        if (!natural) throw new Error('Missing required special: TYPE-Natural in resourceMap');
        if (!bool) throw new Error('Missing required special: TYPE-Boolean in resourceMap');
        if (natural.resourceFormatId !== FORMAT_ApplicationJson_ID) throw new Error('TYPE-Natural must compose FORMAT-ApplicationJson');
        if (bool.resourceFormatId !== FORMAT_ApplicationJson_ID) throw new Error('TYPE-Boolean must compose FORMAT-ApplicationJson');

        // Other types: everything except Natural/Boolean/Job
        const TYPE_Job_ID = String(s.TYPE_Job);
        const others = allTypes.filter(t => t.id !== TYPE_Natural_ID && t.id !== TYPE_Boolean_ID && t.id !== TYPE_Job_ID);
        // Stable order for determinism: by id
        others.sort((a, b) => String(a.id).localeCompare(String(b.id)));

        // Build final entities list: include the fixed specials and then any remaining types.
        const entities = [natural, bool, ...others];

        const center = new THREE.Vector3(0, 0, 0);
        const u = new THREE.Vector3(1, 0, 0);
        const v = new THREE.Vector3(0, 0, 1);

        // Precompute angles: Natural at 0, Boolean at π, others spread evenly across the two half-arcs
        const angleMap = new Map<string, number>();
        angleMap.set(TYPE_Natural_ID, 0);
        // angleMap.set(TYPE_Boolean_ID, Math.PI);
        const nOthers = others.length;
        if (nOthers > 0) {
            const gap = 0.12; // keep a clear gap around 0 and π to avoid overlap with fixed types
            const span = Math.max(Math.PI - 2 * gap, 0.001); // usable span per half-arc
            const firstHalfCount = Math.ceil(nOthers / 2); // [0+gap, π-gap]
            const secondHalfCount = nOthers - firstHalfCount; // [π+gap, 2π-gap]

            // Distribute on first half-arc
            if (firstHalfCount > 0) {
                for (let i = 0; i < firstHalfCount; i++) {
                    const t = (i + 0.5) / firstHalfCount; // center in each segment
                    const angle = gap + t * span; // in (0, π)
                    angleMap.set(others[i].id, angle);
                }
            }
            // Distribute on second half-arc
            if (secondHalfCount > 0) {
                for (let j = 0; j < secondHalfCount; j++) {
                    const t = (j + 0.5) / secondHalfCount;
                    const angle = Math.PI + gap + t * span; // in (π, 2π)
                    angleMap.set(others[firstHalfCount + j].id, angle);
                }
            }
        }

        const ringCfg = this.explorerConfig.space.rings.types;
        const guideConfig = this.explorerConfig.space.lines.ringGuide;

        const delta = drawRing(
            this.root,
            n.types,
            entities,
            center,
            ringCfg,
            { u, v },
            {
                guideConfig,
                orientationMode: 'given',
                angleBy: (entity) => {
                    const angle = angleMap.get(entity.id);
                    if (angle == null) {
                        // As a fallback, evenly spread any unexpected entity using its index amongst total
                        // Note: drawRing still passes (entity, i, count), but we compute stable angles ahead of time
                        return 0;
                    }
                    return angle;
                }
            }
        );
        Object.assign(this.entityMeshMap, delta);

        // Add title sprite for Natural type (similar to job titles)
        const naturalMesh = this.entityMeshMap[TYPE_Natural_ID];
        if (naturalMesh) {
            try {
                const displayName = naturalMesh.userData?.name ?? 'Natural';

                // Reuse existing sprite if present
                let sprite: THREE.Sprite | undefined = naturalMesh.userData?.__typeLabelSprite as THREE.Sprite | undefined;
                if (!sprite) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 512;
                    canvas.height = 256;
                    const texture = new THREE.CanvasTexture(canvas);
                    const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
                    sprite = new THREE.Sprite(material);
                    sprite.renderOrder = 10002;
                    naturalMesh.add(sprite);
                    naturalMesh.userData.__typeLabelSprite = sprite;
                }

                // Draw text into sprite texture
                const drawText = (spr: THREE.Sprite, text: string) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d')!;
                    const padding = 32;
                    const fontSize = 68;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    const metrics = ctx.measureText(text);
                    const width = Math.ceil(metrics.width) + padding * 2;
                    const height = Math.ceil(fontSize * 1.5) + padding * 2;
                    canvas.width = width;
                    canvas.height = height;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    // Transparent background; just draw crisp white text with subtle shadow
                    ctx.clearRect(0, 0, width, height);
                    ctx.shadowColor = 'rgba(0,0,0,0.3)';
                    ctx.shadowBlur = 5;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;
                    ctx.fillStyle = '#ffffff';
                    // Use middle baseline like natural labels to prevent clipping
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, width / 2, height / 2);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                    ctx.strokeText(text, width / 2, height / 2);
                    const tex = new THREE.CanvasTexture(canvas);
                    tex.needsUpdate = true;
                    if (spr.material.map) spr.material.map.dispose();
                    spr.material.map = tex;
                    spr.material.needsUpdate = true;
                    const aspect = width / height;
                    const scaleY = 5.5;
                    spr.scale.set(scaleY * aspect, scaleY, 1);
                };

                drawText(sprite, displayName);

                // Position the sprite to the type's right/outward side so it sits next to the mesh
                // Compute yaw around origin to derive local right/outward directions
                const spacePos = new THREE.Vector3();
                naturalMesh.getWorldPosition(spacePos);
                const yaw = Math.atan2(spacePos.z, spacePos.x);
                const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
                const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
                // Use fixed offsets to avoid PanelConfig geometry dependency
                const offsetRight = 1.2;  // Reduced from 2.4 to move left
                const offsetOut = 1.0;
                const lift = 6.0;  // Increased from 4.5 to move up
                const localOffset = new THREE.Vector3()
                    .addScaledVector(right, offsetRight)
                    .addScaledVector(outward, offsetOut)
                    .add(new THREE.Vector3(0, lift, 0));
                // Convert world-space offset to mesh-local since sprite is child of mesh
                const invMatrix = new THREE.Matrix4().copy(naturalMesh.matrixWorld).invert();
                const localPos = spacePos.clone().add(localOffset).applyMatrix4(invMatrix);
                sprite.position.copy(localPos);
                // Match visibility lifecycle: label visible when type mesh is visible
                sprite.visible = naturalMesh.visible !== false;
            } catch { /* ignore label errors to avoid breaking draw */ }
        }
    }

    // Draw roles based on all Job resources: for each referenced type, build a vertical ring
    // with input roles in the upper semicircle and output roles in the lower semicircle.
    private pendingRolesJobId: string | null = null;
    private drawRoles(jobIdInput: string, opts: { animation: boolean }): boolean {
        // console.log('[CosmosSpace.drawRoles] Called with forAnimationJobId:', forAnimationJobId);
        const n = getNames();
        const s = getSpecials();
        // Draw roles for the currently hovered job OR the animating job
        const jobId: string | null = jobIdInput;
        const jobIdentity: string | null = jobId ? this.mapTimelineJobIdToJobIdentity(jobId) : null;

        if (opts.animation) {
            // console.log('[CosmosSpace.drawRoles] Animation path, clearing existing roles');
            // For animation: clear role rings/links and input connectors, but fade previous output connectors
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === n.roles ||
                    child.name === `${n.roles}-ring-guide` ||
                    child.name === 'job-role-connectors' ||
                    child.name.startsWith('role-resource-input-connectors-') ||
                    child.name.startsWith('role-resource-output-connectors-')
                ) {
                    // Remove all role-related elements and connectors (both input and output)
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === n.roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
        } else {
            // Non-animation path (hover/selection): clear any lingering animation connectors
            // This ensures connectors from the last animation step don't persist after animation stops
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (child.name === 'job-role-connectors') {
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
        }

        if (!jobIdentity) return false;

        // console.log('[CosmosSpace.drawRoles] Processing roles for jobId:', jobId);

        const TYPE_Job_ID = s.TYPE_Job;
        const jobItems = (this.resourceMap?.[TYPE_Job_ID] ?? []) as Resource_JobJson[];
        const item = jobItems.find((r) => {
            const jobData = r.extractedData as JobJson | undefined;
            const extractedIdentity = String(jobData?.identity ?? '');
            return extractedIdentity === jobIdentity;
        });
        if (!item) {
            return false;
        }

        // Collect roles for this job grouped by target type
        type RoleEntry = { id: string; name: string; description: string; kind: 'input' | 'output'; resourceTypeId: string };
        const rolesByType = new Map<string, RoleEntry[]>();
        // Some job data may provide roles under `roleMap` instead of `roles`; support both.
        const exposed = item.extractedData as unknown as {
            roles?: { inputMap?: Record<string, { resourceTypeId: string; name?: string; description?: string }>; outputMap?: Record<string, { resourceTypeId: string; name?: string; description?: string }> };
            roleMap?: { inputMap?: Record<string, { resourceTypeId: string; name?: string; description?: string }>; outputMap?: Record<string, { resourceTypeId: string; name?: string; description?: string }> };
        };
        const inputMap = exposed?.roles?.inputMap ?? exposed?.roleMap?.inputMap ?? {};
        const outputMap = exposed?.roles?.outputMap ?? exposed?.roleMap?.outputMap ?? {};
        for (const [resourceRoleId, info] of Object.entries(inputMap)) {
            if (!info?.resourceTypeId) continue;
            const arr = rolesByType.get(info.resourceTypeId) ?? [];
            arr.push({ id: resourceRoleId, name: String(info.name ?? resourceRoleId), description: String(info.description ?? ''), kind: 'input', resourceTypeId: info.resourceTypeId });
            rolesByType.set(info.resourceTypeId, arr);
        }
        for (const [resourceRoleId, info] of Object.entries(outputMap)) {
            if (!info?.resourceTypeId) continue;
            const arr = rolesByType.get(info.resourceTypeId) ?? [];
            arr.push({ id: resourceRoleId, name: String(info.name ?? resourceRoleId), description: String(info.description ?? ''), kind: 'output', resourceTypeId: info.resourceTypeId });
            rolesByType.set(info.resourceTypeId, arr);
        }
        if (rolesByType.size === 0) {
            if (this._debug.roles) {
                console.log(`[drawRoles] No roles found for job ${jobId}. keys (roles vs roleMap):`, Object.keys(exposed?.roles?.inputMap || {}), Object.keys(exposed?.roleMap?.inputMap || {}));
            }
            return false;
        }

        const groupName = n.roles;
        // Job mesh for link line source
        const jobMeshEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
        const jobMesh = jobMeshEntry?.mesh;

        // CRITICAL: Use job's CUROLEENT space position (like engine-job connectors do)
        // This ensures connectors emanate from where the job currently is after rotation.
        let jobPos: THREE.Vector3 | undefined;
        let jobSpaceRadius: number | undefined;
        if (jobMesh) {
            // Base local radius from geometry
            if (!jobMesh.geometry.boundingSphere) jobMesh.geometry.computeBoundingSphere();
            const localRadius = jobMesh.geometry.boundingSphere?.radius ?? 0;
            // Space scale factor
            const worldScale = new THREE.Vector3();
            jobMesh.getWorldScale(worldScale);
            const scaleFactor = Math.max(worldScale.x, worldScale.y, worldScale.z);
            jobSpaceRadius = localRadius * scaleFactor;

            if (opts.animation) {
                // Use current space position (after wheel rotation) - same as engine-job connectors
                jobPos = new THREE.Vector3();
                jobMesh.getWorldPosition(jobPos);
                // console.log(`[CONNECTOR_ANCHOR] Job ${forAnimationJobId}: current space pos =`, jobPos.toArray());
            } else {
                jobPos = new THREE.Vector3();
                jobMesh.getWorldPosition(jobPos);
            }
        }

        // Group to hold link lines (green for inputs, red for outputs)
        const linkGroup = new THREE.Group();
        linkGroup.name = 'job-role-connectors';
        this.root.add(linkGroup);

        // Array to store all job-role connector lines for animation
        const jobRoleConnectors: THREE.Line[] = [];

        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
            const mat = new THREE.LineBasicMaterial({
                color,
                transparent: true,
                opacity: 0.9,
                depthWrite: false,
                depthTest: false  // Changed to false to ensure connectors are always visible
            });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10000; // Higher render order to draw on top
            line.frustumCulled = false; // Prevent frustum culling
            // console.log('makeLine: from', from.toArray(), 'to', to.toArray(), 'distance', from.distanceTo(to));
            return line;
        };
        for (const [resourceTypeId, entries] of rolesByType.entries()) {
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(e => e.id === resourceTypeId)?.mesh;
            if (!typeMesh) {
                continue;
            }
            const center = new THREE.Vector3();
            typeMesh.getWorldPosition(center);

            // Choose a local radius so roles don't intersect the type mesh
            typeMesh.geometry.computeBoundingSphere();
            const typeRadius = typeMesh.geometry.boundingSphere?.radius ?? 1;
            const localRadius = typeRadius * 2.2;

            const baseRolesCfg = this.explorerConfig.space.rings.roles ?? this.explorerConfig.space.rings.types;
            const ringCfg = { ...baseRolesCfg, ringRadius: localRadius } as typeof baseRolesCfg;

            // Basis: align ring to follow the curvature of the type ring.
            // Use tangent (along the type ring) and space up as the ring plane axes,
            // so the ring plane normal points radially outward.
            const up = new THREE.Vector3(0, 1, 0);
            const radial = center.clone();
            const upComp = up.clone().multiplyScalar(radial.dot(up));
            radial.sub(upComp);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();
            // tangent = up x radial (lies in XZ plane, direction of type ring curvature)
            const tangent = new THREE.Vector3().crossVectors(up, radial);
            if (tangent.lengthSq() < 1e-6) tangent.set(0, 0, 1);
            tangent.normalize();
            const uAxis = tangent;
            const vAxis = up;

            const inputs = entries.filter(e => e.kind === 'input').sort((a, b) => a.name.localeCompare(b.name));
            const outputs = entries.filter(e => e.kind === 'output').sort((a, b) => a.name.localeCompare(b.name));
            const ordered = [...inputs, ...outputs];
            const ringEntities = ordered.map(r => ({ id: r.id, name: r.name, description: r.description }));

            const delta = drawRing(
                this.root,
                groupName,
                ringEntities,
                center,
                ringCfg as typeof baseRolesCfg,
                { u: uAxis, v: vAxis },
                {
                    orientationMode: 'given',
                    semicircleBy: (entity) => inputs.some(i => i.id === entity.id) ? 'upper' : 'lower',
                    nameBy: (entity) => String((entity as unknown as { name?: unknown }).name ?? entity.id),
                }
            );

            // If drawing for animation, make role meshes invisible initially
            if (opts.animation) {
                for (const id of Object.keys(delta)) {
                    const mesh = delta[id];
                    if (mesh.userData?.entity === groupName) {
                        mesh.visible = false;
                    }
                }
            }

            Object.assign(this.entityMeshMap, delta);
            // Attach description to userData for role meshes to support hover tooltip (already in mesh.userData from drawRing)
            // Draw link lines if we have job position and freshly created role meshes
            if (jobPos) {
                const roleMeshById = new Map<string, THREE.Mesh>();
                for (const rid of Object.keys(delta)) {
                    roleMeshById.set(rid, delta[rid]);
                }
                const inputs = entries.filter(e => e.kind === 'input');
                const outputs = entries.filter(e => e.kind === 'output');

                const jobCenter = new THREE.Vector3();
                jobMesh?.getWorldPosition(jobCenter);
                for (const r of inputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const roleCenter = new THREE.Vector3();
                        rm.getWorldPosition(roleCenter);
                        // During animation, anchor from the active job's REST space position and radius
                        const fromPt = (opts.animation && jobPos && jobSpaceRadius !== undefined)
                            ? (() => {
                                const dir = roleCenter.clone().sub(jobPos!);
                                if (dir.lengthSq() < 1e-9) return jobPos!.clone();
                                const computed = jobPos!.clone().add(dir.normalize().multiplyScalar(jobSpaceRadius!));
                                // console.log(`[CONNECTOR_ANCHOR] Input role ${r.id}: jobPos=${jobPos!.toArray()}, roleCenter=${roleCenter.toArray()}, dir=${dir.toArray()}, fromPt=${computed.toArray()}`);
                                return computed;
                            })()
                            : (jobMesh ? this.pointOnMeshBoundingSphere(jobMesh, roleCenter) : (jobPos ? jobPos.clone() : new THREE.Vector3()));
                        const toPt = this.pointOnMeshBoundingSphere(rm, jobCenter);
                        const line = makeLine(fromPt, toPt, 0x00cc66);
                        if (opts.animation) line.visible = false;
                        linkGroup.add(line);
                        jobRoleConnectors.push(line);
                    }
                }
                for (const r of outputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const roleCenter = new THREE.Vector3();
                        rm.getWorldPosition(roleCenter);
                        const fromPt = (opts.animation && jobPos && jobSpaceRadius !== undefined)
                            ? (() => {
                                const dir = roleCenter.clone().sub(jobPos!);
                                if (dir.lengthSq() < 1e-9) return jobPos!.clone();
                                const computed = jobPos!.clone().add(dir.normalize().multiplyScalar(jobSpaceRadius!));
                                // console.log(`[CONNECTOR_ANCHOR] Output role ${r.id}: jobPos=${jobPos!.toArray()}, roleCenter=${roleCenter.toArray()}, dir=${dir.toArray()}, fromPt=${computed.toArray()}`);
                                return computed;
                            })()
                            : (jobMesh ? this.pointOnMeshBoundingSphere(jobMesh, roleCenter) : (jobPos ? jobPos.clone() : new THREE.Vector3()));
                        const toPt = this.pointOnMeshBoundingSphere(rm, jobCenter);
                        const line = makeLine(fromPt, toPt, 0xcc0033);
                        if (opts.animation) line.visible = false;
                        linkGroup.add(line);
                        jobRoleConnectors.push(line);
                    }
                }
                // console.log('drawRoles: added', jobRoleConnectors.length, 'connectors to linkGroup for job', jobId, 'initial visible =', !forAnimationJobId);
            }
        }

        // After processing all types, register all accumulated connectors for animation
        if (opts.animation && jobMesh && jobRoleConnectors.length > 0) {
            // console.log('drawRoles: registering job-role connectors for job', jobId, 'connectors count =', jobRoleConnectors.length);
            this.strategyAnimation?.setJobRoleConnectors(jobMesh, jobRoleConnectors);
        } else if (opts.animation) {
            // console.log('drawRoles: no connectors registered for job', jobId, 'jobMesh exists =', !!jobMesh, 'jobRoleConnectors.length =', jobRoleConnectors.length);
        }

        // Draw and register role-resource INPUT connectors for animation
        if (opts.animation && jobMesh) {
            const execInfo = this.getExecutionInfoForJob(jobId);
            if (execInfo) {
                // Highlight resource panels that were outputs from PREVIOUS step
                this.highlightPreviousOutputResources(this._previousStepOutputs);

                // Store current outputs for next step's highlighting
                this._previousStepOutputs = { ...execInfo.outputBindingMap };

                const inputConnectors = this.createRoleResourceInputConnectors(
                    execInfo.inputBindingMap,
                    true // initially hidden, animation will show them
                );
                if (inputConnectors.length > 0) {
                    // Add to scene
                    const inputGroup = new THREE.Group();
                    inputGroup.name = `role-resource-input-connectors-${execInfo.executionId}`;
                    inputConnectors.forEach(line => inputGroup.add(line));
                    this.root.add(inputGroup);

                    // Register with animation
                    this.strategyAnimation?.setRoleResourceInputConnectors(jobMesh, inputConnectors);
                }
            }
        }
        return true;
    }

    // Removed sprite-based role hover tooltip; DomInteractor handles text display.

    // Draw output role-resource connectors when job completes (called during PULLING_OUT phase)
    private drawOutputRoleResourceConnectors(jobId: string): void {
        const n = getNames();
        const jobMeshEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
        const jobMesh = jobMeshEntry?.mesh;
        if (!jobMesh) return;

        const execInfo = this.getExecutionInfoForJob(jobId);
        if (!execInfo) return;

        // Track newly created natural values from outputs
        this.updateCreatedNaturals(execInfo.executionId, execInfo.outputBindingMap);

        const outputConnectors = this.createRoleResourceOutputConnectors(
            execInfo.outputBindingMap,
            false // visible immediately when created (during PULLING_OUT)
        );

        if (outputConnectors.length > 0) {
            // Add to scene
            const outputGroup = new THREE.Group();
            outputGroup.name = `role-resource-output-connectors-${execInfo.executionId}`;
            outputConnectors.forEach(line => outputGroup.add(line));
            this.root.add(outputGroup);

            // Register with animation (will make them visible immediately)
            this.strategyAnimation?.setRoleResourceOutputConnectors(jobMesh, outputConnectors);
        }
    }

    private drawPrimitiveResources() {
        const n = getNames();
        const s = getSpecials();

        const resourcesByType = this.resourceMap;
        if (!resourcesByType) return;

        const sharedBase = computeFrameworkBaseRadius(this.explorerConfig.space);
        const sphereRadius = sharedBase + (this.explorerConfig.space.sphere?.radiusOffset ?? 16);
        // Ensure camera far plane includes the caps even if the sphere grid is not drawn
        try {
            const cam = (this as unknown as { camera?: THREE.Camera }).camera as THREE.Camera | undefined;
            // Only adjust perspective cameras; orthographic far planes generally already cover the scene
            if (cam && (cam as unknown as { isPerspectiveCamera?: boolean }).isPerspectiveCamera) {
                const pc = cam as THREE.PerspectiveCamera;
                const targetFar = sphereRadius * 2.5;
                if (pc.far < targetFar) {
                    pc.far = targetFar;
                    pc.updateProjectionMatrix();
                }
            }
        } catch { /* best-effort camera fit */ }

        const group = new THREE.Group();
        const groupName = n.resources;
        group.name = groupName;
        this.root.add(group);

        const edgeMat = new THREE.LineBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.5, depthWrite: false, depthTest: false });

        const parentEntries = this.meshEntriesFromEntityGroup(n.types);

        // Render Boolean polar caps as before
        const TYPE_Boolean_ID = s.TYPE_Boolean;
        const booleanItems = resourcesByType[TYPE_Boolean_ID] ?? [];
        if (booleanItems.length > 0) {
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(t => t.id === TYPE_Boolean_ID)?.mesh;
            const typePos = typeMesh?.position.clone() ?? new THREE.Vector3(0, 0, 0);
            // Material for type-resource connector lines from TYPE_Boolean to caps
            const connectorCfg = this.explorerConfig.space.lines.typeResourceConnector;
            const connectorMat = new THREE.LineBasicMaterial({
                color: connectorCfg.material.color,
                transparent: true,
                opacity: connectorCfg.material.opacity,
                depthWrite: connectorCfg.material.depthWrite,
                depthTest: connectorCfg.material.depthTest,
            });
            const makePolarCap = (isNorth: boolean, item: ResourceJson) => {
                const delta = this.explorerConfig.space.poleCaps?.delta ?? 0.12;
                const radiusScale = this.explorerConfig.space.poleCaps?.radiusScale ?? 1.0;
                const color = isNorth ? (this.explorerConfig.space.poleCaps?.colorTrue ?? 0x66ff99) : (this.explorerConfig.space.poleCaps?.colorFalse ?? 0xff6699);
                const opacity = this.explorerConfig.space.poleCaps?.opacity ?? 0.85;
                const lat = isNorth ? (Math.PI / 2 - delta) : (-Math.PI / 2 + delta);
                const y = sphereRadius * Math.sin(lat);
                const r = sphereRadius * Math.cos(lat) * radiusScale;

                const discGeometry = new THREE.CircleGeometry(r, 64);
                const discMaterial = new THREE.MeshBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
                const discMesh = new THREE.Mesh(discGeometry, discMaterial);
                // Prevent culling/clipping surprises when sphere grid is hidden
                discMesh.frustumCulled = false;
                discMesh.rotation.x = isNorth ? -Math.PI / 2 : Math.PI / 2;
                const azimuth = Math.atan2(typePos.z, typePos.x);
                discMesh.rotation.y = azimuth;
                discMesh.position.set(0, y, 0);
                const id = String((item as unknown as { identity?: unknown }).identity ?? '');
                const extractedData = item.extractedData as { identity?: string } | null;
                const name = String(extractedData?.identity ?? '');
                discMesh.userData = { entity: groupName, id, name, booleanCap: true, isNorth };
                discMesh.renderOrder = 10001;
                group.add(discMesh);

                // Draw connector from TYPE_Boolean edge to cap rim (toward the type position)
                try {
                    const center = discMesh.position.clone();
                    const typeXZ = new THREE.Vector3(typePos.x, center.y, typePos.z);
                    const dirXZ = typeXZ.clone().sub(center);
                    dirXZ.y = 0;
                    const to = dirXZ.lengthSq() > 1e-9 ? center.clone().add(dirXZ.normalize().multiplyScalar(r)) : center.clone();
                    const from = typeMesh ? this.pointOnMeshBoundingSphere(typeMesh, to) : typePos.clone();
                    const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
                    const line = new THREE.Line(geom, connectorMat.clone());
                    line.renderOrder = 10000;
                    line.frustumCulled = false;
                    group.add(line);
                } catch { /* ignore connector failures */ }
            };
            const TRUE_ID = s.BOOLEAN_true;
            const FALSE_ID = s.BOOLEAN_false;
            const trueItem = booleanItems.find(r => (r as unknown as { identity?: unknown }).identity === TRUE_ID);
            const falseItem = booleanItems.find(r => (r as unknown as { identity?: unknown }).identity === FALSE_ID);
            if (!trueItem) throw new Error('Missing required BOOLEAN-true resource data item');
            if (!falseItem) throw new Error('Missing required BOOLEAN-false resource data item');
            makePolarCap(false, trueItem);
            makePolarCap(true, falseItem);
        }

        const resMatConfig = this.explorerConfig.space.panels.resources.material;
        const resMat = this.makeMaterialFromConfig(resMatConfig);

        const TYPE_Natural_ID = s.TYPE_Natural;
        const sortNaturalResources = (items: ResourceJson[]): ResourceJson[] => {
            return [...items].sort((a, b) => {
                const av = typeof (a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                const bv = typeof (b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                return av - bv;
            });
        };

        for (const parent of parentEntries) {
            if (parent.id !== TYPE_Natural_ID) continue;
            const rawItems = (resourcesByType[parent.id] ?? []) as ResourceJson[];
            if (!rawItems.length) continue;

            const parentPos = parent.mesh.position.clone();
            const ident = (res: ResourceJson) => {
                const r = res as unknown as { path?: unknown; extractedData?: { identity?: unknown } };
                return { id: String(r.path ?? ''), name: String(r.extractedData?.identity ?? '') };
            };

            // Direction outward from Natural type in XZ plane
            const outwardDir = new THREE.Vector3(parentPos.x, 0, parentPos.z);
            if (outwardDir.lengthSq() < 1e-6) outwardDir.set(1, 0, 0);
            outwardDir.normalize();

            // Layout parameters from config with sensible defaults
            const sheetCfg = (this.explorerConfig.space.panels.resources as unknown as { naturalSheets?: { baseOffset?: number; spacing?: number; sheetWidth?: number; sheetDepth?: number } }).naturalSheets || {};
            const baseOffset = typeof sheetCfg.baseOffset === 'number' ? sheetCfg.baseOffset : 5.0;
            const spacing = typeof sheetCfg.spacing === 'number' ? sheetCfg.spacing : 3.0;
            const sheetWidth = typeof sheetCfg.sheetWidth === 'number' ? sheetCfg.sheetWidth : 4.5;
            const sheetDepth = typeof sheetCfg.sheetDepth === 'number' ? sheetCfg.sheetDepth : 3.0;

            const items = sortNaturalResources(rawItems);

            // Compute type radius so we can start sheets just outside the type surface
            parent.mesh.geometry.computeBoundingSphere();
            const typeRadius = parent.mesh.geometry.boundingSphere?.radius ?? 0;

            const panels: THREE.Mesh[] = [];
            items.forEach((resource, i) => {
                // Spacing logic: centers are separated by sheetWidth + spacing; first sheet starts just beyond type surface.
                const distance = typeRadius + baseOffset + sheetWidth / 2 + i * (sheetWidth + spacing);
                const pos = parentPos.clone().add(outwardDir.clone().multiplyScalar(distance));
                pos.y = 0; // lie in XZ plane

                // PlaneGeometry lies in XY; rotate to XZ by -PI/2 around X
                const geom = new THREE.PlaneGeometry(sheetWidth, sheetDepth, 1, 1);
                const panel = new THREE.Mesh(geom, resMat.clone());
                panel.rotation.x = -Math.PI / 2;
                // Align plane's width (local X) with outwardDir by rotating around Y
                const yaw = Math.atan2(outwardDir.z, outwardDir.x);
                panel.rotation.y = yaw;
                panel.position.copy(pos);

                const rid = ident(resource);
                panel.userData = { entity: groupName, id: rid.id, name: rid.name };
                panel.renderOrder = 9998;

                // Outline edges for visual clarity
                const edges = new THREE.EdgesGeometry(geom);
                const outline = new THREE.LineSegments(edges, edgeMat.clone());
                outline.rotation.copy(panel.rotation);
                outline.position.copy(panel.position);
                outline.renderOrder = 10000;

                group.add(panel);
                group.add(outline);
                panels.push(panel);
            });

            // Draw type-resource connectors: TYPE_Natural -> first panel, and between panels (edge-to-edge)
            if (panels.length > 0) {
                const connCfg = this.explorerConfig.space.lines.typeResourceConnector;
                const connMat = new THREE.LineBasicMaterial({
                    color: connCfg.material.color,
                    transparent: true,
                    opacity: connCfg.material.opacity,
                    depthWrite: connCfg.material.depthWrite,
                    depthTest: connCfg.material.depthTest,
                });
                const halfW = sheetWidth / 2;
                const halfD = sheetDepth / 2;
                const up = new THREE.Vector3(0, 1, 0);
                const localX = outwardDir.clone();
                const localZ = new THREE.Vector3(-localX.z, 0, localX.x).normalize(); // perpendicular in XZ

                const panelEdgePoint = (panel: THREE.Mesh, toward: THREE.Vector3) => {
                    const center = new THREE.Vector3();
                    panel.getWorldPosition(center);
                    const v = toward.clone().sub(center);
                    // Project into panel plane (XZ)
                    v.y = 0;
                    if (v.lengthSq() < 1e-9) return center.clone();
                    const vx = v.dot(localX);
                    const vz = v.dot(localZ);
                    const denom = Math.max(Math.abs(vx) / Math.max(halfW, 1e-9), Math.abs(vz) / Math.max(halfD, 1e-9), 1e-9);
                    const scale = 1.0 / denom;
                    return center.clone().add(localX.clone().multiplyScalar(vx * scale)).add(localZ.clone().multiplyScalar(vz * scale));
                };

                const makeConn = (a: THREE.Vector3, b: THREE.Vector3) => {
                    const geom = new THREE.BufferGeometry().setFromPoints([a.clone(), b.clone()]);
                    const line = new THREE.Line(geom, connMat.clone());
                    line.renderOrder = 10000;
                    line.frustumCulled = false;
                    group.add(line);
                };

                // TYPE -> first (edge-to-edge)
                const typeToFirst_to = panelEdgePoint(panels[0], parentPos);
                const typeToFirst_from = this.pointOnMeshBoundingSphere(parent.mesh, typeToFirst_to);
                makeConn(typeToFirst_from, typeToFirst_to);

                // Between consecutive panels (edge-to-edge)
                for (let i = 1; i < panels.length; i++) {
                    const a = panels[i - 1];
                    const b = panels[i];
                    const bCenter = new THREE.Vector3();
                    const aCenter = new THREE.Vector3();
                    b.getWorldPosition(bCenter);
                    a.getWorldPosition(aCenter);
                    const aToB = panelEdgePoint(a, bCenter);
                    const bToA = panelEdgePoint(b, aCenter);
                    makeConn(aToB, bToA);
                }
            }
        }

    }

    // Update tracked naturals based on output bindings and materialized strategy state.
    private updateCreatedNaturals(executionId: string, outputBindingMap: Record<string, string>): void {
        const sfs = this.statefulStrategy as unknown as { strategyState?: Record<string, Record<string, unknown>> } | null;
        const execRoleMap = sfs?.strategyState?.[executionId];
        if (!execRoleMap) return;

        let foundNewNatural = false;

        // Output bindings are roleId -> resourceId. Use roleId to dereference the materialized record.
        for (const [roleId, resourceId] of Object.entries(outputBindingMap)) {
            const record = execRoleMap[roleId] as { extractedData?: { identity?: unknown } } | undefined;
            const maybeValue = record?.extractedData?.identity;
            if (typeof maybeValue !== 'number') continue;

            this.stateManager?.setNaturalIdentity(resourceId, maybeValue);
            const wasNew = this.stateManager?.addCreatedNatural(maybeValue) ?? false;
            if (wasNew) foundNewNatural = true;
        }

        if (foundNewNatural) {
            this.drawNaturals();
        }
    }

    private drawNaturals() {
        const n = getNames();
        const s = getSpecials();
        const TYPE_Natural_ID = s.TYPE_Natural;

        // Get TYPE_Natural mesh for positioning
        const parentEntries = this.meshEntriesFromEntityGroup(n.types);
        const parent = parentEntries.find(p => p.id === TYPE_Natural_ID);
        if (!parent) return;

        const parentPos = parent.mesh.position.clone();
        const groupName = n.resources;

        // Ensure resources group exists
        let group = this.root.getObjectByName(groupName) as THREE.Group;
        if (!group) {
            group = new THREE.Group();
            group.name = groupName;
            this.root.add(group);
        }

        // Direction outward from Natural type in XZ plane
        const outwardDir = new THREE.Vector3(parentPos.x, 0, parentPos.z);
        if (outwardDir.lengthSq() < 1e-6) outwardDir.set(1, 0, 0);
        outwardDir.normalize();

        // Layout parameters
        const sheetCfg = (this.explorerConfig.space.panels.resources as unknown as { naturalSheets?: { baseOffset?: number; spacing?: number; sheetWidth?: number; sheetDepth?: number } }).naturalSheets || {};
        const baseOffset = typeof sheetCfg.baseOffset === 'number' ? sheetCfg.baseOffset : 5.0;
        const spacing = typeof sheetCfg.spacing === 'number' ? sheetCfg.spacing : 3.0;
        const sheetWidth = typeof sheetCfg.sheetWidth === 'number' ? sheetCfg.sheetWidth : 4.5;
        const sheetDepth = typeof sheetCfg.sheetDepth === 'number' ? sheetCfg.sheetDepth : 3.0;

        // Compute type radius
        parent.mesh.geometry.computeBoundingSphere();
        const typeRadius = parent.mesh.geometry.boundingSphere?.radius ?? 0;

        // Material for panels
        const resMatConfig = this.explorerConfig.space.panels.resources.material;
        const resMat = this.makeMaterialFromConfig(resMatConfig);

        // Edge material for outlines
        const edgeMat = new THREE.LineBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.5,
            depthWrite: false,
            depthTest: false
        });

        // Sort created naturals for consistent ordering
        const sortedNaturals = Array.from(this.stateManager?.getCreatedNaturals() ?? new Set<number>()).sort((a, b) => a - b);
        // console.log(`[drawNaturals] Creating panels for naturals:`, sortedNaturals);

        // Create panels for each natural
        const panels: THREE.Mesh[] = [];
        sortedNaturals.forEach((value: number) => {
            // Generate a stable path-like ID for this natural value
            const resourceId = `TYPE-Natural/mock-${value}`;

            // Calculate position based on actual value (not sorted index) so each natural has a designated spot
            const distance = typeRadius + baseOffset + sheetWidth / 2 + value * (sheetWidth + spacing);
            const pos = parentPos.clone().add(outwardDir.clone().multiplyScalar(distance));
            pos.y = 0;

            // Check if panel already exists
            const existingPanel = group.children.find(
                child => child.userData?.id === resourceId && child instanceof THREE.Mesh
            );
            if (existingPanel) {
                // console.log(`[drawNaturals] Reusing existing panel for ${value} at position ${value}`);
                // Reposition existing panel to correct position (in case spacing changed)
                existingPanel.position.copy(pos);
                // Also update its outline
                const outline = group.children.find(
                    child => child.userData?.isOutline && child.userData?.parentId === resourceId
                );
                if (outline) {
                    outline.position.copy(pos);
                    // Ensure baseline outline color is captured once
                    const om = (outline as THREE.LineSegments).material as THREE.LineBasicMaterial;
                    if (om && !outline.userData.__baselineOutlineColor) {
                        outline.userData.__baselineOutlineColor = om.color.clone();
                    }
                }
                // Update label position if present: vertical and offset to the panel's right
                const label = group.children.find(
                    child => child.userData?.isLabel && child.userData?.parentId === resourceId
                );
                if (label) {
                    const yawExisting = (existingPanel as THREE.Mesh).rotation.y;
                    const rightExisting = new THREE.Vector3(Math.cos(yawExisting), 0, -Math.sin(yawExisting));
                    const outwardExisting = new THREE.Vector3(Math.cos(yawExisting), 0, Math.sin(yawExisting));
                    const offsetRight = rightExisting.multiplyScalar(sheetWidth * 0.55); // further right
                    const offsetOutward = outwardExisting.multiplyScalar(sheetDepth * 0.25); // a bit outward
                    const basePosExisting = new THREE.Vector3(pos.x, pos.y, pos.z).add(offsetRight).add(offsetOutward);
                    label.position.copy(new THREE.Vector3(basePosExisting.x, basePosExisting.y + 0.25, basePosExisting.z));
                    label.rotation.set(0, 0, 0);
                }
                // Ensure baseline emissive values are captured once
                const existingMat = (existingPanel as THREE.Mesh).material as THREE.MeshStandardMaterial;
                if (existingMat && !existingPanel.userData.__baselineEmissiveColor) {
                    existingPanel.userData.__baselineEmissiveColor = existingMat.emissive.clone();
                    existingPanel.userData.__baselineEmissiveIntensity = typeof existingMat.emissiveIntensity === 'number' ? existingMat.emissiveIntensity : 1;
                }
                panels.push(existingPanel as THREE.Mesh);
                this.stateManager?.setMockPanel(value, existingPanel as THREE.Mesh);
                return;
            }

            // Create new panel
            // console.log(`[drawNaturals] Creating NEW panel for ${value} at position ${value}`);

            const geom = new THREE.PlaneGeometry(sheetWidth, sheetDepth, 1, 1);
            const panel = new THREE.Mesh(geom, resMat.clone());
            panel.rotation.x = -Math.PI / 2;
            const yaw = Math.atan2(outwardDir.z, outwardDir.x);
            panel.rotation.y = yaw;
            panel.position.copy(pos);
            panel.userData = { entity: groupName, id: resourceId, name: String(value) };
            panel.renderOrder = 9998;

            // Capture baseline emissive values for reliable highlight restore
            const mat = panel.material as THREE.MeshStandardMaterial;
            panel.userData.__baselineEmissiveColor = mat.emissive.clone();
            panel.userData.__baselineEmissiveIntensity = typeof mat.emissiveIntensity === 'number' ? mat.emissiveIntensity : 1;

            // Outline edges
            const edges = new THREE.EdgesGeometry(geom);
            const outline = new THREE.LineSegments(edges, edgeMat.clone());
            outline.rotation.copy(panel.rotation);
            outline.position.copy(panel.position);
            outline.renderOrder = 10000;
            outline.userData = { isOutline: true, parentId: resourceId };
            // Capture baseline outline color
            const om = outline.material as THREE.LineBasicMaterial;
            outline.userData.__baselineOutlineColor = om.color.clone();

            group.add(panel);
            group.add(outline);
            // Create numeric label canvas texture
            const labelCanvas = document.createElement('canvas');
            // Higher-res canvas for crisp text
            labelCanvas.width = 256; labelCanvas.height = 128;
            const ctx = labelCanvas.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, labelCanvas.width, labelCanvas.height);
                // Draw subtle shadow for contrast
                ctx.shadowColor = 'rgba(0,0,0,0.6)';
                ctx.shadowBlur = 6;
                ctx.shadowOffsetX = 2;
                ctx.shadowOffsetY = 2;
                // Bold, larger font for readability
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 84px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(String(value), labelCanvas.width / 2, labelCanvas.height / 2);
                // Optional stroke for extra contrast
                ctx.lineWidth = 4;
                ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                ctx.strokeText(String(value), labelCanvas.width / 2, labelCanvas.height / 2);
            }
            const tex = new THREE.CanvasTexture(labelCanvas);
            tex.minFilter = THREE.LinearFilter;
            tex.magFilter = THREE.LinearFilter;
            tex.needsUpdate = true;
            // Use a vertical sprite for better readability and to avoid connector overlap
            const labelMat = new THREE.SpriteMaterial({ map: tex, transparent: true, depthWrite: false, depthTest: false });
            const labelSprite = new THREE.Sprite(labelMat);
            // Scale larger for visibility
            labelSprite.scale.set(sheetWidth * 0.8, sheetDepth * 0.6, 1);
            // Compute panel right vector (local +X rotated by yaw)
            const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
            const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
            const offsetRight = right.multiplyScalar(sheetWidth * 0.55); // further right
            const offsetOutward = outward.multiplyScalar(sheetDepth * 0.25); // a bit outward
            const basePos = new THREE.Vector3(panel.position.x, panel.position.y, panel.position.z).add(offsetRight).add(offsetOutward);
            // Place slightly above panel
            labelSprite.position.copy(new THREE.Vector3(basePos.x, basePos.y + 0.25, basePos.z));
            // Center so it aligns correctly
            (labelSprite as THREE.Sprite).center.set(0.5, 0.5);
            labelSprite.renderOrder = 10001;
            labelSprite.userData = { isLabel: true, parentId: resourceId };
            // Debug: log label creation
            try {
                // console.log(`[NaturalLabel] Created vertical label for ${value} at`, labelSprite.position);
            }
            catch { }
            group.add(labelSprite);
            panels.push(panel);
            this.stateManager?.setMockPanel(value, panel);
            // console.log(`[drawNaturals] Stored panel in map: ${value} -> panel`);
        });

        // Draw type-resource connectors
        if (panels.length > 0) {
            const connCfg = this.explorerConfig.space.lines.typeResourceConnector;
            const connMat = new THREE.LineBasicMaterial({
                color: connCfg.material.color,
                transparent: true,
                opacity: connCfg.material.opacity,
                depthWrite: connCfg.material.depthWrite,
                depthTest: connCfg.material.depthTest,
            });

            const halfW = sheetWidth / 2;
            const halfD = sheetDepth / 2;
            const localX = outwardDir.clone();
            const localZ = new THREE.Vector3(-localX.z, 0, localX.x).normalize();

            const panelEdgePoint = (panel: THREE.Mesh, toward: THREE.Vector3) => {
                const center = new THREE.Vector3();
                panel.getWorldPosition(center);
                const v = toward.clone().sub(center);
                v.y = 0;
                if (v.lengthSq() < 1e-9) return center.clone();
                const vx = v.dot(localX);
                const vz = v.dot(localZ);
                const denom = Math.max(Math.abs(vx) / Math.max(halfW, 1e-9), Math.abs(vz) / Math.max(halfD, 1e-9), 1e-9);
                const scale = 1.0 / denom;
                return center.clone().add(localX.clone().multiplyScalar(vx * scale)).add(localZ.clone().multiplyScalar(vz * scale));
            };

            const makeConn = (a: THREE.Vector3, b: THREE.Vector3) => {
                const geom = new THREE.BufferGeometry().setFromPoints([a.clone(), b.clone()]);
                const line = new THREE.Line(geom, connMat.clone());
                line.renderOrder = 10000;
                line.frustumCulled = false;
                line.userData = { isConnector: true };
                group.add(line);
            };

            // Remove old connectors
            const oldConnectors = group.children.filter(child => child.userData?.isConnector);
            oldConnectors.forEach(conn => group.remove(conn));

            // TYPE → first panel
            const typeToFirst_to = panelEdgePoint(panels[0], parentPos);
            const typeToFirst_from = this.pointOnMeshBoundingSphere(parent.mesh, typeToFirst_to);
            makeConn(typeToFirst_from, typeToFirst_to);

            // Between consecutive panels
            for (let i = 1; i < panels.length; i++) {
                const a = panels[i - 1];
                const b = panels[i];
                const bCenter = new THREE.Vector3();
                const aCenter = new THREE.Vector3();
                b.getWorldPosition(bCenter);
                a.getWorldPosition(aCenter);
                const aToB = panelEdgePoint(a, bCenter);
                const bToA = panelEdgePoint(b, aCenter);
                makeConn(aToB, bToA);
            }
        }
    }

    // Implement optional SpaceInterface method: find mesh by entity name and id
    // Resolves entity name from config if it matches a known entity type (e.g., CONSTANTS.SHAPES.types -> config names.types)
    public getMeshById(entity: string, id: string): THREE.Mesh | undefined {
        const n = getNames();
        // Map common entity constants to config-resolved names
        let resolvedEntity = entity;
        if (entity === CONSTANTS.SHAPES.types) resolvedEntity = n.types;
        else if (entity === CONSTANTS.SHAPES.formats) resolvedEntity = n.formats;
        else if (entity === CONSTANTS.RESOURCES.resources) resolvedEntity = n.resources;
        else if (entity === CONSTANTS.RESOURCES.jobs) resolvedEntity = n.jobs;
        else if (entity === CONSTANTS.ROLES.roles) resolvedEntity = n.roles;
        return findMeshById(this.scene, resolvedEntity, id);
    }

    // Animation control methods

    /**
     * Start the job processing animation
     */
    public startStrategyAnimation(): void {
        // Clear any persisted selection, hover, tooltip, and highlighting before animation
        console.log('[CosmosSpace.startStrategyAnimation] Clearing interaction, interactionView exists:', !!this.interactionView, 'clearInteraction exists:', !!this.interactionView?.clearInteraction);
        try {
            if (this.interactionView?.clearInteraction) {
                this.interactionView.clearInteraction();
                console.log('[CosmosSpace.startStrategyAnimation] clearInteraction called successfully');
            } else {
                console.warn('[CosmosSpace.startStrategyAnimation] clearInteraction not available');
            }
        } catch (e) {
            console.error('[CosmosSpace.startStrategyAnimation] Error calling clearInteraction:', e);
        }
        // Reset internal tracking state to start completely fresh
        this.lastRolesJobId = null;
        this.pendingRolesJobId = null;
        this.strategyAnimation?.start();
    }

    /**
     * Stop the job processing animation and reset jobs to original positions
     */
    public stopStrategyAnimation(): void {
        this.strategyAnimation?.stop();
        this._animationHasStarted = false; // Reset flag when stopping
        // Ensure any lingering job-role connectors are cleared after animation stops
        try {
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (child.name === 'job-role-connectors') {
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
        } catch { /* ignore cleanup errors */ }
    }

    /**
     * Pause the job processing animation
     */
    public pauseStrategyAnimation(): void {
        this.strategyAnimation?.pause();
    }

    /**
     * Resume the job processing animation
     */
    public resumeStrategyAnimation(): void {
        this.strategyAnimation?.resume();
    }

    /**
     * Check if job animation is currently running
     */
    public isStrategyAnimationActive(): boolean {
        return this.strategyAnimation?.isActive() ?? false;
    }

    /**
     * Reset space to initial paused state after animation completes
     */
    private resetToInitialState(): void {
        // console.log('[RESET] Starting reset to initial state');

        // Set space to paused state
        this._animationPaused = true;

        // Do not forcibly stop/null the timeline runner here; let animation stop handle cleanup
        // Avoid hard-resetting visualization; prior stop already restores positions

        // Clear all role-resource connectors (both input and output)
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-')) {
                toRemove.push(child);
                // console.log('[RESET] Marking connector for removal:', child.name);
            }
            // Remove ALL type-resource connectors (they'll be redrawn for Natural 1)
            if (child.userData?.isConnector) {
                toRemove.push(child);
                // console.log('[RESET] Marking type-resource connector for removal');
            }
            // Remove natural panels (except the initial value 1) and their associated objects
            if (child.userData?.id?.startsWith('TYPE-Natural/mock-')) {
                const mockId = child.userData.id;
                const resourceIdPart = mockId.replace('TYPE-Natural/mock-', '');
                // Extract natural value from mock ID (e.g., "TYPE-Natural/mock-2" -> 2)
                const intValue = parseInt(resourceIdPart, 10);
                // console.log('[RESET] Found natural panel:', mockId, 'value:', intValue);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking natural panel for removal:', mockId);
                }
            }
            // Remove outlines for natural panels (except value 1)
            if (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Natural/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking outline for removal:', parentId);
                }
            }
            // Remove labels for natural panels (except value 1)
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Natural/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Natural/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking label for removal:', parentId);
                }
            }
        });

        // console.log('[RESET] Total objects marked for removal:', toRemove.length);

        // Remove all collected objects and dispose properly
        toRemove.forEach(obj => {
            obj.removeFromParent();
            // Dispose geometry and materials for meshes and lines
            if (obj instanceof THREE.Mesh) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Line || obj instanceof THREE.LineSegments) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Sprite) {
                // Dispose sprite materials and textures
                if (obj.material) {
                    const spriteMat = obj.material as THREE.SpriteMaterial;
                    if (spriteMat.map) spriteMat.map.dispose();
                    spriteMat.dispose();
                }
            }
        });

        // Clean up natural tracking maps via state manager
        this.stateManager?.resetToBaseState();

        // console.log('[RESET] Reset complete.');

        // Redraw naturalresources baseline if needed
        try { this.drawNaturals(); } catch { }

        // Re-enable all entity interactions (jobs, roles, etc.) for the reset state
        this.updateInteractorEntityFilter();

        // Dispatch custom event to notify ExplorerHost to reset button state
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('cosmos-animation-reset'));
        }
    }

    /**
     * Cleanup when space is destroyed
     */
    public override dispose(): void {
        try { this._timelineRunner?.stop(); } catch { }
        this.strategyAnimation?.stop();
        super.dispose();
    }

}
