import type { TimelineConfig, TimelinePhase, TimelineSegment, StrategyTimeline } from './TimelineTypes';

const PHASES: TimelinePhase[] = ['PAUSING', 'PULLING_IN', 'INSIDE', 'PULLING_OUT'];

export function buildTimelineFromSequence(jobSequence: string[], cfg: TimelineConfig): StrategyTimeline {
  const segments: TimelineSegment[] = [];
  let t = 0;
  let segIndex = 0;

  for (let j = 0; j < jobSequence.length; j++) {
    const jobId = jobSequence[j];

    // Inter-job pause before each job (including the first) for visual consistency
    const pauseDur = cfg.pauseBetweenJobs;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PAUSING',
      startMs: t,
      endMs: t + pauseDur,
      durationMs: pauseDur,
    });
    t += pauseDur;

    // Pulling in
    const pullInDur = cfg.pullInDuration;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PULLING_IN',
      startMs: t,
      endMs: t + pullInDur,
      durationMs: pullInDur,
    });
    t += pullInDur;

    // Inside (processing)
    const insideDur = cfg.pauseInside;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'INSIDE',
      startMs: t,
      endMs: t + insideDur,
      durationMs: insideDur,
    });
    t += insideDur;

    // Pulling out
    const pullOutDur = cfg.pullOutDuration;
    segments.push({
      index: segIndex++,
      jobIndex: j,
      jobId,
      phase: 'PULLING_OUT',
      startMs: t,
      endMs: t + pullOutDur,
      durationMs: pullOutDur,
    });
    t += pullOutDur;
  }

  return {
    segments,
    totalDurationMs: t,
    jobSequence: [...jobSequence],
  };
}

export function findSegmentAt(timeline: StrategyTimeline, timeMs: number): TimelineSegment | null {
  if (timeline.segments.length === 0) return null;
  // Linear scan is fine for short sequences; replace with binary search if needed
  for (const seg of timeline.segments) {
    if (timeMs >= seg.startMs && timeMs < seg.endMs) return seg;
  }
  // If exactly at the end, return last segment
  return timeline.segments[timeline.segments.length - 1] || null;
}

export function phaseProgress(seg: TimelineSegment, timeMs: number): number {
  if (seg.durationMs <= 0) return 1;
  return Math.max(0, Math.min(1, (timeMs - seg.startMs) / seg.durationMs));
}
