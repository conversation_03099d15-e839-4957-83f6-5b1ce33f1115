import type { StatefulStrategyJson } from '@toolproof-npm/schema';

export type TimelinePhase = 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT';

export interface TimelineConfig {
  pullInDuration: number;
  pullOutDuration: number;
  pauseBetweenJobs: number;
  pauseInside: number;
}

export interface TimelineSegment {
  // Global index across all segments
  index: number;
  // Which job in jobSequence this segment belongs to
  jobIndex: number;
  jobId: string;
  phase: TimelinePhase;
  startMs: number;
  endMs: number;
  durationMs: number;
}

export interface StrategyTimeline {
  segments: TimelineSegment[];
  totalDurationMs: number;
  jobSequence: string[];
}

export function sequenceFromStatefulStrategy(statefulStrategy: StatefulStrategyJson | null | undefined): string[] {
  if (!statefulStrategy?.statelessStrategy?.steps) return [];
  const seq: string[] = [];
  for (const step of statefulStrategy.statelessStrategy.steps) {
    const exec = (step as unknown as { execution?: { jobId?: string } }).execution;
    if (exec?.jobId) seq.push(String(exec.jobId));
  }
  return seq;
}
