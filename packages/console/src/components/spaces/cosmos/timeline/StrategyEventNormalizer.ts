import type { StatefulStrategyJson } from '@toolproof-npm/schema';
import { buildTimelineFromSequence } from './StrategyTimeline';
import { sequenceFromStatefulStrategy } from './TimelineTypes';
import type { StrategyTimeline, TimelineConfig } from './TimelineTypes';

/**
 * Converts both live strategy events and recorded strategy data into normalized timeline format.
 * This provides a single abstraction layer so the rest of the system doesn't need to distinguish
 * between live execution and playback.
 */
export class StrategyEventNormalizer {
    private statefulStrategy: StatefulStrategyJson | null = null;
    private onTimelineUpdateCallback?: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void;
    private onCompleteCallback?: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void;

    constructor(private timelineConfig: TimelineConfig) { }

    /**
     * Subscribe to incremental timeline updates (triggered on each event)
     * Used for real-time visualization of live strategies
     */
    public onTimelineUpdate(callback: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void): void {
        this.onTimelineUpdateCallback = callback;
    }

    /**
     * Subscribe to strategy completion (triggered on graph_end event)
     * Used for saving final timeline for replay
     */
    public onComplete(callback: (timeline: StrategyTimeline, spec: StatefulStrategyJson) => void): void {
        this.onCompleteCallback = callback;
    }

    /**
     * Process a live graph event from StrategyBuilder
     * Normalizes the event and updates the strategy spec incrementally
     */
    public processLiveEvent(event: { type: string; label: string; payload: unknown }): void {
        const { label, payload } = event;

        // Extract statefulStrategy if present in payload
        if (payload && typeof payload === 'object') {
            const ws = (payload as Record<string, unknown>)['statefulStrategy'] as StatefulStrategyJson | undefined;
            if (ws) {
                this.updateStatefulStrategy(ws, label === 'graph_end');
            }
        }

        // Update strategyState incrementally from step_complete events
        if (label === 'step_complete' && this.statefulStrategy) {
            const pObj = (payload && typeof payload === 'object') ? (payload as Record<string, unknown>) : null;
            const delta = pObj ? (pObj['strategyStateDelta'] as Record<string, Record<string, unknown>> | undefined) : undefined;

            if (delta) {
                // `strategyState` is the evolving executionId -> roleMap object.
                const ss = this.statefulStrategy as unknown as { strategyState?: Record<string, Record<string, unknown>> };
                if (!ss.strategyState) ss.strategyState = {};
                const base = ss.strategyState;

                // Merge each executionId's materialized/updated role entries into the base map
                for (const [executionId, roleMap] of Object.entries(delta)) {
                    const existingExec = (base[executionId] as Record<string, unknown>) || {};
                    for (const [resourceRoleId, resourceRecord] of Object.entries(roleMap)) {
                        existingExec[resourceRoleId] = resourceRecord;
                    }
                    base[executionId] = existingExec;
                }
            }

            // Regenerate timeline after resourceMap update
            this.generateTimeline(false);
        }
    }

    /**
     * Load a recorded strategy spec (creates initial timeline)
     */
    public loadRecordedStrategy(spec: StatefulStrategyJson): void {
        this.updateStatefulStrategy(spec, false);
    }

    /**
     * Update the strategy spec and generate a new timeline
     */
    private updateStatefulStrategy(spec: StatefulStrategyJson, isComplete: boolean = false): void {
        this.statefulStrategy = spec;
        this.generateTimeline(isComplete);
    }

    /**
     * Generate a timeline from the current strategy spec
     * @param isComplete - Whether this is the final timeline (graph_end) or incremental update
     */
    private generateTimeline(isComplete: boolean = false): void {
        if (!this.statefulStrategy) return;

        const sequence = sequenceFromStatefulStrategy(this.statefulStrategy);
        if (sequence.length === 0) return;

        const timeline = buildTimelineFromSequence(sequence, this.timelineConfig);

        // Notify subscribers of timeline update (for real-time visualization)
        if (this.onTimelineUpdateCallback) {
            this.onTimelineUpdateCallback(timeline, this.statefulStrategy);
        }

        // Notify completion subscribers when strategy finishes
        if (isComplete && this.onCompleteCallback) {
            this.onCompleteCallback(timeline, this.statefulStrategy);
        }
    }

    /**
     * Get the current strategy spec
     */
    public getStatefulStrategy(): StatefulStrategyJson | null {
        return this.statefulStrategy;
    }

    /**
     * Normalize event from various shapes into a standard format
     */
    public static normalizeEvent(raw: unknown): { typeLabel: string; label: string; payload: unknown } {
        let typeLabel = '';
        let payload: unknown = raw;

        if (raw && typeof raw === 'object') {
            const obj = raw as Record<string, unknown>;
            const direct = (obj['type'] || obj['event'] || obj['kind']);
            if (typeof direct === 'string' && direct.length > 0) {
                typeLabel = direct;
            } else {
                const keys = Object.keys(obj);
                if (keys.length === 1) {
                    typeLabel = keys[0];
                    payload = obj[typeLabel];
                }
            }
        }

        if (!typeLabel) typeLabel = 'unknown';

        // Normalize label to standard set
        const label = StrategyEventNormalizer.normalizeLabel(typeLabel);

        return { typeLabel, label, payload };
    }

    /**
     * Normalize event label to standard names
     */
    private static normalizeLabel(label: string): string {
        // Map various event type names to canonical labels
        const lower = label.toLowerCase();
        if (lower.includes('start') || lower.includes('begin')) {
            if (lower.includes('graph') || lower.includes('strategy')) return 'graph_start';
            if (lower.includes('run')) return 'run_start';
        }
        if (lower.includes('end') || lower.includes('finish') || lower.includes('complete')) {
            if (lower.includes('graph') || lower.includes('strategy')) return 'graph_end';
            if (lower.includes('run')) return 'run_end';
            if (lower.includes('step')) return 'step_complete';
        }
        if (lower.includes('debug')) return 'run_graph_debug';

        return label;
    }
}
