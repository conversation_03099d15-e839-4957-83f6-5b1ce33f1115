import type { StrategyTimeline, TimelineSegment } from './TimelineTypes';
import { findSegmentAt, phaseProgress } from './StrategyTimeline';

export interface TimelineRunnerOptions {
  loop?: boolean;
  loopBreakMs?: number; // extra idle time at end before restarting
}

export interface TimelineTick {
  nowMs: number; // absolute now (performance.now())
  playheadMs: number; // time within the active loop (excludes pauses)
  segment: TimelineSegment;
  t: number; // 0..1 progress within the current segment
}

export type TickHandler = (tick: TimelineTick) => void;
export type PhaseChangeHandler = (prev: TimelineSegment | null, next: TimelineSegment) => void;

export class TimelineRunner {
  private startEpoch = 0;
  private pausedAt = 0;
  private pausedAccum = 0;
  private rafId: number | null = null;
  private running = false;

  private lastSegment: TimelineSegment | null = null;

  private onTickHandlers: TickHandler[] = [];
  private onPhaseChangeHandlers: PhaseChangeHandler[] = [];

  constructor(private timeline: StrategyTimeline, private opts: TimelineRunnerOptions = {}) {}

  onTick(cb: <PERSON>ick<PERSON>and<PERSON>) { this.onTickHandlers.push(cb); return () => this.off(this.onTickHandlers, cb); }
  onPhaseChange(cb: PhaseChangeHandler) { this.onPhaseChangeHandlers.push(cb); return () => this.off(this.onPhaseChangeHandlers, cb); }

  private off<T>(arr: T[], cb: T) {
    const i = arr.indexOf(cb);
    if (i >= 0) arr.splice(i, 1);
  }

  start() {
    if (this.running) return;
    try { console.log('[TimelineRunner] start()', 'segments=', this.timeline?.segments?.length ?? 0, 'durationMs=', this.timeline?.totalDurationMs ?? 0); } catch { /* ignore */ }
    this.running = true;
    this.startEpoch = performance.now();
    this.pausedAt = 0;
    this.pausedAccum = 0;
    this.lastSegment = null;
    this.loop();
  }

  stop() {
    try { console.log('[TimelineRunner] stop()'); } catch { /* ignore */ }
    this.running = false;
    if (this.rafId != null) cancelAnimationFrame(this.rafId);
    this.rafId = null;
    this.lastSegment = null;
  }

  pause() {
    if (!this.running || this.pausedAt) return;
    try { console.log('[TimelineRunner] pause()'); } catch { /* ignore */ }
    this.pausedAt = performance.now();
  }

  resume() {
    if (!this.running || !this.pausedAt) return;
    try { console.log('[TimelineRunner] resume()'); } catch { /* ignore */ }
    const now = performance.now();
    this.pausedAccum += now - this.pausedAt;
    this.pausedAt = 0;
  }

  isRunning() { return this.running; }
  isPaused() { return this.pausedAt !== 0; }

  /**
   * Update the timeline while the runner is active (for live strategies)
   * Allows seamless continuation as new segments are added
   */
  updateTimeline(newTimeline: StrategyTimeline) {
    this.timeline = newTimeline;
    // Don't reset playhead or lastSegment - continue seamlessly
  }

  private loop = () => {
    if (!this.running) return;
    this.rafId = requestAnimationFrame(this.loop);
    if (this.pausedAt) return;

    const now = performance.now();
    const base = now - this.startEpoch - this.pausedAccum;

    const loopBreak = Math.max(this.opts.loopBreakMs ?? 0, 0);
    const span = this.timeline.totalDurationMs + (this.opts.loop ? loopBreak : 0);
    const playhead = span > 0 ? (this.opts.loop ? (base % span) : Math.min(base, this.timeline.totalDurationMs)) : 0;

    // If not looping and we've reached the end, stop the runner
    if (!this.opts.loop && base >= this.timeline.totalDurationMs) {
      // Stop after reaching the end to prevent unnecessary RAF calls
      if (base > this.timeline.totalDurationMs + 200) {
        this.stop();
        return;
      }
    }

    // During loop break, we don't emit ticks (idle frame).
    if (this.opts.loop && loopBreak > 0 && playhead > this.timeline.totalDurationMs) {
      return;
    }

    const seg = findSegmentAt(this.timeline, playhead);
    if (!seg) return;
    if (!this.lastSegment || seg.index !== this.lastSegment.index) {
      for (const cb of this.onPhaseChangeHandlers) {
        try { cb(this.lastSegment, seg); } catch { /* ignore */ }
      }
      this.lastSegment = seg;
    }

    const t = phaseProgress(seg, playhead);
    const tick = { nowMs: now, playheadMs: playhead, segment: seg, t };
    for (const cb of this.onTickHandlers) {
      try { cb(tick); } catch { /* ignore */ }
    }
  }
}
