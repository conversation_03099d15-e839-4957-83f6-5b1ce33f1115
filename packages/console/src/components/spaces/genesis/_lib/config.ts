import type { GenesisConfig } from '@/spaces/genesis/_lib/types';

/**
 * Creates default configuration for Genesis space
 */
export function makeGenesisConfig(): GenesisConfig {
	return {
		sphere: {
			radius: 0.5,
			color: 0x4488ff,
			opacity: 0.8,
			segments: 32,
		},
		layout: {
			spacing: 2.5,
			pattern: 'grid',
		},
		label: {
			fontSize: 24,
			color: '#ffffff',
			backgroundColor: 'rgba(0, 0, 0, 0.7)',
			padding: 8,
		},
	};
}
