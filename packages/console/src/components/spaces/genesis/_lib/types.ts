import type * as THREE from 'three';

/**
 * Configuration for Genesis space visualization
 */
export interface GenesisConfig {
	sphere: {
		radius: number;
		color: THREE.ColorRepresentation;
		opacity: number;
		segments: number;
	};
	layout: {
		/** Spacing between spheres */
		spacing: number;
		/** Arrange in grid or spiral */
		pattern: 'grid' | 'spiral' | 'circle';
	};
	label: {
		fontSize: number;
		color: string;
		backgroundColor: string;
		padding: number;
	};
}

/**
 * Individual subschema definition from Genesis.json $defs
 */
export interface SubschemaDefinition {
	/** Name of the subschema (e.g., "BranchStep", "ResourceFormatMeta") */
	name: string;
	/** The actual JSON Schema definition */
	schema: Record<string, unknown>;
	/** Optional anchor reference */
	anchor?: string;
}

/**
 * Data payload for Genesis space
 */
export interface GenesisSpaceData {
	/** All subschemas from Genesis.json $defs */
	subschemas: SubschemaDefinition[];
}
