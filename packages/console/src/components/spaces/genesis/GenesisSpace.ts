import type { GenesisConfig, GenesisSpaceData, SubschemaDefinition } from '@/spaces/genesis/_lib/types';
import type { ExplorerConfig } from '@toolproof-npm/visualization';
import { BaseSpace, createPortalMesh } from '@toolproof-npm/visualization';
import { getResourceTypeMap } from '@toolproof-npm/validation';
import * as THREE from 'three';

export class GenesisSpace extends BaseSpace<GenesisConfig, GenesisSpaceData> {
    private subschemas: SubschemaDefinition[] = [];
    private sphereMeshes: Map<string, THREE.Mesh> = new Map();
    private labelSprites: Map<string, THREE.Sprite> = new Map();
    private portalMesh: THREE.Group | null = null;

    constructor(
        explorerConfig: ExplorerConfig<GenesisConfig>,
        scene: THREE.Scene,
        renderer: THREE.WebGLRenderer
    ) {
        super(explorerConfig, scene, renderer);

        // Attach space reference to scene for interactor access
        this.scene.userData = this.scene.userData || {};
        this.scene.userData.space = this;
    }

    public updateData(payload: Partial<GenesisSpaceData>): void {
        console.log('[GenesisSpace] updateData called with payload:', payload);
        try {
            const { subschemas } = payload;

            if (subschemas && subschemas !== this.subschemas) {
                console.log('[GenesisSpace] Updating subschemas:', subschemas.length);
                this.subschemas = subschemas;
                this.drawScene();
            } else {
                console.log('[GenesisSpace] No subschemas to update or same reference');
            }
        } catch (err) {
            console.error('[GenesisSpace] Error updating data:', err);
        }
    }

    public drawScene(): void {
        // Clear existing meshes and sprites
        this.clearScene();

        const cfg = this.explorerConfig.space;
        const { radius, color, opacity, segments } = cfg.sphere;
        const { spacing, pattern } = cfg.layout;

        // Create geometry and material (reuse for all spheres)
        const geometry = new THREE.SphereGeometry(radius, segments, segments);
        const material = new THREE.MeshStandardMaterial({
            color,
            opacity,
            transparent: opacity < 1,
            roughness: 0.5,
            metalness: 0.3,
        });

        this.subschemas.forEach((subschema, index) => {
            const position = this.calculatePosition(index, spacing, pattern);

            // Create sphere mesh
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.copy(position);
            mesh.name = subschema.name;
            mesh.userData = { subschema, type: 'genesis-subschema' };

            this.root.add(mesh);
            this.sphereMeshes.set(subschema.name, mesh);
        });

        // Add portal to Cosmos space
        this.drawPortal();

        console.log(`[GenesisSpace] Drew ${this.subschemas.length} subschemas`);
    }

    private calculatePosition(index: number, spacing: number, pattern: 'grid' | 'spiral' | 'circle'): THREE.Vector3 {
        switch (pattern) {
            case 'grid': {
                const cols = Math.ceil(Math.sqrt(this.subschemas.length));
                const x = (index % cols) * spacing - ((cols - 1) * spacing) / 2;
                const z = Math.floor(index / cols) * spacing - ((Math.ceil(this.subschemas.length / cols) - 1) * spacing) / 2;
                return new THREE.Vector3(x, 0, z);
            }
            case 'circle': {
                const angle = (index / this.subschemas.length) * Math.PI * 2;
                const circleRadius = (this.subschemas.length * spacing) / (2 * Math.PI);
                const x = Math.cos(angle) * circleRadius;
                const z = Math.sin(angle) * circleRadius;
                return new THREE.Vector3(x, 0, z);
            }
            case 'spiral': {
                const spiralSpacing = spacing * 0.5;
                const angle = index * 0.5;
                const spiralRadius = spiralSpacing * Math.sqrt(index);
                const x = Math.cos(angle) * spiralRadius;
                const z = Math.sin(angle) * spiralRadius;
                return new THREE.Vector3(x, 0, z);
            }
            default:
                return new THREE.Vector3(0, 0, 0);
        }
    }

    private clearScene(): void {
        // Remove all sphere meshes
        this.sphereMeshes.forEach(mesh => {
            this.root.remove(mesh);
            mesh.geometry.dispose();
            if (Array.isArray(mesh.material)) {
                mesh.material.forEach(m => m.dispose());
            } else {
                mesh.material.dispose();
            }
        });
        this.sphereMeshes.clear();

        // Remove all label sprites
        this.labelSprites.forEach(sprite => {
            this.root.remove(sprite);
            sprite.material.map?.dispose();
            sprite.material.dispose();
        });
        this.labelSprites.clear();

        // Remove portal
        if (this.portalMesh) {
            this.root.remove(this.portalMesh);
            this.portalMesh = null;
        }
    }

    private drawPortal(): void {
        // Remove existing portal if any
        if (this.portalMesh) {
            this.root.remove(this.portalMesh);
            this.portalMesh = null;
        }

        const cfg = this.explorerConfig.space;
        const portalRadius = cfg.sphere.radius * 30; // Larger for visibility

        // Position portal to the left side at eye level, facing center
        const portalPosition = new THREE.Vector3(-50, 20, 0);

        // Rotate portal to face the center
        const portalRotation = new THREE.Euler(0, Math.PI / 2, 0);

        this.portalMesh = createPortalMesh({
            radius: portalRadius,
            targetSpace: 'cosmos',
            color: 0xff1111,
            position: portalPosition,
            rotation: portalRotation,
            glowIntensity: 3,
        });

        this.root.add(this.portalMesh);
        console.log(`[GenesisSpace] Created portal to Cosmos space at position (${portalPosition.x.toFixed(2)}, ${portalPosition.y.toFixed(2)}, ${portalPosition.z.toFixed(2)}) radius: ${portalRadius.toFixed(2)}`);
    }

    public override updateOnHover(): void {
        // Animate portal
        if (this.portalMesh?.userData?.animate) {
            this.portalMesh.userData.animate(0.016); // ~60fps
        }
    }

    /**
     * Show label for a subschema on hover
     */
    public showLabel(subschemaName: string): void {
        const mesh = this.sphereMeshes.get(subschemaName);
        if (!mesh) return;

        // Don't recreate if already exists
        if (this.labelSprites.has(subschemaName)) return;

        const cfg = this.explorerConfig.space.label;
        const sprite = this.createTextSprite(subschemaName, cfg);
        sprite.position.copy(mesh.position);
        sprite.position.y += this.explorerConfig.space.sphere.radius + 0.5;

        this.root.add(sprite);
        this.labelSprites.set(subschemaName, sprite);
    }

    /**
     * Hide label for a subschema
     */
    public hideLabel(subschemaName: string): void {
        const sprite = this.labelSprites.get(subschemaName);
        if (sprite) {
            this.root.remove(sprite);
            sprite.material.map?.dispose();
            sprite.material.dispose();
            this.labelSprites.delete(subschemaName);
        }
    }

    private createTextSprite(
        text: string,
        config: { fontSize: number; color: string; backgroundColor: string; padding: number }
    ): THREE.Sprite {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d')!;

        // Set font and measure text
        const fontSize = config.fontSize;
        context.font = `${fontSize}px Arial`;
        const metrics = context.measureText(text);
        const textWidth = metrics.width;

        // Set canvas size with padding
        const padding = config.padding;
        canvas.width = textWidth + padding * 2;
        canvas.height = fontSize + padding * 2;

        // Draw background
        context.fillStyle = config.backgroundColor;
        context.fillRect(0, 0, canvas.width, canvas.height);

        // Draw text
        context.font = `${fontSize}px Arial`;
        context.fillStyle = config.color;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, canvas.width / 2, canvas.height / 2);

        // Create sprite
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);

        // Scale sprite to maintain aspect ratio
        const aspect = canvas.width / canvas.height;
        sprite.scale.set(aspect * 2, 2, 1);

        return sprite;
    }

    public override dispose(): void {
        this.clearScene();
        super.dispose();
    }
}
