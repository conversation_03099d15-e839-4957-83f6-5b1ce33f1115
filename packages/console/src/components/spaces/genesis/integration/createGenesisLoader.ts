import type { GenesisSpaceData } from '@/spaces/genesis/_lib/types';
import type { Loader, InteractionContext } from '@toolproof-npm/visualization';
import { PersistentSelector, SwitchingInteractor, makeExplorerConfig, makeInteractorConfig } from '@toolproof-npm/visualization';
import { GenesisSpace } from '@/spaces/genesis/GenesisSpace';
import { makeGenesisConfig } from '@/spaces/genesis/_lib/config';
import * as THREE from 'three';

export interface GenesisLoaderProps {
	genesisSpaceData: GenesisSpaceData;
	onSpaceReady?: (space: GenesisSpace) => void;
	disableVRButton?: boolean;
}

/**
 * Creates a type-safe loader for GenesisSpace.
 * Encapsulates all React hooks and config creation logic.
 * 
 * @param props - The genesis space data and optional callbacks
 * @returns Loader for GenesisSpace
 */
export function createGenesisLoader(
	props: GenesisLoaderProps
): Loader<GenesisSpace, ReturnType<typeof makeGenesisConfig>, GenesisSpaceData, GenesisLoaderProps> {
	// Stable function/object identities to avoid re-renders causing XR jank
	const interactionPredicate = (obj: THREE.Object3D) => {
		// Allow interaction with genesis subschema spheres
		return obj.userData?.type === 'genesis-subschema';
	};

	// Keep persistent selector (selection via click)
	const activeSelector = new PersistentSelector();

	const interactorFactory = (ctx: InteractionContext) => 
		new SwitchingInteractor(ctx, { disableVRButton: props.disableVRButton });

	const interactorConfig = makeInteractorConfig(
		{ interactionPredicate, selector: activeSelector, interactorFactory },
		{
			// Genesis-specific overrides can go here
		}
	);

	const genesisConfig = makeGenesisConfig();

	const spaceFactory = (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
		const explorerConfig = makeExplorerConfig(interactorConfig, genesisConfig);
		return new GenesisSpace(
			explorerConfig,
			scene,
			renderer
		);
	};

	const dataTransform = (loaderProps: GenesisLoaderProps): Partial<GenesisSpaceData> => {
		return loaderProps.genesisSpaceData;
	};

	return {
		spaceFactory,
		dataTransform,
		props,
		onSpaceReady: props.onSpaceReady,
	};
}
