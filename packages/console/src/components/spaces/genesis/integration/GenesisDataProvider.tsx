'use client';

import type { GenesisSpaceData, SubschemaDefinition } from '@/spaces/genesis/_lib/types';
import type { ReactNode } from 'react';
import { GenesisSchema } from '@toolproof-npm/schema';
import { createContext, useContext, useMemo } from 'react';

export type GenesisDataContextValue = {
	genesisSpaceData: GenesisSpaceData;
	loading: boolean;
	error: Error | null;
};

const GenesisDataContext = createContext<GenesisDataContextValue | null>(null);

export function useGenesisData(): GenesisDataContextValue {
	const context = useContext(GenesisDataContext);
	if (!context) {
		throw new Error('useGenesisData must be used within a GenesisDataProvider');
	}
	return context;
}

interface GenesisDataProviderProps {
	children: ReactNode;
}

export default function GenesisDataProvider({ children }: GenesisDataProviderProps) {
	console.log('[GenesisDataProvider] Component rendering, GenesisSchema:', GenesisSchema);
	
	// Extract subschemas from Genesis.json $defs
	const genesisSpaceData: GenesisSpaceData = useMemo(() => {
		const defs = (GenesisSchema as { $defs?: Record<string, unknown> }).$defs || {};
		
		const subschemas: SubschemaDefinition[] = Object.entries(defs).map(([name, schema]) => {
			const anchor = (schema as { $anchor?: string })?.$anchor;
			return {
				name,
				schema: schema as Record<string, unknown>,
				anchor,
			};
		});

		console.log('[GenesisDataProvider] Extracted subschemas:', subschemas.length, 'from $defs:', Object.keys(defs).length);
		return { subschemas };
	}, []);

	const value: GenesisDataContextValue = useMemo(
		() => ({
			genesisSpaceData,
			loading: false,
			error: null,
		}),
		[genesisSpaceData]
	);

	return (
		<GenesisDataContext.Provider value={value}>
			{children}
		</GenesisDataContext.Provider>
	);
}