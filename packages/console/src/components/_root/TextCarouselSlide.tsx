'use client';

interface TextCarouselSlideProps {
  title: string;
  content: string | string[];
  backgroundColor?: string;
}

export default function TextCarouselSlide({ 
  title, 
  content, 
  backgroundColor = 'bg-gradient-to-br from-blue-50 to-indigo-100' 
}: TextCarouselSlideProps) {
  const contentArray = Array.isArray(content) ? content : [content];
  
  return (
    <div className={`w-full h-full flex items-center justify-center ${backgroundColor} overflow-y-auto overflow-x-hidden`} style={{ minWidth: 0 }}>
      <div className="text-center px-4 sm:px-6 md:px-8 max-w-4xl w-full py-8 sm:py-12 box-border mx-auto" style={{ minWidth: 0, maxWidth: '100%' }}>
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 break-words" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>{title}</h2>
        <div className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-700 leading-relaxed space-y-3 sm:space-y-4">
          {contentArray.map((paragraph, index) => (
            <p key={index} className="break-words" style={{ wordWrap: 'break-word', overflowWrap: 'break-word', hyphens: 'auto' }}>{paragraph}</p>
          ))}
        </div>
      </div>
    </div>
  );
}

