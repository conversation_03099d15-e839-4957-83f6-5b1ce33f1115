'use client';

import { ReactNode, useState, useEffect, useRef } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface CarouselProps {
  children: ReactNode | ReactNode[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showIndicators?: boolean;
  showArrows?: boolean;
  showFullscreenButton?: boolean;
  onFullscreenClick?: () => void;
  onSlideChange?: (index: number) => void;
}

export function Carousel({
  children,
  autoPlay = false,
  autoPlayInterval = 5000,
  showIndicators = true,
  showArrows = true,
  showFullscreenButton = false,
  onFullscreenClick,
  onSlideChange
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const slides = Array.isArray(children) ? children : [children];
  const touchStartX = useRef<number | null>(null);
  const touchStartY = useRef<number | null>(null);
  const touchEndX = useRef<number | null>(null);

  useEffect(() => {
    onSlideChange?.(currentIndex);
  }, [currentIndex, onSlideChange]);

  useEffect(() => {
    if (!autoPlay || slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, slides.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % slides.length);
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartY.current = e.touches[0].clientY;
    touchEndX.current = null;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX.current === null || touchStartY.current === null) return;
    
    const deltaX = Math.abs(e.touches[0].clientX - touchStartX.current);
    const deltaY = Math.abs(e.touches[0].clientY - touchStartY.current);
    
    // Only prevent default if horizontal swipe is significantly more than vertical
    // This allows vertical scrolling while enabling horizontal swiping
    if (deltaX > deltaY && deltaX > 20) {
      e.preventDefault();
    }
    
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (touchStartX.current === null || touchEndX.current === null) {
      touchStartX.current = null;
      touchStartY.current = null;
      touchEndX.current = null;
      return;
    }
    
    const distance = touchStartX.current - touchEndX.current;
    const minSwipeDistance = 50;

    if (distance > minSwipeDistance) {
      goToNext();
    } else if (distance < -minSwipeDistance) {
      goToPrevious();
    }

    touchStartX.current = null;
    touchStartY.current = null;
    touchEndX.current = null;
  };

  return (
    <div 
      className="relative w-full h-full overflow-hidden"
      style={{ maxWidth: '100vw', minWidth: 0, width: '100%' }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Slides container */}
      <div
        className="flex transition-transform duration-500 ease-in-out h-full"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div
            key={index}
            className="w-full h-full flex-shrink-0 overflow-hidden"
            style={{ minWidth: '100%', maxWidth: '100%' }}
          >
            {slide}
          </div>
        ))}
      </div>

      {/* Navigation arrows - visible on all screens but larger on desktop */}
      {showArrows && slides.length > 1 && (
        <>
          {currentIndex > 0 && (
            <button
              onClick={goToPrevious}
              className="flex absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 active:bg-white rounded-full p-2 sm:p-2.5 shadow-lg transition-all touch-manipulation"
              aria-label="Previous slide"
            >
              <ChevronLeftIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-700" />
            </button>
          )}
          {currentIndex < slides.length - 1 && (
            <button
              onClick={goToNext}
              className="flex absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 active:bg-white rounded-full p-2 sm:p-2.5 shadow-lg transition-all touch-manipulation"
              aria-label="Next slide"
            >
              <ChevronRightIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-700" />
            </button>
          )}
        </>
      )}

      {/* Indicators - visible on mobile for navigation */}
      {showIndicators && slides.length > 1 && (
        <div className="absolute bottom-4 sm:bottom-24 left-1/2 -translate-x-1/2 z-30 flex gap-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`h-2 sm:h-2.5 rounded-full transition-all touch-manipulation ${index === currentIndex
                ? 'w-8 bg-blue-600'
                : 'w-2 bg-white/60 active:bg-white/80'
                }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Fullscreen Button - only shown when showFullscreenButton is true */}
      {showFullscreenButton && onFullscreenClick && (
        <button
          onClick={onFullscreenClick}
          className="absolute bottom-16 sm:bottom-24 right-4 sm:right-6 z-50 bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white rounded-full p-3 sm:p-4 shadow-lg transition-all active:scale-95 flex items-center gap-2 touch-manipulation"
          aria-label="View explorer in fullscreen"
        >
          <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          </svg>
          <span className="hidden sm:inline font-medium text-sm sm:text-base">View Fullscreen</span>
        </button>
      )}
    </div>
  );
}

