import { useCallback } from 'react';
import type { StatefulStrategyJ<PERSON>, ExecutionJson, StrategyStateValue<PERSON>son, ResourceRoleIdJson, ResourceJson, ExecutionIdJson, ResourcePotentialInputJson } from '@toolproof-npm/schema';
import { ensureExecution, resolveResourceChain } from '@/builders/strategy/_lib/utils';

interface Params {
    statefulStrategy: StatefulStrategyJson | null;
    activeExecution: ExecutionJson | null;
    setStatefulStrategy: React.Dispatch<React.SetStateAction<StatefulStrategyJson | null>>;
}

export function useResourceBindings({ statefulStrategy, activeExecution, setStatefulStrategy }: Params) {
    const bindInputPath = useCallback((resourceRoleId: ResourceRoleIdJson, resource: ResourceJson) => {
        if (!statefulStrategy) throw new Error('statefulStrategy not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = ensureExecution(resMap, activeExecution.identity as ExecutionIdJson);
            activeExecution.roleBindings.inputBindingMap[resourceRoleId] = resource.identity;
            bucket[resourceRoleId] = resource;
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, setStatefulStrategy]);

    const bindInputPointer = useCallback((resourceRoleId: ResourceRoleIdJson, source: { executionId: ExecutionIdJson; resourceRoleId: ResourceRoleIdJson }) => {
        if (!statefulStrategy) throw new Error('statefulStrategy not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        const targetExecId = activeExecution.identity as ExecutionIdJson;
        const resMap0 = statefulStrategy.strategyState;
        const sourceEntry = resMap0?.[source.executionId]?.[source.resourceRoleId] as (ResourceJson | ResourcePotentialInputJson | undefined);
        if (!sourceEntry) throw new Error(`resourceEntry not found for source (${source.executionId}, ${source.resourceRoleId})`);
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = ensureExecution(resMap, targetExecId);
            // Try to resolve the chain starting from the source socket
            const result = resolveResourceChain(resMap, { executionId: source.executionId, resourceRoleId: source.resourceRoleId });
            if (result.status === 'materialized') {
                const materialized = result.entry;
                activeExecution.roleBindings.inputBindingMap[resourceRoleId] = materialized.identity;
                bucket[resourceRoleId] = materialized;
            } else {
                // Fallback to potential-input pointing backward
                const potentialInput: ResourcePotentialInputJson = {
                    identity: sourceEntry.identity,
                    resourceTypeId: sourceEntry.resourceTypeId,
                    creationContext: { resourceRoleId, executionId: targetExecId },
                    kind: 'potential-input',
                    pendingRef: { executionId: source.executionId, resourceRoleId: source.resourceRoleId }
                };
                activeExecution.roleBindings.inputBindingMap[resourceRoleId] = potentialInput.identity;
                bucket[resourceRoleId] = potentialInput;
            }
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, setStatefulStrategy]);

    const clearInputBinding = useCallback((resourceRoleId: ResourceRoleIdJson) => {
        if (!activeExecution) throw new Error('activeExecution not found');
        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState) };
            const bucket = resMap[activeExecution.identity];
            if (bucket && resourceRoleId in bucket) {
                delete bucket[resourceRoleId];
            }
            return { ...prev, strategyState: resMap };
        });
    }, [activeExecution, setStatefulStrategy]);

    return { bindInputPath, bindInputPointer, clearInputBinding };
}
