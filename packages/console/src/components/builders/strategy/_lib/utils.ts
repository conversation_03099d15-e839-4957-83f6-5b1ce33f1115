import type { ResourceTypeId<PERSON><PERSON>, ResourceRoleIdJson, ExecutionIdJson, ExecutionJson, StrategyStateValueJson, JobJson, RoleBindingMapJson, ResourceJson, ResourcePotentialInputJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';


// Ensure nested execution map entry exists
export function ensureExecution(strategyState: StrategyStateValueJson, executionId: ExecutionIdJson) {
    if (!strategyState[executionId]) {
        strategyState[executionId] = {} as Record<ResourceRoleIdJson, ResourceJson | ResourcePotentialInputJson | ResourcePotentialOutputJson>;
    }
    return strategyState[executionId];
}

function getUnboundRoles(roleBindingMap: RoleBindingMapJson, execution: ExecutionJson, strategyState: StrategyStateValueJson) {
    const unboundKeys: string[] = [];
    const execBucket = strategyState[execution.identity as ExecutionIdJson] || {};
    for (const roleKey of Object.keys(roleBindingMap)) {
        if (!(roleK<PERSON> in execBucket)) {
            unboundKeys.push(roleKey);
        }
    }
    return unboundKeys;
}

export function getUnboundInputs(execution: ExecutionJson, strategyState: StrategyStateValueJson) {
    return getUnboundRoles(execution.roleBindings.inputBindingMap, execution, strategyState);
}

export function getUnboundOutputs(execution: ExecutionJson, strategyState: StrategyStateValueJson) {
    return getUnboundRoles(execution.roleBindings.outputBindingMap, execution, strategyState);
}

// Produce nested outputs under executionId -> resourceRoleId
export function bindOutputs(execution: ExecutionJson, job: JobJson): StrategyStateValueJson {
    const bound: StrategyStateValueJson = {} as StrategyStateValueJson;
    const execId = execution.identity as ExecutionIdJson;
    bound[execId] = {} as Record<ResourceRoleIdJson, ResourcePotentialOutputJson>;
    const outputBindingMap = execution.roleBindings.outputBindingMap;
    for (const [resourceRoleId, resourceId] of Object.entries(outputBindingMap)) {
        const role = job.roles.outputMap?.[resourceRoleId as ResourceRoleIdJson];
        if (!role) throw new Error(`Cannot bind output for resourceRoleId ${resourceRoleId}: role not found`);
        (bound[execId] as Record<ResourceRoleIdJson, ResourcePotentialOutputJson>)[resourceRoleId as ResourceRoleIdJson] = {
            identity: resourceId,
            resourceTypeId: role.resourceTypeId as ResourceTypeIdJson,
            creationContext: { resourceRoleId: resourceRoleId as ResourceRoleIdJson, executionId: execId },
            kind: 'potential-output'
        };
    }
    return bound;
}

// Resolve a resource chain by following potential-input pointers.
// Stops when encountering a materialized entry or a potential-output.
export type ResourceSocket = { executionId: ExecutionIdJson; resourceRoleId: ResourceRoleIdJson };

export type ResolveResult =
    | { status: 'materialized'; entry: ResourceJson; path: ResourceSocket[] }
    | { status: 'blocked-output'; entry: ResourcePotentialOutputJson; path: ResourceSocket[] }
    | { status: 'unresolved'; reason: 'missing-entry' | 'cycle' | 'depth-exceeded'; path: ResourceSocket[] };

export function resolveResourceChain(
    strategyState: StrategyStateValueJson,
    start: ResourceSocket,
    opts?: { maxDepth?: number }
): ResolveResult {
    const maxDepth = opts?.maxDepth ?? 50;
    const visited = new Set<string>();
    const path: ResourceSocket[] = [];
    let current: ResourceSocket = start;

    for (let depth = 0; depth <= maxDepth; depth++) {
        path.push(current);
        const visitKey = `${current.executionId}::${current.resourceRoleId}`;
        if (visited.has(visitKey)) {
            return { status: 'unresolved', reason: 'cycle', path };
        }
        visited.add(visitKey);

        const bucket = strategyState[current.executionId];
        if (!bucket) return { status: 'unresolved', reason: 'missing-entry', path };
        const entry = bucket[current.resourceRoleId] as (
            | (ResourceJson & { kind: 'materialized' })
            | (ResourcePotentialInputJson & { kind: 'potential-input' })
            | (ResourcePotentialOutputJson & { kind: 'potential-output' })
            | undefined
        );
        if (!entry) return { status: 'unresolved', reason: 'missing-entry', path };

        if (entry.kind === 'materialized') {
            return { status: 'materialized', entry: entry as ResourceJson, path };
        }
        if (entry.kind === 'potential-output') {
            return { status: 'blocked-output', entry: entry as ResourcePotentialOutputJson, path };
        }
        // potential-input: follow pointer backwards
        if (entry.kind === 'potential-input') {
            const pointer = (entry as ResourcePotentialInputJson).pendingRef as ResourceSocket | undefined;
            if (!pointer) return { status: 'unresolved', reason: 'missing-entry', path };
            current = pointer;
            continue;
        }

        // Unknown case
        return { status: 'unresolved', reason: 'missing-entry', path };
    }
    return { status: 'unresolved', reason: 'depth-exceeded', path };
}
