import { useEffect } from 'react';
import type { StatefulStrategyJson, ExecutionJson, StrategyStateValueJson, ExecutionIdJson } from '@toolproof-npm/schema';
import type { JobJsonWithId } from '@/builders/strategy/_lib/types';
import { getUnboundInputs, getUnboundOutputs, bindOutputs, ensureExecution } from '@/builders/strategy/_lib/utils';

interface Params {
    statefulStrategy: StatefulStrategyJson | null;
    activeExecution: ExecutionJson | null;
    jobDataMap: Map<string, JobJsonWithId>;
    setStatefulStrategy: React.Dispatch<React.SetStateAction<StatefulStrategyJson | null>>;
}

// Automatically bind outputs for an execution once all inputs are bound.
export function useAutoBindOutputs({ statefulStrategy, activeExecution, jobDataMap, setStatefulStrategy }: Params) {
    useEffect(() => {
        if (!statefulStrategy) return;
        if (!activeExecution) return;
        const strategyState = statefulStrategy.strategyState;
        const unboundInputs = getUnboundInputs(activeExecution, strategyState);
        if (unboundInputs.length !== 0) return;

        const unboundOutputs = getUnboundOutputs(activeExecution, strategyState);
        if (unboundOutputs.length === 0) return; // Already bound

        const job = jobDataMap.get(activeExecution.jobId);
        if (!job) return;

        const boundOutputs = bindOutputs(activeExecution, job);

        setStatefulStrategy(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.strategyState ?? {}) } as StrategyStateValueJson;
            const execId = activeExecution.identity as ExecutionIdJson;
            const execBucket = ensureExecution(resMap, execId);
            const newBucket = boundOutputs[execId] || {};
            Object.assign(execBucket, newBucket);
            return { ...prev, strategyState: resMap };
        });
    }, [statefulStrategy, activeExecution, jobDataMap, setStatefulStrategy]);
}
