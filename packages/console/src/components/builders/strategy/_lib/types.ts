import type { <PERSON><PERSON>d<PERSON><PERSON>, <PERSON><PERSON>son, ExecutionId<PERSON><PERSON>, ResourceRoleIdJson } from '@toolproof-npm/schema';


export type  JobJsonWithId = JobJson & { id: ResourceIdJson };


// UI helper types (local)
export type SelectedIndex = {
    // Index into statefulStrategy.statelessStrategy.steps
    stepIndex: number;
    // For BranchStep only; otherwise has no meaning
    caseIndex: number | null;
};


export interface DragSource {
    executionId: ExecutionIdJson;
    resourceRoleId: ResourceRoleIdJson;
}