import type { ResourceRoleIdJson, ResourceId<PERSON>son, ResourceJson, ExecutionIdJson } from '@toolproof-npm/schema';
import type { Role } from '@toolproof-npm/shared/types';
import type { DragSource } from '@/builders/strategy/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { ensureExecution } from '@/builders/strategy/_lib/utils';
import CircleBadge from '@/builders/strategy/CircleBadge';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';
import { useResourceContext } from '@/components/builders/strategy/contexts/ResourceContext';
import { useDragContext } from '@/builders/strategy/contexts/DragContext';
import { useBindingsContext } from '@/builders/strategy/contexts/BindingsContext';
import { useMemo, useRef, useEffect, useState } from 'react';


export default function RoleBindingPanel() {
    const { activeStep, activeExecution, activeJob, selectedIndex } = useSelectionContext();
    const { resourceMap, strategyState } = useResourceContext();
    const { dragSource, setDragSource } = useDragContext();
    const { onBindInputPath, onBindInputPointer, onClearInputBinding } = useBindingsContext();
    const resourcesData: ResourceJson[] = Object.values(resourceMap).flat();

    const inputRoles = useMemo(() => {
        if (!activeJob) return [] as Role[];
        const inputsObj = activeJob.roles.inputMap ?? {};
        return Object.entries(inputsObj).map(([id, role]) => ({ id, ...role } as Role));
    }, [activeJob]);
    // console.log('inputRoles', JSON.stringify(inputRoles, null, 2));

    const outputRoles = useMemo(() => {
        if (!activeJob) return [] as Role[];
        const outputsObj = activeJob.roles.outputMap ?? {};
        return Object.entries(outputsObj).map(([id, role]) => ({ id, ...role } as Role));
    }, [activeJob]);

    const handleDropOnInput = (resourceRoleId: ResourceRoleIdJson, roleName: string, _e?: DragEvent | React.DragEvent<Element>) => {
        const source: DragSource | null = dragSource ?? null;
        console.log('handleDropOnInput', { resourceRoleId, roleName, source, selectedIndex });
        if (source != null) {
            const inputRole = inputRoles.find(r => r.id === resourceRoleId);
            const inputType = inputRole?.resourceTypeId;
            const draggedResource = strategyState?.[source.executionId]?.[source.resourceRoleId] as ResourceJson | undefined;
            const draggedResourceTypeId = draggedResource?.resourceTypeId;
            if (inputType && draggedResourceTypeId && inputType !== draggedResourceTypeId) {
                setDragSource(null);
                return; // types mismatch
            }
            onBindInputPointer(resourceRoleId as ResourceRoleIdJson, { executionId: source.executionId as ExecutionIdJson, resourceRoleId: source.resourceRoleId as ResourceRoleIdJson });
            setDragSource(null);
        }
    };

    const [pickerRole, setPickerRole] = useState<string | null>(null);
    // For WhileStep: which role id is designated as the iterating input
    const [iteratingResourceRoleId, setIteratingResourceRoleId] = useState<string | null>(null);

    // reset iterating selection when step changes
    useEffect(() => {
        setIteratingResourceRoleId(null); // ATTENTION
    }, [activeStep?.identity]);

    // Close picker when clicking anywhere in the panel outside the picker trigger/menu
    const panelBodyRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        const onDocClick = (e: MouseEvent) => {
            if (panelBodyRef.current && !panelBodyRef.current.contains(e.target as Node)) {
                setPickerRole(null);
            }
        };
        document.addEventListener('mousedown', onDocClick);
        return () => {
            document.removeEventListener('mousedown', onDocClick);
        };
    }, []);

    // console.log('activeJob', JSON.stringify(activeJob, null, 2));
    // console.log('activeExecution', JSON.stringify(activeExecution, null, 2));
    // Narrow required strategy entities after all hooks to satisfy TS and rules-of-hooks.
    if (!activeStep || !activeExecution || !activeJob) {
        return null;
    }
    const stepKind = activeStep.kind;

    return (
        <div className="h-full flex flex-col bg-red-50">
            <div className="flex-1 select-none" ref={panelBodyRef} onClick={() => setPickerRole(null)}>
                <div className="space-y-4 p-0">
                    <div className="text-sm text-gray-800 font-medium mb-2 px-0">Step: {activeStep.identity || '(unknown)'}{stepKind ? ` (${stepKind})` : ''}</div>
                    {/* Current step inputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Inputs</div>
                        <div className="flex flex-wrap gap-3">
                            {inputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No inputs</div>
                            ) : (
                                inputRoles.map((input, i) => {
                                    const inputBindingId = activeExecution.roleBindings.inputBindingMap?.[input.id as ResourceRoleIdJson] as ResourceIdJson;
                                    const isBound = !!inputBindingId && !!strategyState?.[activeExecution.identity]?.[input.id as ResourceRoleIdJson];
                                    const circleCls = isBound
                                        ? 'bg-green-500 text-white border-green-600'
                                        : 'bg-white text-green-600 border-green-500 hover:bg-green-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound) {
                                        const inputEntry = (strategyState?.[activeExecution.identity]?.[input.id as ResourceRoleIdJson] as ResourceJson | undefined) ?? undefined;
                                        const semId = inputEntry?.extractedData?.identity;
                                        // Use nullish check so 0/'' are preserved, and coerce to string for display
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`in-${input.name}-${i}`} className="flex flex-col items-center relative">
                                            <CircleBadge
                                                as="button"
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setPickerRole((prev) => (prev === input.name ? null : input.name));
                                                }}
                                                onDragOver={(e) => {
                                                    if (!dragSource) return;
                                                    const dragged = strategyState?.[dragSource.executionId]?.[dragSource.resourceRoleId] as ResourceJson | undefined;
                                                    if (dragged && dragged.resourceTypeId === input.resourceTypeId) e.preventDefault();
                                                }}
                                                onDrop={(e) => { e.preventDefault(); handleDropOnInput(input.id as ResourceRoleIdJson, input.name, e); setPickerRole(null); }}
                                                onContextMenu={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    if (isBound && inputBindingId) {
                                                        onClearInputBinding(input.id as ResourceRoleIdJson);
                                                    }
                                                }}
                                            >
                                                <span className="text-xs font-semibold">IN</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{input.name}</div>

                                            {/* If this is a WhileStep or ForStep, render an iterate toggle for one input */}

                                            {(stepKind === CONSTANTS.STEP.while || stepKind === CONSTANTS.STEP.for) && (
                                                <div className="mt-2 text-[11px]">
                                                    <label className="inline-flex items-center gap-2">
                                                        <input
                                                            type="radio"
                                                            name="iteratingRole"
                                                            checked={iteratingResourceRoleId === input.id}
                                                            onChange={() => setIteratingResourceRoleId(input.id)}
                                                        />
                                                        <span>Iterating input</span>
                                                    </label>
                                                </div>
                                            )}

                                            {pickerRole === input.name && (
                                                <div className="absolute z-10 top-12 left-full ml-2 w-64 max-h-56 overflow-auto bg-white border border-gray-200 rounded shadow-lg" onClick={(e) => e.stopPropagation()}>
                                                    <div className="sticky top-0 bg-gray-50 text-[11px] text-gray-600 px-2 py-1">Choose a file</div>
                                                    <ul className="py-1">
                                                        {resourcesData.filter((resource) => resource.resourceTypeId === input.resourceTypeId).map((resource, i) => (
                                                            <li key={i}>
                                                                <button
                                                                    className="w-full text-left px-3 py-2 text-sm hover:bg-green-50"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        onBindInputPath(input.id as ResourceRoleIdJson, resource);
                                                                        setPickerRole(null);
                                                                    }}
                                                                >
                                                                    {String(resource.extractedData.identity)}
                                                                </button>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                    <div className="border-t text-[11px] text-gray-500 px-2 py-1">Right-click input to clear</div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>

                    {/* Current step outputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Outputs</div>
                        <div className="flex flex-wrap gap-3">
                            {outputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No outputs</div>
                            ) : (
                                outputRoles.map((output, idx) => {
                                    const outputBindingId = activeExecution.roleBindings.outputBindingMap?.[output.id] as ResourceIdJson;
                                    const isBound = !!outputBindingId && !!strategyState?.[activeExecution.identity]?.[output.id as ResourceRoleIdJson];
                                    const circleCls = isBound
                                        ? 'bg-red-500 text-white border-red-600'
                                        : 'bg-white text-red-600 border-red-500 hover:bg-red-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound && outputBindingId) {
                                        const outputEntry = (strategyState?.[activeExecution.identity]?.[output.id as ResourceRoleIdJson] as ResourceJson | undefined) ?? undefined;
                                        const semId = outputEntry?.extractedData?.identity;
                                        // Use nullish check so 0/'' are preserved, and coerce to string for display
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`out-${output.name}-${idx}`} className="flex flex-col items-center">
                                            <CircleBadge
                                                as={(stepKind !== CONSTANTS.STEP.work) ? 'button' : 'div'}
                                                draggable={(stepKind !== CONSTANTS.STEP.work)}
                                                onDragStart={() => {
                                                    if (!outputBindingId) return;
                                                    const dragPayload: DragSource = { executionId: activeExecution.identity as ExecutionIdJson, resourceRoleId: output.id as ResourceRoleIdJson };
                                                    setDragSource(dragPayload);
                                                }}
                                                onDragEnd={() => setDragSource(null)}
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                            >
                                                <span className="text-xs font-semibold">OUT</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{output.name}</div>
                                        </div>
                                    )
                                })
                            )}
                        </div>
                    </div>

                    <div className="text-xs text-gray-500">
                        Tip: Drag a red output onto a green input to bind it; or click an input to choose from preset calculator json files. Right-click an input to clear.
                    </div>
                </div>
            </div>
        </div>
    );
}
