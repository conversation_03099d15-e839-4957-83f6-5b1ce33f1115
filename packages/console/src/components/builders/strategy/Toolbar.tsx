import { PlusIcon, PlayIcon, DocumentArrowDownIcon, CubeIcon } from '@heroicons/react/24/outline';

interface ValidationResult {
    isValid: boolean;
    initialInputs: string[];
}

interface ToolbarProps {
    strategyName: string;
    onStrategyNameChange: (name: string) => void;
    onDispatch: () => void;
    onViewXr: () => void;
    validationResult: ValidationResult | null;
}

export default function Toolbar({
    strategyName,
    onStrategyNameChange,
    onDispatch,
    onViewXr,
    validationResult
}: ToolbarProps) {
    return (
        <div className="bg-white border-b border-gray-200 px-3 sm:px-4 md:px-6 py-3 sm:py-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
                <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0 w-full sm:w-auto">
                    <input
                        type="text"
                        value={strategyName}
                        onChange={(e) => onStrategyNameChange(e.target.value)}
                        className="text-base sm:text-lg md:text-xl font-semibold bg-transparent border-none outline-none focus:bg-gray-50 px-2 py-1 rounded flex-1 min-w-0"
                        placeholder="Workflow Name"
                    />

                    {validationResult && (
                        <div className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm whitespace-nowrap flex-shrink-0 ${validationResult.isValid
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                            {validationResult.isValid ? '✓ Valid' : '⚠ Invalid'}
                        </div>
                    )}
                </div>

                <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                    {/* 

                    {onViewXr && (
                        <button
                            onClick={onViewXr}
                            className="flex items-center space-x-2 px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                        >
                            <CubeIcon className="w-4 h-4" />
                            <span>3D View</span>
                        </button>
                    )} */}

                    <button
                        onClick={onDispatch}
                        className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm sm:text-base flex-1 sm:flex-initial justify-center"
                        // disabled={!validationResult?.isValid}
                    >
                        <DocumentArrowDownIcon className="w-4 h-4" />
                        <span className="hidden sm:inline">Dispatch</span>
                    </button>
                </div>
            </div>

            {validationResult && !validationResult.isValid && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-800">
                        Please fix validation issues before dispatching.
                    </p>
                    {validationResult.initialInputs.length > 0 && (
                        <p className="text-sm text-red-600 mt-1">
                            Required initial inputs: {validationResult.initialInputs.join(', ')}
                        </p>
                    )}
                </div>
            )}
        </div>
    );
}
