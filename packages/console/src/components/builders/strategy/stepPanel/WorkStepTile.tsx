import { Job<PERSON>son, WorkStepJson } from '@toolproof-npm/schema';
import React from 'react';

interface WorkStepTileProps {
	job: Job<PERSON>son;
	workStep: WorkStepJson;
	isSelected: boolean;
	onClick: () => void;
}

const WorkStepTile: React.FC<WorkStepTileProps> = ({ job, workStep, isSelected, onClick }) => {
	return (
		<button
			className={`w-full text-left rounded-md px-3 py-2 border ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'} hover:border-blue-400`}
			onClick={onClick}
			title={job.name}
		>
			<div className="text-sm font-medium text-gray-800 truncate">{job.name || 'Job'}</div>
			<div className="text-[11px] text-gray-500 truncate">{workStep.identity}</div>
		</button>
	);
};

export default WorkStepTile;
