import React, { createContext, useContext } from 'react';
import type { DragSource } from '@/builders/strategy/_lib/types';

export interface DragContextValue {
  dragSource: DragSource | null;
  setDragSource: React.Dispatch<React.SetStateAction<DragSource | null>>;
}

const DragContext = createContext<DragContextValue | undefined>(undefined);

export const DragProvider: React.FC<React.PropsWithChildren<{ value: DragContextValue }>> = ({ value, children }) => (
  <DragContext.Provider value={value}>{children}</DragContext.Provider>
);

export function useDragContext(): DragContextValue {
  const ctx = useContext(DragContext);
  if (!ctx) throw new Error('useDragContext must be used within a DragProvider');
  return ctx;
}