
import type { SelectionContextValue } from '@/builders/strategy/contexts/SelectionContext';
import type { RoleExecutionContextValue } from '@/builders/strategy/contexts/RoleExecutionContext';
import type { ResourceContextValue } from '@/components/builders/strategy/contexts/ResourceContext';
import type { BindingsContextValue } from '@/builders/strategy/contexts/BindingsContext';
import type { DragContextValue } from '@/builders/strategy/contexts/DragContext';

import { SelectionProvider } from '@/builders/strategy/contexts/SelectionContext';
import { RoleExecutionProvider } from '@/builders/strategy/contexts/RoleExecutionContext';
import { ResourceProvider } from '@/components/builders/strategy/contexts/ResourceContext';
import { BindingsProvider } from '@/builders/strategy/contexts/BindingsContext';
import { DragProvider } from '@/builders/strategy/contexts/DragContext';


interface StrategyProvidersProps {
  selection: SelectionContextValue;
  roleExecution: RoleExecutionContextValue;
  resourceData: ResourceContextValue;
  bindings: BindingsContextValue;
  drag: DragContextValue;
  children: React.ReactNode;
}

export function StrategyProviders({ selection, roleExecution, resourceData, bindings, drag, children }: StrategyProvidersProps) {
  return (
    <SelectionProvider value={selection}>
      <RoleExecutionProvider value={roleExecution}>
        <ResourceProvider value={resourceData}>
          <BindingsProvider value={bindings}>
            <DragProvider value={drag}>{children}</DragProvider>
          </BindingsProvider>
        </ResourceProvider>
      </RoleExecutionProvider>
    </SelectionProvider>
  );
}

export default StrategyProviders;