import React, { createContext, useContext } from 'react';
import type { ResourceRoleIdJson, ResourceDataJson, ExecutionIdJson } from '@toolproof-npm/schema';

export interface BindingsContextValue {
  onBindInputPath: (resourceRoleId: ResourceRoleIdJson, resourceData: ResourceDataJson) => void;
  onBindInputPointer: (resourceRoleId: ResourceRoleIdJson, source: { executionId: ExecutionIdJson; resourceRoleId: ResourceRoleIdJson }) => void;
  onClearInputBinding: (resourceRoleId: ResourceRoleIdJson) => void;
}

const BindingsContext = createContext<BindingsContextValue | undefined>(undefined);

export const BindingsProvider: React.FC<React.PropsWithChildren<{ value: BindingsContextValue }>> = ({ value, children }) => (
  <BindingsContext.Provider value={value}>{children}</BindingsContext.Provider>
);

export function useBindingsContext(): BindingsContextValue {
  const ctx = useContext(BindingsContext);
  if (!ctx) throw new Error('useBindingsContext must be used within a BindingsProvider');
  return ctx;
}