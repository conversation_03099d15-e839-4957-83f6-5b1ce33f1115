import React, { createContext, useContext } from 'react';
import type { ExecutionJson, ExecutionIdJson } from '@toolproof-npm/schema';
import type { Role } from '@toolproof-npm/shared/types';

export interface RoleExecutionContextValue {
  roleMap: Map<string, Role>;
  executionMap: Map<ExecutionIdJson, ExecutionJson>;
}

const RoleExecutionContext = createContext<RoleExecutionContextValue | undefined>(undefined);

export const RoleExecutionProvider: React.FC<React.PropsWithChildren<{ value: RoleExecutionContextValue }>> = ({ value, children }) => (
  <RoleExecutionContext.Provider value={value}>{children}</RoleExecutionContext.Provider>
);

export function useRoleExecutionContext(): RoleExecutionContextValue {
  const ctx = useContext(RoleExecutionContext);
  if (!ctx) throw new Error('useRoleExecutionContext must be used within a RoleExecutionProvider');
  return ctx;
}