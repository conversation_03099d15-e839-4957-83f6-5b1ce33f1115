import React, { createContext, useContext } from 'react';
import type { StepJson, ExecutionJson } from '@toolproof-npm/schema';
import type { JobJsonWithId, SelectedIndex } from '@/builders/strategy/_lib/types';

export interface SelectionContextValue {
  activeStep: StepJson | null;
  activeExecution: ExecutionJson | null;
  activeJob: JobJsonWithId | null;
  selectedIndex: SelectedIndex | null;
  excludeCaseStepIds: string[];
}

const SelectionContext = createContext<SelectionContextValue | undefined>(undefined);

export const SelectionProvider: React.FC<React.PropsWithChildren<{ value: SelectionContextValue }>> = ({ value, children }) => (
  <SelectionContext.Provider value={value}>{children}</SelectionContext.Provider>
);

export function useSelectionContext(): SelectionContextValue {
  const ctx = useContext(SelectionContext);
  if (!ctx) throw new Error('useSelectionContext must be used within a SelectionProvider');
  return ctx;
}