import React, { createContext, useContext } from 'react';
import type { StrategyStateValueJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';

export interface ResourceContextValue {
  resourceMap: ResourceMap;
  strategyState: StrategyStateValueJson;
}

const ResourceContext = createContext<ResourceContextValue | undefined>(undefined);

export const ResourceProvider: React.FC<React.PropsWithChildren<{ value: ResourceContextValue }>> = ({ value, children }) => (
  <ResourceContext.Provider value={value}>{children}</ResourceContext.Provider>
);

export function useResourceContext(): ResourceContextValue {
  const ctx = useContext(ResourceContext);
  if (!ctx) throw new Error('useResourceContext must be used within a ResourceProvider');
  return ctx;
}