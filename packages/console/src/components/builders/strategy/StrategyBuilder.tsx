'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Branch<PERSON>tep<PERSON><PERSON>, StatefulStrategy<PERSON><PERSON>, ResourceId<PERSON>son, WorkStepIdJson, ExecutionIdJson, ResourceRoleIdJson, RoleBindingMapJson, JobJson } from '@toolproof-npm/schema';
import type { JobJsonWithId, SelectedIndex } from '@/builders/strategy/_lib/types';
import type { Loader, LoaderMap, SpaceInterface } from '@toolproof-npm/visualization';
import type { Role } from '@toolproof-npm/shared/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getUiContext } from '@/builders/_lib/utils';
import JobCanvas from '@/builders/strategy/JobCanvas';
import Toolbar from '@/builders/strategy/Toolbar';
import Sidebar from '@/builders/strategy/Sidebar';
// Split context providers (selection, role/execution, resource data, bindings, drag)
import StrategyProviders from '@/components/builders/strategy/contexts/StrategyProviders';
import { getUnboundInputs } from '@/builders/strategy/_lib/utils';
import { useStrategySelection } from '@/components/builders/strategy/_lib/useStrategySelection';
import { collectJobIds, buildExecutionMap } from '@/components/builders/strategy/_lib/strategyTraversal';
import { useAutoBindOutputs } from '@/builders/strategy/_lib/useAutoBindOutputs';
import { useResourceBindings } from '@/builders/strategy/_lib/useResourceBindings';
import { getNewId } from '@/_lib/server/firebaseAdminHelpers';
// import { validateExecution, validateStrategyState } from '@toolproof-npm/validation';
import { ExplorerHost } from '@toolproof-npm/visualization';
import { createCosmosLoader } from '@/spaces/cosmos/integration/createCosmosLoader';
import { uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useCosmosData } from '@/spaces/cosmos/integration/CosmosDataProvider';
import type { DragSource } from '@/builders/strategy/_lib/types';


export default function StrategyBuilder() {
    const { cosmosSpaceData } = useCosmosData();
    const [showExplorer, setShowExplorer] = useState(false);

    const [statefulStrategy, setStatefulStrategy] = useState<StatefulStrategyJson | null>(null);
    const [statefulStrategyId, setStatefulStrategyId] = useState<string | null>(null);
    const [statelessStrategyId, setStatelessStrategyId] = useState<string | null>(null);
    const [strategyName, setStrategyName] = useState('Untitled Strategy'); // ATTENTION: StatelessStrategy is not a Documented

    const resourceMap = cosmosSpaceData.resourceMap;

    // console.log('resourceMap:', JSON.stringify(resourceMap, null, 2));

    // Build jobs map from ResourceMap['TYPE-Job']
    const jobDataMap = useMemo(() => {
        // DOC: jobs are resources under the special type 'TYPE-Job'
        const jobResources = resourceMap[CONSTANTS.SPECIALS.TYPE_Job] ?? [];
        return jobResources.reduce((map, res) => {
            const job = res.extractedData as unknown as JobJson;
            map.set(res.identity as ResourceIdJson, { ...job, id: res.identity as ResourceIdJson } as JobJsonWithId);
            return map;
        }, new Map<ResourceIdJson, JobJsonWithId>());
    }, [resourceMap]);

    const [workStepMap, setWorkStepMap] = useState<Map<string, WorkStepJson>>(new Map()); // ATTENTION: is workStepMap still needed?

    // DOC: Track selected step (and optional branch case) for StepPanel/RoleBindingPanel
    const [selectedIndex, setSelectedIndex] = useState<SelectedIndex | null>(null);

    const { activeStep, activeExecution, activeJob } = useStrategySelection(statefulStrategy, selectedIndex, jobDataMap);

    // Global drag source state (shared between RoleBindingPanel and StrategyStatePanel)
    const [dragSource, setDragSource] = useState<DragSource | null>(null);

    // Build a global role map across ALL jobs referenced in the strategy
    const roleMap = useMemo(() => {
        const map = new Map<string, Role>();
        const jobIds = collectJobIds(statefulStrategy);
        for (const jId of jobIds) {
            const job = jobDataMap.get(jId);
            if (!job) continue;
            Object.entries(job.roles.inputMap ?? {}).forEach(([rid, role]) => map.set(rid, { id: rid as ResourceRoleIdJson, ...role } as Role));
            Object.entries(job.roles.outputMap ?? {}).forEach(([rid, role]) => map.set(rid, { id: rid as ResourceRoleIdJson, ...role } as Role));
        }
        return map;
    }, [statefulStrategy, jobDataMap]);

    // For branch steps: compute sibling case step ids to exclude from resource panel
    const excludeCaseStepIds = useMemo(() => {
        if (!activeStep || activeStep.kind !== CONSTANTS.STEP.branch || !selectedIndex) return [] as string[];
        const branch = activeStep as BranchStepJson;
        const cases = branch.cases ?? [];
        const rawIdx = selectedIndex.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        return cases
            .map(cw => (cw?.what?.identity as string | undefined))
            .filter((id, idx) => typeof id === 'string' && idx !== caseIdx) as string[];
    }, [activeStep, selectedIndex]);

    // Build a global execution map for classifying resources (input/output) by their origin
    const executionMap = useMemo(() => buildExecutionMap(statefulStrategy), [statefulStrategy]);

    // DOC: Generate statefulStrategyId and statelessStrategyId if not present
    useEffect(() => {

        const asyncWrapper = async () => {
            // Ensure we have a statefulStrategy id
            if (!statefulStrategyId) {
                const id = await getNewId('STATEFUL_STRATEGY');
                setStatefulStrategyId(id);
            }

            // Ensure we have a statelessStrategy id as well
            if (!statelessStrategyId) {
                const id = await getNewId('STATELESS_STRATEGY');
                setStatelessStrategyId(id);
            }
        };

        asyncWrapper();

    }, [statefulStrategyId, statelessStrategyId]);

    // DOC: When both ids are available, initialize statefulStrategy
    useEffect(() => {
        let mounted = true;
        if (statefulStrategyId && statelessStrategyId) {
            // Only set if not already set or if ids don't match
            setStatefulStrategy((prev) => {
                if (!mounted) return prev;
                if (!prev || prev.identity !== statefulStrategyId || prev.statelessStrategy?.identity !== statelessStrategyId) {
                    return {
                        identity: statefulStrategyId,
                        statelessStrategy: {
                            identity: statelessStrategyId,
                            steps: []
                        },
                        strategyState: {}
                    };
                }
                return prev;
            });
        }
        return () => {
            mounted = false;
        };
    }, [statefulStrategyId, statelessStrategyId, setStatefulStrategy]);

    // Auto-bind outputs when all inputs are bound
    useAutoBindOutputs({ statefulStrategy, activeExecution, jobDataMap, setStatefulStrategy });

    // Resource binding helpers
    const { bindInputPath, bindInputPointer, clearInputBinding } = useResourceBindings({ statefulStrategy, activeExecution, setStatefulStrategy });

    // DOC: Helper function to construct a WorkStep object from an jobMetaJson (does not append to statelessStrategy.steps)
    const makeWorkStepFromJob = useCallback(async (job: JobJsonWithId): Promise<WorkStepJson> => {
        if (!statefulStrategy) throw new Error('statefulStrategy not initialized');
        if (activeExecution) {
            const unbound = getUnboundInputs(activeExecution, statefulStrategy.strategyState);
            if (unbound.length > 0) {
                alert(`Provide inputs for the current step before adding another. Missing: ${unbound.join(', ')}`);
                throw new Error('unbound inputs');
            }
        }

        const workStepId = await getNewId(CONSTANTS.STEP.work) as WorkStepIdJson;
        const executionId = workStepId.replace(CONSTANTS.STEP.work.toUpperCase(), 'execution'.toUpperCase()) as ExecutionIdJson; // ATTENTION: use function

        const inputBindingMap: RoleBindingMapJson = {};
        const inputs = job.roles.inputMap;
        Object.keys(inputs).forEach((resourceRoleId) => {
            const resourceId = 'RESOURCE-X'; // DOC: Placeholder until user binds input
            inputBindingMap[resourceRoleId as ResourceRoleIdJson] = resourceId;
        });

        const outputBindingMap: RoleBindingMapJson = {};
        const outputs = job.roles.outputMap;
        Object.keys(outputs).forEach(async (resourceRoleId) => {
            const resourceId = await getNewId(CONSTANTS.RESOURCES.resources) as ResourceIdJson;
            outputBindingMap[resourceRoleId as ResourceRoleIdJson] = resourceId;
        });

        const newWorkStep: WorkStepJson = {
            identity: workStepId,
            kind: CONSTANTS.STEP.work,
            execution: {
                identity: executionId,
                jobId: job.id as ResourceIdJson,
                roleBindings: {
                    inputBindingMap: inputBindingMap,
                    outputBindingMap: outputBindingMap
                }
            }
        };

        return newWorkStep;

    }, [activeExecution, statefulStrategy]);

    // DOC: Handler for dropping an job onto the 'WorkStep' area of the JobCanvas
    const onDropWorkStep = useCallback(async (job: JobJsonWithId) => {
        try {
            const newWorkStep = await makeWorkStepFromJob(job);
            if (!statefulStrategyId || !statelessStrategyId) return;
            setStatefulStrategy((prev) => {
                const base = prev ?? {
                    identity: statefulStrategyId,
                    statelessStrategy: {
                        identity: statelessStrategyId,
                        steps: []
                    },
                    strategyState: {}
                };
                return {
                    ...base,
                    statelessStrategy: { ...base.statelessStrategy, steps: [...(base.statelessStrategy.steps ?? []), newWorkStep] }
                };
            });
            setWorkStepMap(prev => new Map(prev).set(newWorkStep.identity ?? '', newWorkStep));
            setSelectedIndex((prev) => (prev == null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            // makeWorkStepFromJob already alerts for input binding issues; ignore errors
            console.warn('Failed to create workStep:', err);
        }
    }, [makeWorkStepFromJob, statefulStrategyId, statelessStrategyId]);

    // DOC: Handler for dropping an job onto the 'BranchStep' area of the JobCanvas
    const onDropBranchStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropBranchStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for dropping an job onto the 'Add Case' area of a BranchStepTile
    const onAddBranchStepCase = useCallback((jobId: ResourceIdJson) => {
    }, []);

    // DOC: Handler for dropping an job onto the 'WhileStep' area of the JobCanvas
    const onDropWhileStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropWhileStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for dropping an job onto the 'ForStep' area of the JobCanvas
    const onDropForStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropForStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for clicking an job in JobCanvas (currently just logs the job, can be expanded to show job details)
    const handleJobClick = useCallback(async (job: JobJsonWithId) => {
        console.log('Job clicked:', job.name);
    }, []);

    // DOC: Allow clicking a step in StepPanel to change selection
    const handleSelectStep = useCallback((index: SelectedIndex) => {
        if (!statefulStrategy) return;
        setSelectedIndex(index);
    }, [statefulStrategy]);

    // DOC: Handler for dispatching statefulStrategy to the Engine for execution (currently it's executed directly, but in the future it will be scheduled)
    const handleDispatchStrategy = useCallback(async () => {
        // Guard: only proceed when statefulStrategy is initialized
        if (!statefulStrategy) return;

        // Hint recorder classification before CosmosSpace attaches
        try { (window as unknown as { latestStatefulStrategy?: StatefulStrategyJson | null }).latestStatefulStrategy = statefulStrategy; } catch { /* ignore */ }

        // Ensure Explorer (CosmosSpace) mounts before starting the stream
        if (!showExplorer) {
            setShowExplorer(true);
            // Small delay to allow listener attachment
            await new Promise(resolve => setTimeout(resolve, 150));
        }

        const statefulStrategyMock = {
            'identity': 'STATEFUL_STRATEGY-nPavImsl9vOOjs2BXFAt',
            'statelessStrategy': {
                'identity': 'STATELESS_STRATEGY-fS6EbDxmW4JMRCDhCue8',
                'steps': [
                    {
                        'identity': 'WORK-32V4Rxc3xXwGZSIml7r3',
                        'kind': 'work',
                        'execution': {
                            'identity': 'EXECUTION-32V4Rxc3xXwGZSIml7r3',
                            'jobId': 'RESOURCE-ontpa4TFOaRFegKAaXZE',
                            'roleBindings': {
                                'inputBindingMap': {
                                    'ROLE-22l2GgttEtvnnQSYdg6h': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                                    'ROLE-c6VOzBwzoF6MbeVGL5xE': 'RESOURCE-wE8ERPi8k25bM16KXV9Q'
                                },
                                'outputBindingMap': {
                                    'ROLE-IMRry7yMBc1fe0Pur4xl': 'RESOURCE-oGb5nyHFh7bUUb1Afl1B'
                                }
                            }
                        }
                    }
                ]
            },
            'strategyState': {
                'EXECUTION-32V4Rxc3xXwGZSIml7r3': {
                    'ROLE-22l2GgttEtvnnQSYdg6h': {
                        'identity': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                        'resourceTypeId': 'TYPE-Natural',
                        'creationContext': {
                            'resourceRoleId': 'ROLE-Builder',
                            'executionId': 'EXECUTION-eJ1alasXsIf9wW20Hxxt'
                        },
                        'kind': 'materialized',
                        'path': 'TYPE-Natural/85149ba8dfb3fea64a69448f5dff9e8e0ba215e8a7d95344800a01e9fdfde2e4',
                        'timestamp': '2025-12-14T14:59:38.215Z',
                        'extractedData': {
                            'identity': 1
                        }
                    },
                    'ROLE-c6VOzBwzoF6MbeVGL5xE': {
                        'identity': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                        'resourceTypeId': 'TYPE-Natural',
                        'creationContext': {
                            'resourceRoleId': 'ROLE-Builder',
                            'executionId': 'EXECUTION-eJ1alasXsIf9wW20Hxxt'
                        },
                        'kind': 'missing'
                    },
                    'ROLE-IMRry7yMBc1fe0Pur4xl': {
                        'identity': 'RESOURCE-oGb5nyHFh7bUUb1Afl1B',
                        'resourceTypeId': 'TYPE-Natural',
                        'creationContext': {
                            'resourceRoleId': 'ROLE-IMRry7yMBc1fe0Pur4xl',
                            'executionId': 'EXECUTION-32V4Rxc3xXwGZSIml7r3'
                        },
                        'kind': 'potential-output'
                    }
                }
            }
        };

        // console.log('Dispatching statefulStrategy:', JSON.stringify(statefulStrategy, null, 2));
        // return;

        try {
            const res = await fetch('/api/run-graph', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ statefulStrategy: statefulStrategyMock }),
            });
            if (!res.body) {
                console.warn('No response body for streaming run-graph');
                return;
            }
            const reader = res.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                buffer += decoder.decode(value, { stream: true });
                let idx;
                while ((idx = buffer.indexOf('\n')) >= 0) {
                    const line = buffer.slice(0, idx).trim();
                    buffer = buffer.slice(idx + 1);
                    if (!line) continue;
                    try {
                        const event = JSON.parse(line);

                        // Handle interrupt events
                        if (event.type === 'interrupt') {
                            console.log('Interrupt detected:', event);
                            const userResponse = prompt(event.message);
                            if (userResponse) {
                                // Resume the graph with user response
                                await handleResumeGraph(event.threadId, userResponse);
                            }
                            return; // Exit current stream
                        }

                        if (typeof window !== 'undefined') {
                            const detail = { __live: true, ...event };
                            window.dispatchEvent(new CustomEvent('toolproof:graphEvent', { detail }));
                        }
                    } catch (e) {
                        console.warn('Failed to parse graph event line:', e);
                    }
                }
            }
            // Flush any trailing data
            if (buffer.trim()) {
                try {
                    const event = JSON.parse(buffer.trim());
                    if (typeof window !== 'undefined') {
                        const detail = { __live: true, ...event };
                        window.dispatchEvent(new CustomEvent('toolproof:graphEvent', { detail }));
                    }
                } catch { /* ignore */ }
            }
        } catch (e) {
            console.error('Streaming /api/run-graph failed:', e);
        }
    }, [statefulStrategy, showExplorer]);

    // DOC: Handler for resuming graph execution after interrupt
    const handleResumeGraph = useCallback(async (threadId: string, userResponse: string) => {
        try {
            const res = await fetch('/api/resume-graph', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ threadId, userResponse }),
            });
            if (!res.body) {
                console.warn('No response body for streaming resume-graph');
                return;
            }
            const reader = res.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                buffer += decoder.decode(value, { stream: true });
                let idx;
                while ((idx = buffer.indexOf('\n')) >= 0) {
                    const line = buffer.slice(0, idx).trim();
                    buffer = buffer.slice(idx + 1);
                    if (!line) continue;
                    try {
                        const event = JSON.parse(line);

                        // Handle another interrupt
                        if (event.type === 'interrupt') {
                            console.log('Another interrupt detected:', event);
                            const nextUserResponse = prompt(event.message);
                            if (nextUserResponse) {
                                await handleResumeGraph(event.threadId, nextUserResponse);
                            }
                            return;
                        }

                        if (typeof window !== 'undefined') {
                            const detail = { __live: true, ...event };
                            window.dispatchEvent(new CustomEvent('toolproof:graphEvent', { detail }));
                        }
                    } catch (e) {
                        console.warn('Failed to parse resume graph event line:', e);
                    }
                }
            }
        } catch (e) {
            console.error('Streaming /api/resume-graph failed:', e);
        }
    }, []);

    // DOC: Handler for binding an inputRole to a file path
    // Alias hook fns under stable names for downstream components
    const onBindInputPath = bindInputPath;
    const onBindInputPointer = bindInputPointer;
    const onClearInputBinding = clearInputBinding;

    // Memoized slices for split contexts
    const selectionValue = useMemo(() => ({
        activeStep,
        activeExecution,
        activeJob,
        selectedIndex,
        excludeCaseStepIds
    }), [activeStep, activeExecution, activeJob, selectedIndex, excludeCaseStepIds]);

    const dragValue = useMemo(() => ({ dragSource, setDragSource }), [dragSource, setDragSource]);

    const resourceDataValue = useMemo(() => ({
        resourceMap,
        strategyState: (statefulStrategy?.strategyState ?? {})
    }), [resourceMap, statefulStrategy?.strategyState]);

    const bindingsValue = useMemo(() => ({
        onBindInputPath,
        onBindInputPointer,
        onClearInputBinding
    }), [onBindInputPath, onBindInputPointer, onClearInputBinding]);

    const roleExecutionValue = useMemo(() => ({ roleMap, executionMap }), [roleMap, executionMap]);

    if (!statefulStrategy) {
        return null;
    }

    /* const { isValid: isValidExecution, errors: errorsExecution } = validateExecution(activeExecution, activeJob, getUiContext());

    const { isValid: isValidStrategyState, errors: errorsStrategyState } = validateStrategyState(statefulStrategy.strategyState, activeExecution, getUiContext()); */

    // console.log('Execution validation:', isValidExecution, errorsExecution);
    // console.log('StrategyState validation:', isValidStrategyState, errorsStrategyState);

    return (
        <div className="flex flex-col bg-gray-50 min-h-[600px]">
            {/* Toolbar */}
            <Toolbar
                strategyName={strategyName}
                onStrategyNameChange={setStrategyName}
                onDispatch={handleDispatchStrategy}
                onViewXr={() => setShowExplorer(!showExplorer)}
                validationResult={null} // ATTENTION: should apply to validation of the whole StatefulStrategy
            />

            <div className="flex flex-col md:flex-row" style={{ minHeight: 'calc(100vh - 400px)' }}>
                <div className="flex-1 relative min-w-0">
                    {showExplorer ? (() => {
                        const cosmosLoader = createCosmosLoader({ cosmosSpaceData });
                        const genericLoader: Loader<SpaceInterface, unknown, unknown, unknown> = {
                            spaceFactory: cosmosLoader.spaceFactory,
                            // Bridge data into updateData without relying on typed props/dataTransform
                            dataTransform: () => (cosmosSpaceData as unknown as Partial<unknown>),
                            props: {},
                        };
                        const loaderMap: LoaderMap = new Map();
                        loaderMap.set('cosmos', genericLoader);
                        return (
                            <ExplorerHost loaderMap={loaderMap} initialSpaceKey="cosmos">
                                {/* No HUD or controls needed in StrategyBuilder */}
                            </ExplorerHost>
                        );
                    })() : (
                        <JobCanvas
                            jobDataMap={jobDataMap}
                            onJobClick={handleJobClick}
                        />
                    )}
                </div>
                {!showExplorer && (
                    <StrategyProviders
                        selection={selectionValue}
                        roleExecution={roleExecutionValue}
                        resourceData={resourceDataValue}
                        bindings={bindingsValue}
                        drag={dragValue}
                    >
                        <Sidebar
                            steps={statefulStrategy.statelessStrategy.steps}
                            workStepMap={workStepMap}
                            jobDataMap={jobDataMap}
                            selectedIndex={selectedIndex}
                            onSelectStep={handleSelectStep}
                            onAddBranchStepCase={onAddBranchStepCase}
                        />
                    </StrategyProviders>
                )}
            </div>
            {!showExplorer && (
                <div className="bg-white border-t border-gray-200">
                    <div className="max-w-full mx-auto px-2 sm:px-4 py-3">
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4">
                            {['WorkStep', 'BranchStep', 'WhileStep', 'ForStep'].map((name) => {
                                return (
                                    <div
                                        key={name}
                                        onDragOver={(e) => e.preventDefault()}
                                        onDrop={async (e) => {
                                            e.preventDefault();
                                            const jobId = e.dataTransfer?.getData('application/toolproof-job-id') as ResourceIdJson;
                                            if (!jobId) return;
                                            const job = jobDataMap.get(jobId) as JobJsonWithId;
                                            if (!job) {
                                                console.warn('Dropped job id not found:', jobId);
                                                return;
                                            }
                                            if (name === 'WorkStep') {
                                                // Reuse existing click logic when dropping onto WorkStep
                                                await onDropWorkStep(job as JobJsonWithId);
                                            } else if (name === 'BranchStep') {
                                                await onDropBranchStep(job as JobJsonWithId);
                                            } else if (name === 'WhileStep') {
                                                await onDropWhileStep(job as JobJsonWithId);
                                            } else if (name === 'ForStep') {
                                                await onDropForStep(job as JobJsonWithId);
                                            } else {
                                                console.log(`Canceling step creation for job ${job.name}`);
                                            }
                                        }}
                                        className="min-h-[64px] flex items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 p-3"
                                    >
                                        <div className="text-sm font-medium text-gray-700">{name}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );

}
