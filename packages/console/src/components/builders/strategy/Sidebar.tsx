import React from 'react';
import type { ResourceId<PERSON><PERSON>, StepJson, WorkStepJson } from '@toolproof-npm/schema';
import type { JobJsonWithId, SelectedIndex } from '@/builders/strategy/_lib/types';
import StepPanel from '@/builders/strategy/stepPanel/StepPanel';
import StrategyStatePanel from '@/builders/strategy/StrategyStatePanel';
import RoleBindingPanel from '@/builders/strategy/RoleBindingPanel';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';

interface SidebarProps {
  steps: StepJson[];
  workStepMap: Map<string, WorkStepJson>;
  jobDataMap: Map<ResourceIdJson, JobJsonWithId>;
  selectedIndex: SelectedIndex | null;
  onSelectStep: (index: SelectedIndex) => void;
  onAddBranchStepCase: (jobId: ResourceIdJson) => void;
}

export default function Sidebar(props: SidebarProps) {
  const {
    steps,
    workStepMap,
    jobDataMap,
    selectedIndex,
    onSelectStep,
    onAddBranchStepCase,
  } = props;
  const { activeStep, activeExecution, activeJob, excludeCaseStepIds } = useSelectionContext();

  return (
    <>
      {/* Step list */}
      <div className="w-full md:w-80 bg-white border-t md:border-t-0 md:border-l border-gray-200 flex-shrink-0">
        <StepPanel
          steps={steps}
          workStepMap={workStepMap}
          jobMap={jobDataMap}
          selectedIndex={selectedIndex}
          onSelect={onSelectStep}
          onAddBranchStepCase={onAddBranchStepCase}
        />
      </div>

      {/* Resource bindings */}
      {(activeStep && activeExecution && activeJob && selectedIndex) ? (
        <div className="w-full md:w-96 bg-white border-t md:border-l border-gray-200 flex flex-col flex-shrink-0">
          <div className="p-3 sm:p-4 border-b border-gray-200">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900">Resources</h2>
            <p className="text-xs text-gray-500">Drag outputs to inputs or pick files.</p>
          </div>
          <div className="flex-1 overflow-auto p-3 sm:p-4 space-y-4">
            <StrategyStatePanel excludeStepIds={excludeCaseStepIds} />
            <RoleBindingPanel />
          </div>
        </div>
      ) : null}
    </>
  );
}
