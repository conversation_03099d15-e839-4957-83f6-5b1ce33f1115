import { JobJsonWithId } from '@/builders/strategy/_lib/types';
import { useRef, useMemo, useLayoutEffect, useState } from 'react';

interface JobCanvasProps {
    jobDataMap: Map<string, JobJsonWithId>;
    onJobClick: (job: JobJsonWithId) => void;
}

export default function JobCanvas({
    jobDataMap,
    onJobClick
}: JobCanvasProps) {
    const canvasRef = useRef<HTMLDivElement>(null);
    const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });

    const availableJobs = useMemo(() => Array.from(jobDataMap.values()), [jobDataMap]);

    useLayoutEffect(() => {
        const element = canvasRef.current;
        if (!element) return;
        const update = () => setCanvasSize({ width: element.clientWidth, height: element.clientHeight });
        update();
        const ro = new ResizeObserver(update);
        ro.observe(element);
        return () => ro.disconnect();
    }, []);

    const center = useMemo(() => ({ x: canvasSize.width / 2, y: canvasSize.height / 2 }), [canvasSize]);

    const positions = useMemo(() => {
        const count = availableJobs.length;
        const radius = Math.max(120, Math.min(center.x, center.y) - 80);
        const angleStep = count > 0 ? (2 * Math.PI) / count : 0;
        return availableJobs.map((job, i) => {
            const angle = i * angleStep - Math.PI / 2; // start at top
            const x = center.x + radius * Math.cos(angle);
            const y = center.y + radius * Math.sin(angle);
            return { job, x, y };
        });
    }, [availableJobs, center]);

    return (
        <div
            ref={canvasRef}
            className="h-full w-full bg-gray-100 relative overflow-hidden"
        >
            {/* Grid background */}
            <div
                className="absolute inset-0 opacity-20"
                style={{
                    backgroundImage: `
                        linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                        linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                    `,
                    backgroundSize: '20px 20px'
                }}
            />

            {/* Central engine node */}
            <div
                className="absolute flex items-center justify-center rounded-full shadow bg-white border border-gray-300"
                style={{
                    left: center.x - 50,
                    top: center.y - 50,
                    width: 100,
                    height: 100
                }}
            >
                <div className="text-center">
                    <div className="text-xs text-gray-500">engine</div>
                    <div className="font-semibold text-gray-800">Workflow</div>
                </div>
            </div>

            {/* Spokes from engine to jobs */}
            <svg className="absolute inset-0 pointer-events-none" width="100%" height="100%">
                {positions.map(({ x, y }, idx) => (
                    <line
                        key={`line-${idx}`}
                        x1={center.x}
                        y1={center.y}
                        x2={x}
                        y2={y}
                        stroke="#93c5fd"
                        strokeWidth={2}
                        strokeDasharray="4 4"
                    />
                ))}
            </svg>

            {/* Job nodes */}
            {positions.map(({ job, x, y }) => (
                <div
                    key={job.id}
                    className="absolute -translate-x-1/2 -translate-y-1/2"
                    style={{ left: x, top: y }}
                >
                    <button
                        draggable
                        onDragStart={(e) => {
                            // store job id for drop target
                            e.dataTransfer?.setData('application/toolproof-job-id', job.id);
                        }}
                        className="w-44 text-left rounded-lg bg-white border border-gray-300 shadow hover:shadow-md transition-shadow p-3"
                        onClick={() => onJobClick?.(job)}
                        title={job.name}
                    >
                        <div className="text-sm font-medium text-gray-800 truncate">{job.name}</div>
                        <div className="text-xs text-gray-500 line-clamp-2 mt-1">
                            {((job.description as unknown as { description?: string })?.description) || 'Job'}
                        </div>
                    </button>
                </div>
            ))}

            {/* Empty state */}
            {availableJobs.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                        <div className="text-gray-400 text-6xl mb-4">🧭</div>
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No jobs available</h3>
                        <p className="text-gray-400">Provide jobs to visualize around the engine</p>
                    </div>
                </div>
            )}
        </div>
    );
}
