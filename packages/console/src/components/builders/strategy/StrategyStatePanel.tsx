import type { Resource<PERSON><PERSON>, ExecutionIdJson, ResourceRoleIdJson, ResourcePotentialInputJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';
import type { DragSource } from '@/builders/strategy/_lib/types';
import CircleBadge from '@/builders/strategy/CircleBadge';
import { useSelectionContext } from '@/builders/strategy/contexts/SelectionContext';
import { useRoleExecutionContext } from '@/builders/strategy/contexts/RoleExecutionContext';
import { useResourceContext } from '@/components/builders/strategy/contexts/ResourceContext';
import { useDragContext } from '@/builders/strategy/contexts/DragContext';

interface StrategyStatePanelProps { excludeStepIds?: string[] }

export default function StrategyStatePanel({ excludeStepIds = [] }: StrategyStatePanelProps) {
    const { activeExecution } = useSelectionContext();
    const { executionMap, roleMap } = useRoleExecutionContext();
    const { strategyState } = useResourceContext();
    const { setDragSource } = useDragContext();
    const cancelDrag = () => setDragSource(null);
    const excludeSet = new Set<string>(excludeStepIds.filter(Boolean));

    // console.log('roles:', JSON.stringify(Object.fromEntries(roles), null, 2));
    // console.log('activeExecution:', JSON.stringify(activeExecution, null, 2));

    return (
        <div>
            {(() => {
                const execEntries = Object.entries(strategyState || {});
                if (execEntries.length === 0) return null;
                const tiles: { executionId: ExecutionIdJson; resourceRoleId: ResourceRoleIdJson; r: ResourcePotentialInputJson | ResourcePotentialOutputJson | ResourceJson }[] = [];
                for (const [execId, roleMapObj] of execEntries) {
                    if (!roleMapObj) continue;
                    if (activeExecution?.identity === execId) continue; // exclude active execution resources
                    for (const [resourceRoleId, r] of Object.entries(roleMapObj as Record<string, ResourcePotentialInputJson | ResourcePotentialOutputJson | ResourceJson>)) {
                        if (!r) continue;
                        tiles.push({ executionId: execId as ExecutionIdJson, resourceRoleId: resourceRoleId as ResourceRoleIdJson, r });
                    }
                }
                if (tiles.length === 0) return null;
                return (
                    <div className='bg-blue-100'>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Resources</div>
                        <div className="flex flex-wrap gap-3">
                            {tiles.map(({ executionId, resourceRoleId, r }) => {
                                const role = roleMap.get(resourceRoleId);
                                if (!role) {
                                    console.warn(`Role not found for resourceRoleId: ${resourceRoleId}`);
                                    return null; // Skip this resource if role is not found
                                }

                                let isInput: boolean | undefined = undefined;
                                const originExec = executionMap.get(executionId as ExecutionIdJson);
                                if (originExec) {
                                    const inIds = Object.keys(originExec.roleBindings.inputBindingMap ?? {});
                                    const outIds = Object.keys(originExec.roleBindings.outputBindingMap ?? {});
                                    if (inIds.includes(resourceRoleId)) {
                                        isInput = true;
                                    } else if (outIds.includes(resourceRoleId)) {
                                        isInput = false;
                                    }
                                }
                                // Fallback: if still undefined, assume output to avoid mis-binding
                                if (isInput === undefined) isInput = false;
                                const dragPayload: DragSource = { executionId, resourceRoleId };

                                // For materialized resources, use identity as title
                                let titleText: string = role.name;
                                if (r.kind === 'materialized') {
                                    const ed = (r as ResourceJson).extractedData;
                                    titleText = String(ed.identity);
                                }

                                return (
                                    <div key={`${executionId}__${resourceRoleId}`} className="flex flex-col items-center">
                                        <CircleBadge
                                            as="div"
                                            draggable
                                            onDragStart={(e) => {
                                                // set drag source for drop handlers
                                                setDragSource(dragPayload);
                                                // set minimal data for native drag-and-drop
                                                try { e.dataTransfer?.setData('text/plain', JSON.stringify(dragPayload)); } catch (e) { }
                                            }}
                                            onDragEnd={() => cancelDrag()}
                                            className={isInput ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}
                                            title={titleText}
                                        >
                                            <span className="text-xs font-semibold">{isInput ? 'IN' : 'OUT'}</span>
                                        </CircleBadge>
                                        <div
                                            className="text-[11px] text-gray-700 mt-1 truncate max-w-28"
                                            title={titleText}
                                        >
                                            {role.name}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                );
            })()}
        </div>
    )

}