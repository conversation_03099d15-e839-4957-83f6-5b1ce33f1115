'use client'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { UserDropdown } from './UserDropdown'

export function HomeTopHeader() {
  const router = useRouter()
  const pathname = usePathname()
  const { data: session, status } = useSession()
  const navLinkClass = (href: string) => `tp-subnav-link ${pathname?.startsWith(href) ? 'tp-subnav-link-active' : ''}`
  const isAuthenticated = status === 'authenticated' && session
  
  if (pathname?.startsWith('/xr')) return null
  return (
    <header className="w-full">
      {/* top accent */}
      <div className="h-[3px] w-full tp-brand-accent-bg" />
      <div className="tp-header">
        <Link href="/" className="tp-brand-heading">ToolProof</Link>
        {isAuthenticated ? (
          <UserDropdown />
        ) : (
          <Link href="/auth" className="tp-brand-btn px-4 py-2">Sign up / Sign in</Link>
        )}
      </div>
      {/* Secondary Navigation - homepage only */}
      <nav className="w-full border-t border-b-2 tp-subnav-bg tp-brand-border">
        <div className="tp-container">
          <ul className="flex items-center justify-evenly gap-4 sm:gap-8 md:gap-12 py-2 sm:py-3">
            {/* <li>
              <Link
                href="/contact"
                className={navLinkClass('/contact')}
              >
                Contact us
              </Link>
            </li>
            <li>
              <Link
                href="/builders"
                className={navLinkClass('/builders')}
                onClick={(e) => {
                  e.preventDefault()
                  router.push('/builders')
                }}
              >
                Builders
              </Link>
            </li>
            <li>
              <Link
                href="/explorer"
                className={navLinkClass('/explorer')}
              >
                Visualisation
              </Link>
            </li> */}
            <li>
              <Link
                href="/docs"
                className={navLinkClass('/docs')}
                onClick={(e) => {
                  e.preventDefault()
                  router.push('/docs')
                }}
              >
                Documentation
              </Link>
            </li>
          </ul>
        </div>
      </nav>
    </header>
  )
}



