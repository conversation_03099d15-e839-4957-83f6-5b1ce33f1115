'use client';

import type { ResourceTypeId<PERSON>son, ResourceFormatIdJson, Resource_ResourceFormatJson, ResourceTypeJson, ExtractionSchemaValueJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResourceShape, validateResource } from '@toolproof-npm/validation';
import { getUiContext } from '@/builders/_lib/utils';
import { DropDown } from '@/builders/_lib/DropDown';
import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { LabeledInput } from '@/builders/_lib/LabeledInput';
import { LabeledCheckbox } from '@/builders/_lib/LabeledCheckbox';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';


const defaultExtractionSchema: ExtractionSchemaValueJson = {
    $anchor: 'Natural',
    $schema: 'https://json-schema.org/draft/2020-12/schema',
    type: 'object',
    properties: {
        identity: { $ref: '#/$defs/IdentityValueId' },
    },
    required: ['identity'],
    $defs: {
        IdentityValueId: {
            type: 'integer',
        }
    },
    additionalProperties: false,
};

const defaultType: ResourceTypeJson = {
    identity: '',
    name: 'Natural',
    description: 'dummy-description',
    resourceFormatId: 'FORMAT-ApplicationJson',
    extractionSchema: defaultExtractionSchema,
};

const defaultSampleResource = { identity: 0 };

interface TypeBuilderProps {
    resourceResourceFormatMap: Record<string, Resource_ResourceFormatJson>;
}

export default function TypeBuilder({ resourceResourceFormatMap }: TypeBuilderProps) {
    const [resourceId, setResourceId] = useState<string>('');
    const [executionId, setExecutionId] = useState<string>('');
    const [identity, setIdentity] = useState<string>(defaultType.identity);
    const [name, setName] = useState(defaultType.name);
    const [description, setDescription] = useState(defaultType.description);
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const [selectedResourceFormatId, setSelectedResourceFormatId] = useState<string>('');
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    const [extractionSchema, setExtractionSchema] = useState<ExtractionSchemaValueJson>(defaultType.extractionSchema as ExtractionSchemaValueJson);
    const [extractionSchemaText, setExtractionSchemaText] = useState<string>(
        JSON.stringify(defaultType.extractionSchema, null, 2)
    );
    const [extractionSchemaParseError, setExtractionSchemaParseError] = useState<string | null>(null);

    const [sampleResource, setSampleResource] = useState<unknown>(defaultSampleResource);
    const [sampleResourceText, setSampleResourceText] = useState<string>(JSON.stringify(defaultSampleResource, null, 2));
    const [sampleResourceParseError, setSampleResourceParseError] = useState<string | null>(null);

    const resourceFormatResources = useMemo(() => {
        return Object.values(resourceResourceFormatMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceFormatMap]);

    // DOC: Let an instance of the resourceshape be defined by state variables
    const type: ResourceTypeJson = useMemo(
        () => ({
            identity,
            name,
            description,
            resourceFormatId: selectedResourceFormatId as ResourceFormatIdJson,
            extractionSchema,
        }),
        [identity, name, description, selectedResourceFormatId, extractionSchema]
    );

    // DOC: Fetch a new id for the resourceshape
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!identity) {
                const newResourceId = await getNewId(CONSTANTS.RESOURCES.resources);
                const newExecutionId = await getNewId('executions');
                const newIdentity = await getNewId(CONSTANTS.SHAPES.types);
                setResourceId(newResourceId);
                setExecutionId(newExecutionId);
                setIdentity(newIdentity);
            }
        };
        asyncWrapper();
    }, [identity]);

    // DOC: When dependent resourceShapes arrive, auto-select the first
    useEffect(() => {
        if (!selectedResourceFormatId && resourceFormatResources.length) {
            setSelectedResourceFormatId(resourceFormatResources[0].extractedData.identity);
        } else {
            console.log('TypeBuilder - resourceFormats or selectedResourceFormatId unchanged');
            console.log('length:', resourceFormatResources.length, 'selectedResourceFormatId:', selectedResourceFormatId);
        }
    }, [resourceFormatResources, selectedResourceFormatId]);

    // DOC: Display errors from loading dependent resourceShapes
    // No external loading/error when maps are injected and gated by parent

    // DOC: Validate the resourceshape locally
    const isValidLocal = useMemo(() => validateLocally(type), [type]);

    const uiContext = getUiContext();

    // DOC: Validate the resourceshape formally against its schema
    const { isValid: isValidFormal, errors } = validateResourceShape('ResourceType', type, uiContext);

    // console.log('-----------');
    // console.log('TypeBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && !extractionSchemaParseError && isValidFormal;

    // DOC: Validate sampleResource against extractionSchema
    const { isValid: isValidSample, errors: errorsSample } = validateResource(extractionSchema, sampleResource);

    // DOC: Update extractionSchema state on text change, with parse error handling
    const handleExtractionSchemaChange = (text: string) => {
        setExtractionSchemaText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setExtractionSchemaParseError('extractionSchema must be a JSON Schema object.');
                return;
            }
            setExtractionSchema(parsed as ExtractionSchemaValueJson);
            setExtractionSchemaParseError(null);
        } catch (e) {
            setExtractionSchemaParseError((e as Error).message);
        }
    };

    // DOC: Update sampleResource state on text change, with parse error handling
    const handleSampleResourceChange = (text: string) => {
        setSampleResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setSampleResourceParseError('sampleResource must be a JSON object.');
                return;
            }
            setSampleResource(parsed);
            setSampleResourceParseError(null);
        } catch (e) {
            setSampleResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resourceshape upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        // DOC: 'extractionSchema' is excluded from the resourceshape metadata saved to Firestore
        const res = (await uploadResource(
            {
                identity: resourceId,
                resourceTypeId: 'TYPE-ResourceType',
                creationContext: {
                    resourceRoleId: 'ROLE-Builder',
                    executionId,
                },
            },
            JSON.stringify(type, null, 2)
        ));
        if (res.success) {
            setSaveStatus(`Saved at path: ${res.path}`);
        } else {
            setSaveStatus(`Save failed: ${res.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='type-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {isSpecial ? (
                        <LabeledInput label='ID' value={identity} onChange={setIdentity} placeholder='Custom ID' />
                    ) : (
                        <>
                            {/* DOC: 'id' is generated server-side */}
                            <ReadOnlyIdField value={identity} />
                        </>
                    )}

                    <DropDown
                        items={resourceFormatResources}
                        value={selectedResourceFormatId}
                        label='Format'
                        // loading={resourceFormatMetaMapLoading}
                        onChange={(newResourceFormatId) => {
                            setSelectedResourceFormatId(newResourceFormatId);
                        }}
                    />
                    <LabeledInput
                        label='Name'
                        value={name}
                        onChange={setName}
                        placeholder={defaultType.name}
                        error={isValidLocal.errors.name}
                    />
                    <LabeledInput
                        label='Description'
                        value={description}
                        onChange={setDescription}
                        placeholder={defaultType.description}
                        error={isValidLocal.errors.description}
                    />
                    <LabeledCheckbox label='isSpecial' checked={isSpecial} onChange={setIsSpecial} />
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <JsonEditor
                            legend='extractionSchema (JSON Schema draft 2020-12)'
                            valueText={extractionSchemaText}
                            onChangeText={handleExtractionSchemaChange}
                            parseError={extractionSchemaParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview {name}</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {loadingPreview
                                    ? 'Loading…'
                                    : JSON.stringify(
                                        type,
                                        null,
                                        2
                                    )}
                            </pre>
                            <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                        </section>
                    </div>

                    <div>
                        <JsonEditor
                            legend='sampleResource (validated against extractionSchema)'
                            valueText={sampleResourceText}
                            onChangeText={handleSampleResourceChange}
                            parseError={sampleResourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(sampleResource, null, 2)}
                            </pre>
                            {errorsSample?.length ? (
                                <ValidationErrors errors={errorsSample} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current extractionSchema.
                                </div>
                            )}
                        </section>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='type-form'
                buttonText='Save Type'
                disabled={!isValid || !identity}
                isValid={isValid}
                invalidMessage='Fix errors before saving.'
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}

function validateLocally(
    type: ResourceTypeJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    // id is server-generated; no client-side validation
    if (!type.name.trim()) errors.name = 'name is required';
    // if (!rt.description?.trim()) errors.description = 'Description is required';
    if (!type.resourceFormatId) errors.resourceFormatId = 'resourceFormatId is required';
    if (!type.extractionSchema || typeof type.extractionSchema !== 'object' || Array.isArray(type.extractionSchema)) {
        errors.extractionSchema = 'extractionSchema must be a JSON Schema object';
    }

    return { valid: Object.keys(errors).length === 0, errors };
}
