'use client';

import type { ResourceTypeId<PERSON><PERSON>, ExecutionId<PERSON>son, ResourceRoleIdJson, RoleMapJson, ResourceRoleValueJson, ExtractionSchemaValue<PERSON>son, Resource_ResourceTypeJson } from '@toolproof-npm/schema';
import { JobSchema } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { LabeledInput } from '../_lib/LabeledInput';
import { getUiContext } from '@/builders/_lib/utils';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import SaveControls from '@/builders/_lib/SaveControls';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';

interface JobBuilderProps {
    resourceResourceTypeMap: Record<string, Resource_ResourceTypeJson>;
}

export default function JobBuilder({ resourceResourceTypeMap }: JobBuilderProps) {
    const [resourceId, setResourceId] = useState<string>('');
    const [executionId, setExecutionId] = useState<string>('');
    const [identity, setIdentity] = useState<string>('');
    const [name, setName] = useState('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [implementation, setImplementation] = useState<string>('');
    const [inputMap, setInputMap] = useState<RoleMapJson>({});
    const [outputMap, setOutputMap] = useState<RoleMapJson>({});
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [newInputResourceTypeId, setNewInputResourceTypeId] = useState<string>('');
    const [newOutputResourceTypeId, setNewOutputResourceTypeId] = useState<string>('');
    const [job, setJob] = useState<unknown>('');
    const [jobText, setJobText] = useState<string>(JSON.stringify('', null, 2));
    const [jobParseError, setJobParseError] = useState<string | null>(null);

    const resourceTypeResources = useMemo(() => {
        return Object.values(resourceResourceTypeMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceTypeMap]);

    // DOC: Fetch a new id for the job
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!identity) {
                const newIdentity = await getNewId('jobs'); // ATTENTION
                setIdentity(newIdentity);
            }
        };
        asyncWrapper();
    }, [identity]);

    // DOC: Fetch a new executionId for the job creation
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!resourceId) {
                const newId = await getNewId(CONSTANTS.RESOURCES.resources);
                setResourceId(newId);
            }
            if (!executionId) {
                const newId = await getNewId('executions');
                setExecutionId(newId);
            }
        };
        asyncWrapper();
    }, [resourceId, executionId]);

    // DOC: Initialize default selections for add-dropdowns when types load
    useEffect(() => {
        if (resourceTypeResources.length) {
            setNewInputResourceTypeId((prev) => prev || resourceTypeResources[0].extractedData.identity);
            setNewOutputResourceTypeId((prev) => prev || resourceTypeResources[0].extractedData.identity);
        }
    }, [resourceTypeResources]);

    const uiContext = getUiContext();

    // DOC: Validate job against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        return validateResource(JobSchema as ExtractionSchemaValueJson, job); // ATTENTION
    }, [job]);

    // console.log('JobBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    // DOC: Add/Remove/Update role helpers for inputs and outputs
    const addRole = async (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceTypeId: string,
    ) => {
        const resourceRoleId = await getNewId(CONSTANTS.ROLES.roles) as ResourceRoleIdJson; // ATTENTION
        const newRole: ResourceRoleValueJson = {
            resourceTypeId,
            name: '',
            description: '',
        };
        setCurrent({ ...current, [resourceRoleId]: newRole as unknown as RoleMapJson[keyof RoleMapJson] }); // ATTENTION
    };

    // Update helpers for individual fields on a role literal
    const updateRoleType = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleId: ResourceRoleIdJson,
        resourceTypeId: ResourceTypeIdJson,
    ) => {
        const prev = current[resourceRoleId] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleId]: { ...prev, resourceTypeId } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleName = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleId: ResourceRoleIdJson,
        name: string,
    ) => {
        const prev = current[resourceRoleId] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleId]: { ...prev, name } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleDescription = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleId: ResourceRoleIdJson,
        description: string,
    ) => {
        const prev = current[resourceRoleId] as unknown as ResourceRoleValueJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [resourceRoleId]: { ...prev, description } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const removeRole = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        resourceRoleId: ResourceRoleIdJson,
    ) => {
        const { [resourceRoleId]: _removed, ...rest } = current;
        setCurrent(rest);
    };

    // DOC: Update job state on text change, with parse error handling
    const handleJobChange = (text: string) => {
        setJobText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setJobParseError('job must be a JSON object.');
                return;
            }
            setJob(parsed);
            setJobParseError(null);
        } catch (e) {
            setJobParseError((e as Error).message);
        }
    };

    // DOC: Auto-build job JSON whenever form fields change
    useEffect(() => {
        const jobObj = {
            identity,
            name,
            description,
            implementation,
            roles: {
                inputMap,
                outputMap,
            },
        };
        handleJobChange(JSON.stringify(jobObj, null, 2));
    }, [identity, name, description, implementation, executionId, inputMap, outputMap]);

    // DOC: Upload the job (as a resource) upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadResource(
            {
                identity: resourceId,
                resourceTypeId: 'TYPE-Job', // ATTENTION: hardcoded
                creationContext: {
                    resourceRoleId: CONSTANTS.SPECIALS.ROLE_BUILDER as ResourceRoleIdJson,
                    executionId: executionId as ExecutionIdJson
                },
            },
            JSON.stringify(job, null, 2)
        ));
        if (res.success) {
            setSaveStatus(`Saved at path: ${res.path}`);
        } else {
            setSaveStatus(`Save failed: ${res.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div className='p-6 max-w-5xl mx-auto space-y-4'>
            <form id='job-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={identity} />
                </div>

                <LabeledInput
                    label='Name'
                    value={name}
                    onChange={setName}
                    placeholder={''}
                // error={isValidLocal.errors.name}
                />

                <LabeledInput label='Description' value={description} onChange={setDescription} placeholder='What this job is for' />

                <LabeledInput
                    label='Uri'
                    value={implementation}
                    onChange={setImplementation}
                    placeholder={''}
                // error={isValidLocal.errors.uri}
                />

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {/* Inputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Inputs</legend>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newInputResourceTypeId}
                                        onChange={(e) => setNewInputResourceTypeId(e.target.value)}
                                    >
                                        {resourceTypeResources.map((t) => (
                                            <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                {t.extractedData.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newInputResourceTypeId}
                                        onClick={() => addRole(inputMap, setInputMap, newInputResourceTypeId)}
                                    >
                                        Add input
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(inputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No inputs yet</li>
                                    ) : (
                                        Object.entries(inputMap).map(([resourceRoleId, role]) => {
                                            const rl = role as unknown as ResourceRoleValueJson;
                                            return (
                                                <li key={resourceRoleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{resourceRoleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.resourceTypeId || ''}
                                                            onChange={(e) => updateRoleType(inputMap, setInputMap, resourceRoleId as ResourceRoleIdJson, e.target.value as ResourceTypeIdJson)}
                                                        >
                                                            {resourceTypeResources.map((t) => (
                                                                <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                                    {t.extractedData.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                            onClick={() => removeRole(inputMap, setInputMap, resourceRoleId as ResourceRoleIdJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) => updateRoleName(inputMap, setInputMap, resourceRoleId as ResourceRoleIdJson, e.target.value)}
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) => updateRoleDescription(inputMap, setInputMap, resourceRoleId as ResourceRoleIdJson, e.target.value)}
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                    </fieldset>

                    {/* Outputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Outputs</legend>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newOutputResourceTypeId}
                                        onChange={(e) => setNewOutputResourceTypeId(e.target.value)}
                                    >
                                        {resourceTypeResources.map((t) => (
                                            <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                {t.extractedData.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newOutputResourceTypeId}
                                        onClick={() => addRole(outputMap, setOutputMap, newOutputResourceTypeId)}
                                    >
                                        Add output
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(outputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No outputs yet</li>
                                    ) : (
                                        Object.entries(outputMap).map(([resourceRoleId, role]) => {
                                            const rl = role as unknown as ResourceRoleValueJson;
                                            return (
                                                <li key={resourceRoleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{resourceRoleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.resourceTypeId || ''}
                                                            onChange={(e) => updateRoleType(outputMap, setOutputMap, resourceRoleId as ResourceRoleIdJson, e.target.value as ResourceTypeIdJson)}
                                                        >
                                                            {resourceTypeResources.map((t) => (
                                                                <option key={t.extractedData.identity} value={t.extractedData.identity}>
                                                                    {t.extractedData.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                            onClick={() => removeRole(outputMap, setOutputMap, resourceRoleId as ResourceRoleIdJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) => updateRoleName(outputMap, setOutputMap, resourceRoleId as ResourceRoleIdJson, e.target.value)}
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) => updateRoleDescription(outputMap, setOutputMap, resourceRoleId as ResourceRoleIdJson, e.target.value)}
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                    </fieldset>
                </div>
            </form>

            <div>
                <h3 className='font-semibold mb-2'>Preview {identity}</h3>
                <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>{loadingPreview
                    ? 'Loading…'
                    : JSON.stringify(
                        job,
                        null,
                        2)}
                </pre>
                <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
            </div>

            <SaveControls
                formId='job-form'
                buttonText='Save Job'
                disabled={!isValid || !identity}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
            />
        </div>
    );
}
