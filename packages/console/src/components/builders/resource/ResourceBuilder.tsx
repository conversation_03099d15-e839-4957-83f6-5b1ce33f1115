'use client';

import type { ResourceTypeId<PERSON><PERSON>, ResourceRoleIdJson, ExecutionIdJson, ResourceTypeJson, ResourceIdJson, Resource_ResourceTypeJson, ExtractionSchemaValueJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { DropDown } from '@/builders/_lib/DropDown';
import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';


interface ResourceBuilderProps {
    resourceResourceTypeMap: Record<string, Resource_ResourceTypeJson>;
}

export default function ResourceBuilder({ resourceResourceTypeMap }: ResourceBuilderProps) {
    const [identity, setIdentity] = useState<string>('');
    const [executionId, setExecutionId] = useState<string>('');

    const [selectedType, setSelectedType] = useState<ResourceTypeJson>();
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [resource, setResource] = useState<unknown>('');
    const [resourceText, setResourceText] = useState<string>(JSON.stringify('', null, 2));
    const [resourceParseError, setResourceParseError] = useState<string | null>(null);
    const [uploadedFileInfo, setUploadedFileInfo] = useState<{ message: string, content: string } | null>(null);

    // console.log('selectedType:', JSON.stringify(selectedType, null, 2));

    const resourceTypeResources = useMemo(() => {
        return Object.values(resourceResourceTypeMap).map((res) => ({
            ...res,
        }));
    }, [resourceResourceTypeMap]);

    // DOC: Fetch a new id for the resource
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!identity) {
                const newIdentity = await getNewId(CONSTANTS.RESOURCES.resources);
                setIdentity(newIdentity);
            }
        };
        asyncWrapper();
    }, [identity]);

    // DOC: Fetch a new executionId for the resource
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!executionId) {
                const newId = await getNewId('executions');
                setExecutionId(newId);
            }
        };
        asyncWrapper();
    }, [executionId]);

    // DOC: When dependent resourceShapes arrive, auto-select the first
    useEffect(() => {
        if (!selectedType && resourceTypeResources.length) {
            setSelectedType(resourceTypeResources[0].extractedData);
        }
    }, [resourceTypeResources, selectedType]);

    // DOC: Validate resource against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        if (!selectedType) {
            console.log('No selectedType, cannot validate');
            return { isValid: false, errors: null as ErrorObject[] | null };
        }
        return validateResource(selectedType.extractionSchema, resource);
    }, [resource, selectedType]);

    // DOC: Handle file upload and read content
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            const text = await file.text();
            const lines = text.split('\n');
            const firstLine = lines[0] || '';

            const fileInfo = {
                message: `File uploaded: ${file.name} (${file.size} bytes)`,
                content: firstLine
            };

            setUploadedFileInfo(fileInfo);

            // Optionally, set the file content as the resource text
            // setResourceText(text);
            // handleResourceChange(text);
        } catch (error) {
            setUploadedFileInfo({
                message: `Error reading file: ${(error as Error).message}`,
                content: ''
            });
        }
    };

    // DOC: Update resource state on text change, with parse error handling
    const handleResourceChange = (text: string) => {
        setResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setResourceParseError('resource must be a JSON object.');
                return;
            }
            setResource(parsed);
            setResourceParseError(null);
        } catch (e) {
            setResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resource upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        if (!selectedType) return;
        const res = (await uploadResource(
            {
                identity,
                resourceTypeId: selectedType.identity,
                creationContext: {
                    resourceRoleId: CONSTANTS.SPECIALS.ROLE_BUILDER,
                    executionId: executionId
                }
            },
            JSON.stringify(resource, null, 2)
        ));
        if (res.success) {
            setSaveStatus(`Saved at path: ${res.path}`);
        } else {
            setSaveStatus(`Save failed: ${res.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='resource-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'executionId' is generated server-side */}
                    <ReadOnlyIdField value={executionId} />
                </div>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <DropDown
                        items={resourceTypeResources}
                        value={selectedType?.identity ?? ''}
                        label='Type'
                        loading={false}
                        onChange={(newResourceTypeId) => {
                            console.log('DropDown onChange triggered');
                            console.log('newResourceTypeId:', newResourceTypeId);
                            console.log('Available resourceTypeResources:', resourceTypeResources.map(r => ({ identity: r.identity, extractedIdentity: r.extractedData.identity, name: r.extractedData.name })));
                            const newResourceTypeResource = (resourceTypeResources ?? []).find(type => type.extractedData.identity === newResourceTypeId);
                            console.log('Found newResourceTypeResource:', newResourceTypeResource?.identity);
                            console.log('extractedData to set:', newResourceTypeResource?.extractedData);
                            setSelectedType(newResourceTypeResource?.extractedData);
                        }}
                    />

                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>

                    <div>
                        <JsonEditor
                            legend='sampleResource (select a Type to validate against its extractionSchema)'
                            valueText={resourceText}
                            onChangeText={handleResourceChange}
                            parseError={resourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(resource, null, 2)}
                            </pre>
                            {!selectedType ? (
                                <div className='text-sm text-gray-600 mt-2'>
                                    Select a Type to run schema validation.
                                </div>
                            ) : errors?.length ? (
                                <ValidationErrors errors={errors} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current extractionSchema.
                                </div>
                            )}
                        </section>
                    </div>

                    {/* Right column with File Upload Section */}
                    <div>
                        <div className='mb-4'>
                            <label className='block text-sm font-medium text-gray-700 mb-2'>
                                Upload a file
                            </label>
                            <input
                                type="file"
                                onChange={handleFileUpload}
                                className='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
                                accept=".json,.txt,.js,.ts,.jsx,.tsx,.py,.md"
                            />
                            {uploadedFileInfo && (
                                <div className='mt-4'>
                                    <h4 className='text-sm font-semibold text-gray-700 mb-2'>File Upload JSON:</h4>
                                    <pre className='bg-gray-100 p-3 rounded border text-xs overflow-auto'>
                                        {JSON.stringify(uploadedFileInfo, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='resource-form'
                buttonText='Save Resource'
                disabled={!isValid}
                isValid={isValid}
                error={error}
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}
