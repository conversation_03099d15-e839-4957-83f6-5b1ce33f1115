'use client';

import type { ResourceFormatJson } from '@toolproof-npm/schema';
import type { UploadResult } from '@/builders/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResourceShape } from '@toolproof-npm/validation';
import { getUiContext } from '@/builders/_lib/utils';
import { LabeledInput } from '@/builders/_lib/LabeledInput';
import { LabeledCheckbox } from '@/builders/_lib/LabeledCheckbox';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';


export default function FormatBuilder() {
    const [resourceId, setResourceId] = useState<string>('');
    const [executionId, setExecutionId] = useState<string>('');
    const [identity, setIdentity] = useState<string>('');
    const [name, setName] = useState<string>('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    // DOC: Let an instance of the resourceshape be defined by state variables
    const format: ResourceFormatJson = useMemo(
        () => {
            return {
                identity,
                name,
                description,
            };
        },
        [identity, name, description]
    );

    // DOC: Fetch a new id for the resourceshape
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!identity) {
                const newResourceId = await getNewId(CONSTANTS.RESOURCES.resources);
                const newExecutionId = await getNewId('executions');
                const newIdentity = await getNewId(CONSTANTS.SHAPES.formats);
                setResourceId(newResourceId);
                setExecutionId(newExecutionId);
                setIdentity(newIdentity);
            }
        };
        asyncWrapper();
    }, [identity]);

    // DOC: Validate the resourceshape locally
    const isValidLocal = Boolean(identity.trim() && name.trim());

    const uiContext = getUiContext();

    // DOC: Validate the resourceshape formally against its schema
    const { isValid: isValidFormal, errors } = validateResourceShape('ResourceFormat', format, uiContext); // ATTENTION: hardcoded defsPointer

    // console.log('-----------');
    // console.log('FormatBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && isValidFormal;

    // DOC: Upload the resourceshape upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadResource(
            {
                identity: resourceId,
                resourceTypeId: 'TYPE-ResourceFormat',
                creationContext: {
                    resourceRoleId: 'ROLE-Builder',
                    executionId,
                },
            },
            JSON.stringify(format, null, 2)
        ));
        if (res.success) {
            setSaveStatus(`Saved at path: ${res.path}`);
        } else {
            setSaveStatus(`Save failed: ${res.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='format-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {isSpecial ? (
                        <LabeledInput label='ID' value={identity} onChange={setIdentity} placeholder='Custom ID' />
                    ) : (
                        <>
                            {/* DOC: 'id' is generated server-side */}
                            <ReadOnlyIdField value={identity} />
                        </>
                    )}

                    <LabeledInput label='Name' value={name} onChange={setName} placeholder='Format name' />

                    <LabeledInput label='Description' value={description} onChange={setDescription} placeholder='What this format is for' />

                    <LabeledCheckbox label='isSpecial' checked={isSpecial} onChange={setIsSpecial} />
                </div>

                <div>
                    <h3 className='font-semibold mb-2'>Preview {name}</h3>
                    <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                        {loadingPreview
                            ? 'Loading…'
                            : JSON.stringify(
                                format,
                                null,
                                2
                            )}
                    </pre>
                    <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                </div>
            </form>

            <SaveControls
                formId='format-form'
                buttonText='Save Format'
                disabled={!isValid}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}
