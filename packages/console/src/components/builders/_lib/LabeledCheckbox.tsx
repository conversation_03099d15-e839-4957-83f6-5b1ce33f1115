'use client';

export type LabeledCheckboxProps = {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
};

export function LabeledCheckbox({ label, checked, onChange, className }: LabeledCheckboxProps) {
  return (
    <div className={`flex items-center space-x-2 ${className ?? ''}`}>
      <input
        type='checkbox'
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className='h-4 w-4 text-blue-600 border-gray-300 rounded'
      />
      <label className='text-sm'>
        {label}
      </label>
    </div>
  );
}

export default LabeledCheckbox;
