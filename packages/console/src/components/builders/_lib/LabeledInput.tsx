

export function LabeledInput(props: {
  label: string;
  value: string;
  onChange: (v: string) => void;
  placeholder?: string;
  error?: string;
}) {
  return (
    <div>
      <label className='block text-sm font-medium mb-1'>{props.label}</label>
      <input
        className='w-full rounded border border-gray-300 px-3 py-2'
        value={props.value}
        onChange={(e) => props.onChange(e.target.value)}
        placeholder={props.placeholder}
      />
      {props.error && <div className='text-red-600 text-sm mt-1'>{props.error}</div>}
    </div>
  );
}
