'use client';

export type ReadOnlyIdFieldProps = {
  value: string;
  label?: string;
  generatingText?: string;
  className?: string;
  inputClassName?: string;
};

export function ReadOnlyIdField({
  value,
  label = 'ID',
  generatingText = 'Generating…',
  className,
  inputClassName,
}: ReadOnlyIdFieldProps) {
  return (
    <div className={className}>
      <label className='block text-sm font-medium mb-1'>{label}</label>
      <input
        className={`w-full rounded border border-gray-200 bg-gray-100 px-3 py-2 text-gray-700 ${inputClassName ?? ''}`}
        value={value || generatingText}
        readOnly
      />
    </div>
  );
}

export default ReadOnlyIdField;
