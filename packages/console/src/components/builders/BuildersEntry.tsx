'use client';

import type { ResourceShapeConst, ResourceMap } from '@toolproof-npm/shared/types';
import type { ResourceJson, ResourceIdJson, ResourceTypeIdJson, Resource_ResourceFormatJson, Resource_ResourceTypeJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import FormatBuilder from '@/builders/format/FormatBuilder';
import TypeBuilder from '@/builders/type/TypeBuilder';
import JobBuilder from '@/builders/job/JobBuilder';
import ResourceBuilder from './resource/ResourceBuilder';
import StrategyBuilder from '@/builders/strategy/StrategyBuilder';
import { useState } from 'react';
import { useCosmosData } from '@/spaces/cosmos/integration/CosmosDataProvider';
import { PageLayout } from '@/components/_root/PageLayout';

type BuilderKey = ResourceShapeConst | typeof CONSTANTS.RESOURCES.resources | typeof CONSTANTS.RESOURCES.jobs | 'Strategy';

/**
 * Extracts resources of a specific type from the strategyState and returns them
 * as a Record mapping resource IDs to their full resource objects (including extractedData).
 * 
 * @param resourceMap - The full resource map containing all resource types
 * @param resourceTypeId - The specific resource type ID to filter by (e.g., 'TYPE-ResourceFormat')
 * @returns A record mapping resource IDs to their full resource objects
 */
function extractResourcesByType(
    resourceMap: ResourceMap,
    resourceTypeId: ResourceTypeIdJson
): Record<ResourceIdJson, ResourceJson> {
    const resources = resourceMap[resourceTypeId] ?? [];
    const result: Record<ResourceIdJson, ResourceJson> = {};

    for (const resource of resources) {
        result[resource.identity] = resource;
    }

    return result;
}

const builders: { key: BuilderKey; label: string }[] = [
    { key: CONSTANTS.SHAPES.formats, label: 'ResourceFormat' },
    { key: CONSTANTS.SHAPES.types, label: 'ResourceType' },
    { key: CONSTANTS.RESOURCES.resources, label: 'Resource' },
    { key: CONSTANTS.RESOURCES.jobs, label: 'Job' },
    { key: 'Strategy', label: 'Strategy' },
];

export default function BuildersEntry() {
    const { cosmosSpaceData, loading, error } = useCosmosData();
    const { resourceMap } = cosmosSpaceData;
    const [active, setActive] = useState<BuilderKey>(CONSTANTS.SHAPES.formats);

    const resourceResourceFormatMap = extractResourcesByType(resourceMap, 'TYPE-ResourceFormat') as Record<ResourceIdJson, Resource_ResourceFormatJson>;

    const resourceResourceTypeMap = extractResourcesByType(resourceMap, 'TYPE-ResourceType') as Record<ResourceIdJson, Resource_ResourceTypeJson>;

    console.log(
        '[BuildersEntry] resourceMap:',
        JSON.stringify(resourceMap, null, 2)
    );

    if (loading) {
        return (
            <PageLayout>
                <div className="flex h-full items-center justify-center text-sm text-gray-400">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-transparent" />
                    Loading Cosmos data…
                </div>
            </PageLayout>
        );
    }

    if (error) {
        return (
            <PageLayout>
                <div className="flex h-full items-center justify-center px-4 text-sm text-red-500">
                    Failed to load Cosmos data: {String(error?.message ?? error)}
                </div>
            </PageLayout>
        );
    }

    // Special layout for StrategyBuilder - needs adequate height but contained
    if (active === 'Strategy') {
        return (
            <PageLayout>
                <div className="flex flex-col">
                    {/* Header section - fixed height */}
                    <div className="flex-shrink-0 tp-container py-4 sm:py-6 px-4 sm:px-6 bg-white border-b border-gray-200">
                        <div className="mx-auto max-w-[95vw] w-full">
                            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2 sm:mb-3" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                Builders
                            </h1>
                            <p className="text-xs sm:text-sm text-[#7A0019]/80 break-words mb-4" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                Create and manage ResourceFormats, ResourceTypes, Resources, Jobs, and Strategys
                            </p>

                            {/* Top controls */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
                                {/* Left: ResourceShapes and Resources groups */}
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 md:gap-4 flex-wrap">
                                    {/* ResourceShapes */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <span className="text-xs font-medium tracking-wide text-gray-500 mr-1 hidden sm:inline">ResourceShapes</span>
                                        {([CONSTANTS.SHAPES.formats, CONSTANTS.SHAPES.types] as BuilderKey[]).map((key) => {
                                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                            return (
                                                <button
                                                    key={key}
                                                    onClick={() => setActive(key)}
                                                    className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                        ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                        : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                        }`}
                                                    aria-pressed={active === key}
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })}
                                    </div>

                                    {/* Resources */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <span className="text-xs font-medium tracking-wide text-gray-500 mr-1 hidden sm:inline">Resources</span>
                                        {([CONSTANTS.RESOURCES.resources, CONSTANTS.RESOURCES.jobs] as BuilderKey[]).map((key) => {
                                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                            return (
                                                <button
                                                    key={key}
                                                    onClick={() => setActive(key)}
                                                    className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                        ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                        : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                        }`}
                                                    aria-pressed={active === key}
                                                >
                                                    {label}
                                                </button>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Right: Strategy button */}
                                <div className="flex items-center w-full sm:w-auto">
                                    <button
                                        onClick={() => setActive('Strategy')}
                                        className={`px-2.5 py-1.5 sm:px-3 md:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium w-full sm:w-auto ${active === 'Strategy'
                                            ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                            : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                            }`}
                                        aria-pressed={active === 'Strategy'}
                                    >
                                        Strategy
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Strategy builder - takes remaining space with padding */}
                    <div className="w-full px-2 sm:px-4 md:px-6 lg:px-8 xl:px-10" style={{ minHeight: 'calc(100vh - 300px)' }}>
                        <StrategyBuilder />
                    </div>
                </div>
            </PageLayout>
        );
    }

    // Standard layout for other builders
    return (
        <PageLayout>
            <div className="tp-container py-6 sm:py-8 md:py-12 px-4 sm:px-6">
                <div className="mx-auto max-w-6xl w-full">
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight text-[#7A0019] break-words mb-2 sm:mb-3" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Builders
                    </h1>
                    <p className="text-xs sm:text-sm text-[#7A0019]/80 break-words mb-6 sm:mb-8" style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                        Create and manage ResourceFormats, ResourceTypes, Resources, Jobs, and Strategys
                    </p>

                    {/* Top controls: ResourceShapes and Resources on the left; Strategy on the right */}
                    <div className="flex items-center justify-between flex-wrap gap-3 sm:gap-4 mb-8 pb-6 border-b border-gray-200">
                        {/* Left: ResourceShapes and Resources groups */}
                        <div className="flex items-center gap-3 sm:gap-4 flex-wrap">
                            {/* ResourceShapes */}
                            <div className="flex items-center gap-2 flex-wrap">
                                <span className="text-xs font-medium tracking-wide text-gray-500 mr-1">ResourceShapes</span>
                                {([CONSTANTS.SHAPES.formats, CONSTANTS.SHAPES.types] as BuilderKey[]).map((key) => {
                                    const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                    return (
                                        <button
                                            key={key}
                                            onClick={() => setActive(key)}
                                            className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                }`}
                                            aria-pressed={active === key}
                                        >
                                            {label}
                                        </button>
                                    );
                                })}
                            </div>

                            {/* Resources */}
                            <div className="flex items-center gap-2 flex-wrap">
                                <span className="text-xs font-medium tracking-wide text-gray-500 mr-1">Resources</span>
                                {([CONSTANTS.RESOURCES.resources, CONSTANTS.RESOURCES.jobs] as BuilderKey[]).map((key) => {
                                    const label = builders.find((b) => b.key === key)?.label ?? String(key);
                                    return (
                                        <button
                                            key={key}
                                            onClick={() => setActive(key)}
                                            className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium ${active === key
                                                ? 'bg-[#7A0019] text-white border-2 border-[#7A0019] hover:bg-[#5A0013]'
                                                : 'bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5'
                                                }`}
                                            aria-pressed={active === key}
                                        >
                                            {label}
                                        </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Right: Strategy button */}
                        <div className="flex items-center">
                            <button
                                onClick={() => setActive('Strategy')}
                                className="px-3 py-1.5 sm:px-4 sm:py-2 rounded-md text-xs sm:text-sm transition-colors font-medium bg-white text-[#7A0019] border-2 border-[#7A0019] hover:bg-[#7A0019]/5"
                                aria-pressed={false}
                            >
                                Strategy
                            </button>
                        </div>
                    </div>

                    <div className="mt-6">
                        {active === CONSTANTS.SHAPES.formats && (
                            <FormatBuilder />
                        )}
                        {active === CONSTANTS.SHAPES.types && (
                            <TypeBuilder resourceResourceFormatMap={resourceResourceFormatMap} />
                        )}
                        {active === CONSTANTS.RESOURCES.jobs && (
                            <JobBuilder resourceResourceTypeMap={resourceResourceTypeMap} />
                        )}
                        {active === CONSTANTS.RESOURCES.resources && (
                            <ResourceBuilder resourceResourceTypeMap={resourceResourceTypeMap} />
                        )}
                    </div>
                </div>
            </div>
        </PageLayout>
    );
}
