# @toolproof-npm/schema

JSON schemas and TypeScript types for ToolProof...

## Installation

```bash
pnpm add @toolproof-npm/schema
# or
npm install @toolproof-npm/schema
# or
yarn add @toolproof-npm/schema
```

## Usage

### Import Schemas and Types

```typescript
import { /* schemas and types */ } from '@toolproof-npm/schema';
```

## Features

- JSON Schema definitions with $defs subschemas
- Generated TypeScript types from JSON schemas
- Runtime schema validation support
- Extractors for non-JSON resource types
- Schema bundling utilities

## Development

### Generate Types

```bash
pnpm run generateTypes
```

### Build

```bash
pnpm run build
```

### Update (Clean build + generate types)

```bash
pnpm run update
```

## Requirements

- Node.js 16+
- TypeScript 4.5+ (for TypeScript projects)

## License

MIT

