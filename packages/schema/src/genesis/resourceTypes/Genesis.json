{"identity": "TYPE-Genesis", "name": "Genesis", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$comment": "This schema defines all genesis schemas used throughout the Toolproof ecosystem. The genesis schemas themselves are defined in $defs.<ResourceType>.extractionSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (src/schemas/Genesis.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/ResourceType, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem.", "$id": "https://schemas.toolproof.com/v0/Genesis.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"Name": {"identity": "TYPE-Name", "name": "Name", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Name", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "semanticValidation": "Ajv custom keyword to verify name."}}}}, "Description": {"identity": "TYPE-Description", "name": "Description", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Description", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["description"], "properties": {"description": {"type": "string", "minLength": 1, "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "semanticValidation": "Ajv custom keyword to verify description."}}}}, "Documented": {"identity": "TYPE-Documented", "name": "Documented", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Documented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#Name"}, {"$ref": "#Description"}]}}, "IdentityValue": {"identity": "TYPE-IdentityValue", "name": "IdentityValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$ref"], "properties": {"$ref": {"type": "string", "$comment": "Supports both anchor syntax (#<Name>Id) and JSON Pointer syntax (#/path/to/definition). Anchors use #<Name>Id for identity refs. Top-level and nested $defs should expose an $anchor matching <Name>Id.", "pattern": "^#([A-Za-z][A-Za-z0-9._-]*Id|/.*)$"}}, "additionalProperties": false}}, "IdSchemaValue": {"identity": "TYPE-IdSchemaValue", "name": "IdSchemaValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdSchemaValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["type"], "properties": {"type": {"enum": ["string", "number", "integer", "boolean"]}}}}, "IdentityProp": {"identity": "TYPE-IdentityProp", "name": "IdentityProp", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$defs", "required", "properties"], "properties": {"$defs": {"type": "object", "patternProperties": {"^[A-Za-z][A-Za-z0-9._-]*Id$": {"$ref": "#IdSchemaValue"}}, "$comment": "Ajv-specific: patternRequired enforces at least one Id-like key in this $defs block when identity is present.", "patternRequired": ["^[A-Za-z][A-Za-z0-9._-]*Id$"], "minProperties": 1, "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "contains": {"const": "identity"}}, "properties": {"type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#IdentityValue"}}}}}}, "MeritValue": {"identity": "TYPE-MeritValue", "name": "MeritValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["number", "integer"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}}]}}, "MeritProp": {"identity": "TYPE-MeritProp", "name": "MeritProp", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"merit": {"$ref": "#MeritValue"}}}}}}, "ExtractionSchemaValue": {"identity": "TYPE-ExtractionSchemaValue", "name": "ExtractionSchemaValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchemaValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$schema", "$anchor", "type"], "properties": {"$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}}, "allOf": [{"if": {"anyOf": [{"properties": {"properties": {"type": "object", "properties": {"identity": {}}}}}, {"properties": {"required": {"type": "array", "contains": {"const": "identity"}}}}]}, "then": {"$ref": "#IdentityProp"}}, {"if": {"properties": {"properties": {"type": "object", "properties": {"merit": {}}}}}, "then": {"$ref": "#MeritProp"}}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "unevaluatedProperties": false}}, "ExtractionSchema": {"identity": "TYPE-ExtractionSchema", "name": "ExtractionSchema", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["extractionSchema"], "properties": {"extractionSchema": {"$ref": "#ExtractionSchemaValue"}}}}, "ResourceFormatId": {"identity": "TYPE-ResourceFormatId", "name": "ResourceFormatId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceFormatId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORMAT-.+$"}}, "ResourceTypeId": {"identity": "TYPE-ResourceTypeId", "name": "ResourceTypeId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceTypeId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^TYPE-.+$"}}, "ResourceType": {"identity": "TYPE-ResourceType", "name": "ResourceType", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceType", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity", "resourceFormatId"], "properties": {"identity": {"$ref": "#ResourceTypeId"}, "resourceFormatId": {"$ref": "#ResourceFormatId"}}}, {"$ref": "#Documented"}, {"$ref": "#ExtractionSchema"}, {"if": {"$comment": "If resourceFormatId is FORMAT-ApplicationJson, then extractor must not be present, but if resourceFormatId is not FORMAT-ApplicationJson, then extractor must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor.", "properties": {"resourceFormatId": {"const": "FORMAT-ApplicationJson"}}}, "then": {"not": {"required": ["extractor"]}}, "else": {"type": "object", "required": ["extractor"], "properties": {"extractor": {"type": "string", "format": "uri"}}}}]}}, "ResourceFormat": {"identity": "TYPE-ResourceFormat", "name": "ResourceFormat", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceFormat", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity"], "properties": {"identity": {"$ref": "#ResourceFormatId"}}}, {"$ref": "#Documented"}]}}, "ResourceRoleId": {"identity": "TYPE-ResourceRoleId", "name": "ResourceRoleId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceRoleId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^ROLE-.+$"}}, "ResourceId": {"identity": "TYPE-ResourceId", "name": "ResourceId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "", "pattern": "^RESOURCE-.+$"}}, "ResourceRoleValue": {"identity": "TYPE-ResourceRoleValue", "name": "ResourceRoleValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceRoleValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "A ResourceRole does not have a self-contained identity, as it is always defined in the context of a RoleMap.", "required": ["resourceTypeId"], "properties": {"resourceTypeId": {"$ref": "#ResourceTypeId"}}}, {"$ref": "#Documented"}]}}, "RoleMap": {"identity": "TYPE-RoleMap", "name": "RoleMap", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#ResourceRoleId"}, "additionalProperties": {"$ref": "#ResourceRoleValue"}}}, "RolesInner": {"identity": "TYPE-RolesInner", "name": "RolesInner", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RolesInner", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputMap", "outputMap"], "properties": {"inputMap": {"$ref": "#RoleMap"}, "outputMap": {"$ref": "#RoleMap"}}, "unevaluatedProperties": false}}, "RolesOuter": {"identity": "TYPE-RolesOuter", "name": "RolesOuter", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RolesOuter", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roles"], "properties": {"roles": {"$ref": "#RolesInner"}}}}, "JobId": {"identity": "TYPE-JobId", "name": "JobId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "jobFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "JobId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "", "pattern": "^JOB-.+$"}}, "Job": {"identity": "TYPE-Job", "name": "Job", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJob", "extractor": "https://extractors.toolproof.com/v0/JobExtractor.js", "extractionSchema": {"$anchor": "Job", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity", "implementation"], "properties": {"identity": {"$ref": "#JobId"}, "implementation": {"type": "string", "format": "uri"}}}, {"$ref": "#Documented"}, {"$ref": "#RolesOuter"}], "unevaluatedProperties": false}}, "RoleBindingMap": {"identity": "TYPE-RoleBindingMap", "name": "RoleBindingMap", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#ResourceRoleId"}, "additionalProperties": {"$ref": "#ResourceId"}}}, "RoleBindingsInner": {"identity": "TYPE-RoleBindingsInner", "name": "RoleBindingsInner", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingsInner", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputBindingMap", "outputBindingMap"], "properties": {"inputBindingMap": {"$ref": "#RoleBindingMap"}, "outputBindingMap": {"$ref": "#RoleBindingMap"}}, "unevaluatedProperties": false}}, "RoleBindingsOuter": {"identity": "TYPE-RoleBindingsOuter", "name": "RoleBindingsOuter", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingsOuter", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roleB<PERSON>ings"], "properties": {"roleBindings": {"$ref": "#RoleBindingsInner"}}}}, "ResourceSocket": {"identity": "TYPE-ResourceSocket", "name": "ResourceSocket", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceSocket", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["resourceRoleId", "executionId"], "properties": {"resourceRoleId": {"$ref": "#ResourceRoleId"}, "executionId": {"$ref": "#ExecutionId"}}}}, "CreationContext": {"identity": "TYPE-CreationContext", "name": "CreationContext", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "CreationContext", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["creationContext"], "properties": {"creationContext": {"$ref": "#ResourceSocket"}}}}, "ResourceBase": {"identity": "TYPE-ResourceBase", "name": "ResourceBase", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity", "resourceTypeId"], "properties": {"identity": {"$ref": "#ResourceId"}, "resourceTypeId": {"$ref": "#ResourceTypeId"}}}, {"$ref": "#CreationContext"}]}}, "ResourceKind": {"identity": "TYPE-ResourceKind", "name": "ResourceKind", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceKind", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"enum": ["missing", "potential-input", "potential-output", "materialized"]}}}}, "ResourceMissing": {"identity": "TYPE-ResourceMissing", "name": "ResourceMissing", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMissing", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#ResourceBase"}, {"$ref": "#ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "missing"}}}], "unevaluatedProperties": false}}, "PendingRef": {"identity": "TYPE-PendingRef", "name": "PendingRef", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "PendingRef", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["pendingRef"], "properties": {"pendingRef": {"$comment": "This points to a resource created at a previous step in the same workflow. This resource is not yet materialized at the time of defining the workflow, but will be materialized when the step using this resource is executed.The Engine resolves this ref at runtime.", "$ref": "#ResourceSocket"}}}}, "ResourcePotentialInput": {"identity": "TYPE-ResourcePotentialInput", "name": "ResourcePotentialInput", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialInput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#ResourceBase"}, {"$ref": "#ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-input"}}}, {"$ref": "#PendingRef"}], "unevaluatedProperties": false}}, "ResourcePotentialOutput": {"identity": "TYPE-ResourcePotentialOutput", "name": "ResourcePotentialOutput", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialOutput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#ResourceBase"}, {"$ref": "#ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-output"}}}], "unevaluatedProperties": false}}, "Timestamp": {"identity": "TYPE-Timestamp", "name": "Timestamp", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Timestamp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}}}}, "Path": {"identity": "TYPE-Path", "name": "Path", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Path", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["path"], "properties": {"path": {"type": "string"}}}}, "ResourceMetaBase": {"identity": "TYPE-ResourceMetaBase", "name": "ResourceMetaBase", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMetaBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#ResourceBase"}, {"$ref": "#ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "materialized"}}}, {"$ref": "#Timestamp"}, {"$ref": "#Path"}]}}, "ResourceMeta": {"identity": "TYPE-ResourceMeta", "name": "ResourceMeta", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "$ref": "#ResourceMetaBase", "unevaluatedProperties": false}}, "JsonValue": {"identity": "TYPE-JsonValue", "name": "JsonValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "JsonValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#J<PERSON>V<PERSON>ue"}}, {"type": "object", "additionalProperties": {"$ref": "#J<PERSON>V<PERSON>ue"}}]}}, "Resource": {"identity": "TYPE-Resource", "name": "Resource", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Resource", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#ResourceMetaBase"}, {"required": ["extractedData"], "properties": {"extractedData": {"$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property.", "type": "object", "additionalProperties": {"$ref": "#J<PERSON>V<PERSON>ue"}}}}], "unevaluatedProperties": false}}, "ExecutionId": {"identity": "TYPE-ExecutionId", "name": "ExecutionId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExecutionId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^EXECUTION-.+$"}}, "Execution": {"identity": "TYPE-Execution", "name": "Execution", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Execution", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"type": "object", "required": ["identity", "jobId"], "properties": {"identity": {"$ref": "#ExecutionId"}, "jobId": {"$ref": "#JobId"}}}, {"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#RoleBindingsOuter"}]}}, "ConditionalWrapper": {"identity": "TYPE-ConditionalWrapper", "name": "ConditionalWrapper", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ConditionalWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["when", "what"], "properties": {"when": {"$ref": "#WorkStep"}, "what": {"$ref": "#WorkStep"}}, "unevaluatedProperties": false}}, "StepKind": {"identity": "TYPE-<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "<PERSON><PERSON><PERSON>", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}}}, "WorkStepId": {"identity": "TYPE-WorkStepId", "name": "WorkStepId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKSTEP-.+$"}}, "WorkStep": {"identity": "TYPE-WorkStep", "name": "WorkStep", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#StepK<PERSON>"}, {"type": "object", "required": ["identity", "kind", "execution"], "properties": {"identity": {"$ref": "#WorkStepId"}, "kind": {"const": "work"}, "execution": {"$ref": "#Execution"}}}]}}, "BranchStepId": {"identity": "TYPE-BranchStepId", "name": "BranchStepId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^BRANCHSTEP-.+$"}}, "BranchStep": {"identity": "TYPE-BranchStep", "name": "BranchStep", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#StepK<PERSON>"}, {"type": "object", "required": ["identity", "kind", "cases"], "properties": {"identity": {"$ref": "#BranchStepId"}, "kind": {"const": "branch"}, "cases": {"type": "array", "items": {"$ref": "#ConditionalWrapper"}, "minItems": 1, "uniqueItems": true}}}]}}, "WhileStepId": {"identity": "TYPE-WhileStepId", "name": "WhileStepId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WHILESTEP-.+$"}}, "WhileStep": {"identity": "TYPE-WhileStep", "name": "WhileStep", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#StepK<PERSON>"}, {"type": "object", "required": ["identity", "kind", "case"], "properties": {"identity": {"$ref": "#WhileStepId"}, "kind": {"const": "while"}, "case": {"$ref": "#ConditionalWrapper"}}}]}}, "ForStepId": {"identity": "TYPE-ForStepId", "name": "ForStepId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORSTEP-.+$"}}, "ForStep": {"identity": "TYPE-ForStep", "name": "ForStep", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#StepK<PERSON>"}, {"type": "object", "required": ["identity", "kind", "case"], "properties": {"identity": {"$ref": "#ForStepId"}, "kind": {"const": "for"}, "case": {"$ref": "#ConditionalWrapper"}}}]}}, "Step": {"identity": "TYPE-Step", "name": "Step", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Step", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#WorkStep"}, {"$ref": "#BranchStep"}, {"$ref": "#WhileStep"}, {"$ref": "#ForStep"}], "unevaluatedProperties": false}}, "StatelessStrategyId": {"identity": "TYPE-StatelessStrategyId", "name": "StatelessStrategyId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatelessStrategyId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STATELESS_STRATEGY-.+$"}}, "StatelessStrategy": {"identity": "TYPE-StatelessStrategy", "name": "StatelessStrategy", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatelessStrategy", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"type": "object", "required": ["identity", "steps"], "properties": {"identity": {"$ref": "#StatelessStrategyId"}, "steps": {"type": "array", "items": {"$ref": "#Step"}, "uniqueItems": true}}}], "unevaluatedProperties": false}}, "StrategyStateValue": {"identity": "TYPE-StrategyStateValue", "name": "StrategyStateValue", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyStateValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#ExecutionId"}, "additionalProperties": {"type": "object", "propertyNames": {"$ref": "#ResourceRoleId"}, "additionalProperties": {"oneOf": [{"$ref": "#ResourceMissing"}, {"$ref": "#ResourcePotentialInput"}, {"$ref": "#ResourcePotentialOutput"}, {"$ref": "#Resource"}]}}}}, "StrategyState": {"identity": "TYPE-StrategyState", "name": "StrategyState", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StrategyState", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["strategyState"], "properties": {"strategyState": {"$ref": "#StrategyStateValue"}}}}, "StatefulStrategyId": {"identity": "TYPE-StatefulStrategyId", "name": "StatefulStrategyId", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatefulStrategyId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^STATEFUL_STRATEGY-.+$"}}, "StatefulStrategy": {"identity": "TYPE-StatefulStrategy", "name": "StatefulStrategy", "description": "dummy-description", "resourceFormatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StatefulStrategy", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["identity", "statelessStrategy"], "properties": {"identity": {"$ref": "#StatefulStrategyId"}, "statelessStrategy": {"$ref": "#StatelessStrategy"}}}, {"$ref": "#StrategyState"}]}}}}}