// Re-export JSON schemas via .ts shims to avoid .json re-exports in declarations
export { default as GenesisSchema } from './genesis/generated/schemas/Genesis.js';
export { default as Genesis } from './genesis/resourceTypes/Genesis.js';
export { default as JobSchema } from './genesis/generated/schemas/Job.js';

export type {
  Resource_ResourceFormat as Resource_ResourceFormatJson
} from './_lib/types/Resource_ResourceFormat.js';
export type {
  Resource_ResourceType as Resource_ResourceTypeJson
} from './_lib/types/Resource_ResourceType.js';
export type {
  Resource_Job as Resource_JobJson
} from './_lib/types/Resource_Job.js';


export type {
  Documented as DocumentedJson,
  ResourceFormatId as ResourceFormatIdJson,
  ResourceFormat as ResourceFormatJson,
  ExtractionSchema as ExtractionSchemaJson,
  ExtractionSchemaValue as ExtractionSchemaValueJson,
  IdentityProp as IdentityPropJson,
  MeritProp as MeritPropJson,
  ResourceTypeId as ResourceTypeIdJson,
  ResourceType as ResourceType<PERSON>son,
  ResourceRoleId as ResourceRoleIdJson,
  ResourceRoleValue as ResourceRoleValueJson,
  ExecutionId as ExecutionIdJson,
  Execution as ExecutionJson,
  ConditionalWrapper as ConditionalWrapperJson,
  RoleMap as RoleMapJson,
  RolesOuter as RolesOuterJson,
  RoleBindingMap as RoleBindingMapJson,
  RoleBindingsOuter as RoleBindingsOuterJson,
  ResourceId as ResourceIdJson,
  WorkStepId as WorkStepIdJson,
  BranchStepId as BranchStepIdJson,
  WhileStepId as WhileStepIdJson,
  ForStepId as ForStepIdJson,
  WorkStep as WorkStepJson,
  BranchStep as BranchStepJson,
  WhileStep as WhileStepJson,
  ForStep as ForStepJson,
  Step as StepJson,
  ResourceSocket as ResourceSocketJson,
  ResourcePotentialInput as ResourcePotentialInputJson,
  ResourcePotentialOutput as ResourcePotentialOutputJson,
  ResourceMetaBase as ResourceMetaJson, // ATTENTION: type not generated for ResourceMeta
  Resource as ResourceJson,
  StrategyState as StrategyStateJson,
  StrategyStateValue as StrategyStateValueJson,
  StatelessStrategyId as StatelessStrategyIdJson,
  StatelessStrategy as StatelessStrategyJson,
  StatefulStrategyId as StatefulStrategyIdJson,
  StatefulStrategy as StatefulStrategyJson,
  Job as JobJson,
  JsonValue as JsonValueJson,
} from './_lib/types/types.js';

// Re-export brand factories so consumers can construct branded IDs at runtime.
export {
  unsafeBrand,
  asResourceTypeId,
  asResourceRoleId,
  asExecutionId,
  asResourceId,
  asWorkStepId,
  asBranchStepId,
  asForStepId,
  asResourceFormatId,
  asWhileStepId,
  asStatelessStrategyId,
  asStatefulStrategyId,
  asResourceTypeIds,
  asResourceRoleIds,
  asExecutionIds,
  asResourceIds,
  asWorkStepIds,
  asBranchStepIds,
  asForStepIds,
  asResourceFormatIds,
  asWhileStepIds,
  asStatelessStrategyIds,
  asStatefulStrategyIds,
} from './scripts/brandFactories.js';
