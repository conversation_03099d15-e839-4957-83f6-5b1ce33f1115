import type { Job } from './types/types.js';
import type { Resource_Job } from './types/Resource_Job.js';


const Job: Job = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    implementation: 'implementation',
    roles: {
        inputMap: {
            'ROLE-1234': {
                resourceTypeId: 'TYPE-1234',
                name: 'name',
                description: 'description'
            }
        },
        outputMap: {
            'ROLE-5678': {
                resourceTypeId: 'TYPE-5678',
                name: 'name',
                description: 'description'
            }
        }
    }
};

const jobAlpha = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    implementation: 'implementation',
    roles: {
        inputMap: {
            'data-input': {
                resourceTypeId: 'resourceTypeId',
                name: 'name',
                description: 'description'
            }
        },
        outputMap: {
            'data-output': {
                resourceTypeId: 'resourceTypeId',
                name: 'name',
                description: 'description'
            }
        }
    }
};

const jobBeta = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    implementation: 'implementation',
    roles: {
        inputMap: {
            'data-input': {
                resourceTypeId: 'resourceTypeId',
                name: 'name',
                description: 'description'
            }
        },
        /* outputMap: {
            'data-output': {
                resourceTypeId: 'resourceTypeId',
                name: 'name',
                description: 'description'
            }
        } */
    }
};


// This checks (no error expected); we're providing all required fields
const ResourceJobAlpha: Resource_Job = {
    identity: 'RESOURCE-1',
    resourceTypeId: 'TYPE-ID',
    creationContext: {
        resourceRoleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'materialized',
    timestamp: 'timestamp',
    path: 'path',
    extractedData: jobAlpha
};

// @ts-expect-error Missing path property
// This SHOULD fail (missing path) once Resource_Job enforces Path strictly.
const ResourceJobAlpha2: Resource_Job = {
    identity: 'RESOURCE-3',
    resourceTypeId: 'TYPE-ID',
    creationContext: {
        resourceRoleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'materialized',
    timestamp: 'timestamp',
    // path: 'path',
    extractedData: jobAlpha
};

// @ts-expect-error Missing extractedData property
// This should fail; we're missing extractedData
const ResourceJobAlpha3: Resource_Job = {
    identity: 'RESOURCE-2',
    resourceTypeId: 'TYPE-ID',
    creationContext: {
        resourceRoleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'materialized',
    timestamp: 'timestamp',
    path: 'path',
    // extractedData: jobAlpha
};

// This SHOULD fail (missing roles.outputMap) once Job's roles is strictly enforced.
const ResourceJobBeta: Resource_Job = {
    identity: 'RESOURCE-4',
    resourceTypeId: 'TYPE-ID',
    creationContext: {
        resourceRoleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'materialized',
    timestamp: 'timestamp',
    path: 'path',
    // @ts-expect-error Missing outputMap inside roles (Job requires outputMap)
    extractedData: jobBeta
};


