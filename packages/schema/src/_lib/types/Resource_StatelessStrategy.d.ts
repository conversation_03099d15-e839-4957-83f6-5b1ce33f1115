// Auto-generated strict composite type. Do not edit.
export type Resource_StatelessStrategy = ResourceMetaBase & {
  extractedData: StatelessStrategy;
};
export type ResourceMetaBase = ResourceBase &
  ResourceKind & {
    kind: "materialized";
  } & Timestamp &
  Path;
export type ResourceBase = {
  identity: string;
  resourceTypeId: string;
} & CreationContext;
export type StatelessStrategy = {
  identity: string;
  steps: Step[];
};
export type Step = WorkStep | BranchStep | WhileStep | ForStep;
export type WorkStep = StepKind & {
  execution: Execution;
  identity: string;
  kind: "work";
};
export type Execution = {
  identity: string;
  jobId: string;
} & RoleBindingsOuter;
export type BranchStep = StepKind & {
  /**
   * @minItems 1
   */
  cases: [ConditionalWrapper, ...ConditionalWrapper[]];
  identity: string;
  kind: "branch";
};
export type WhileStep = StepKind & {
  case: ConditionalWrapper;
  identity: string;
  kind: "while";
};
export type ForStep = StepKind & {
  case: ConditionalWrapper;
  identity: string;
  kind: "for";
};

export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: string;
  resourceRoleId: string;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
export interface RoleBindingsOuter {
  roleBindings: RoleBindingsInner;
}
export interface RoleBindingsInner {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
export interface RoleBindingMap {
  [k: string]: string;
}
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
}
