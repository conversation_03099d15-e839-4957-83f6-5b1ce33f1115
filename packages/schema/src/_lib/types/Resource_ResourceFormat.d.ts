// Auto-generated strict composite type. Do not edit.
export type Resource_ResourceFormat = ResourceMetaBase & {
  extractedData: ResourceFormat;
};
export type ResourceMetaBase = ResourceBase &
  ResourceKind & {
    kind: "materialized";
  } & Timestamp &
  Path;
export type ResourceBase = {
  identity: string;
  resourceTypeId: string;
} & CreationContext;
export type ResourceFormat = {
  identity: string;
} & Documented;
export type Documented = Name & Description;

export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: string;
  resourceRoleId: string;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}
export interface Name {
  name: string;
}
export interface Description {
  description: string;
}
