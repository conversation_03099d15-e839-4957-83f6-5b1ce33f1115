// Auto-generated from JSON schemas. Do not edit.

/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "BranchStep".
 */
export type BranchStep =
 StepKind & {
  /**
   * @minItems 1
   */
  cases: [ConditionalWrapper, ...ConditionalWrapper[]];
  identity: BranchStepId;
  kind: "branch";
};
/**
 * This interface was referenced by `Genesis<PERSON>son`'s JSON-Schema
 * via the `definition` "WorkStep".
 */
export type WorkStep =
 StepKind & {
  execution: Execution;
  identity: WorkStepId;
  kind: "work";
};
/**
 * This interface was referenced by `GenesisJson`'s J<PERSON><PERSON>-Schema
 * via the `definition` "Execution".
 */
export type Execution =
 {
  identity: ExecutionId;
  jobId: JobId;
} & RoleBindingsOuter;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExecutionId".
 */
export type ExecutionId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s J<PERSON><PERSON>-<PERSON>hema
 * via the `definition` "JobId".
 */
export type JobId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceId".
 */
export type ResourceId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkStepId".
 */
export type WorkStepId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "BranchStepId".
 */
export type BranchStepId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceRoleId".
 */
export type ResourceRoleId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Documented =
 Name & Description;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchemaValue".
 */
export type ExtractionSchemaValue =
 {
} & {
  $schema: "https://json-schema.org/draft/2020-12/schema";
  $defs?: {
  };
  type: "object";
  allOf?: Array<{[k: string]: unknown}>;
  properties?: {
  };
  required?: string[];
  additionalProperties?: false;
  unevaluatedProperties?: false;
  $anchor: string;
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStep".
 */
export type ForStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: ForStepId;
  kind: "for";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStepId".
 */
export type ForStepId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Job".
 */
export type Job =
 {
  identity: JobId;
  implementation: string;
} & Documented &
  RolesOuter;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceRoleValue".
 */
export type ResourceRoleValue =
 {
  resourceTypeId: ResourceTypeId;
} & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceTypeId".
 */
export type ResourceTypeId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "JsonValue".
 */
export type JsonValue =
  | null
  | boolean
  | number
  | string
  | JsonValue
  | {
      [k: string]: JsonValue;
    };
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritValue".
 */
export type MeritValue =
  | {
      type: "number" | "integer";
}
  | {
      enum: [{type: "array"; items: {type: "number"}; minItems: 1}];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Resource".
 */
export type Resource =
 ResourceMetaBase & {
  extractedData: {
    [k: string]: JsonValue;
  };
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMetaBase".
 */
export type ResourceMetaBase =
 ResourceBase &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceBase".
 */
export type ResourceBase =
 {
  identity: ResourceId;
  resourceTypeId: ResourceTypeId;
} & CreationContext;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceFormat".
 */
export type ResourceFormat =
 {
  identity: ResourceFormatId;
} & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceFormatId".
 */
export type ResourceFormatId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMeta".
 */
export type ResourceMetaBase1 =
 ResourceBase &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMissing".
 */
export type ResourceMissing =
 ResourceBase &
  ResourceKind & {
    kind: "missing";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialInput".
 */
export type ResourcePotentialInput =
 ResourceBase &
  ResourceKind & {
    kind: "potential-input";
} & PendingRef;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialOutput".
 */
export type ResourcePotentialOutput =
 ResourceBase &
  ResourceKind & {
    kind: "potential-output";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceType".
 */
export type ResourceType =
 {
  identity: ResourceTypeId;
  resourceFormatId: ResourceFormatId;
} & Documented &
  ExtractionSchema & {
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatefulStrategy".
 */
export type StatefulStrategy =
 {
  identity: StatefulStrategyId;
  statelessStrategy: StatelessStrategy;
} & StrategyState;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatefulStrategyId".
 */
export type StatefulStrategyId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatelessStrategy".
 */
export type StatelessStrategy =
 {
  identity: StatelessStrategyId;
  steps: Step[];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StatelessStrategyId".
 */
export type StatelessStrategyId =
 string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Step".
 */
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStep".
 */
export type WhileStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: WhileStepId;
  kind: "while";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStepId".
 */
export type WhileStepId =
 string;

export interface GenesisJson {
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StepKind".
 */
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ConditionalWrapper".
 */
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
}
export interface RoleBindingsOuter {
  roleBindings: RoleBindingsInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingsInner".
 */
export interface RoleBindingsInner {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingMap".
 */
export type RoleBindingMap = Record<ResourceRoleId, ResourceId>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "CreationContext".
 */
export interface CreationContext {
  creationContext: ResourceSocket;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceSocket".
 */
export interface ResourceSocket {
  executionId: ExecutionId;
  resourceRoleId: ResourceRoleId;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Description".
 */
export interface Description {
  description: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Name".
 */
export interface Name {
  name: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchema".
 */
export interface ExtractionSchema {
  extractionSchema: ExtractionSchemaValue;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdSchemaValue".
 *
 * This interface was referenced by `undefined`'s JSON-Schema definition
 * via the `patternProperty` "^[A-Za-z][A-Za-z0-9._-]*Id$".
 */
export interface IdSchemaValue {
  type: "string" | "number" | "integer" | "boolean";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentityProp".
 */
export interface IdentityProp {
  $defs: {
};
  properties: {
    identity: IdentityValue;
};
  required?: string[];
  additionalProperties?: unknown;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentityValue".
 */
export interface IdentityValue {
  $ref: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RolesOuter".
 */
export interface RolesOuter {
  roles: RolesInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RolesInner".
 */
export interface RolesInner {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleMap".
 */
export type RoleMap = Record<ResourceRoleId, ResourceRoleValue>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritProp".
 */
export interface MeritProp {
  properties?: {
    merit?: MeritValue;
};
  required?: [];
  additionalProperties?: unknown;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Path".
 */
export interface Path {
  path: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "PendingRef".
 */
export interface PendingRef {
  pendingRef: ResourceSocket1;
}
export interface ResourceSocket1 {
  executionId: ExecutionId;
  resourceRoleId: ResourceRoleId;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceKind".
 */
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Timestamp".
 */
export interface Timestamp {
  timestamp: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingsOuter".
 */
export interface RoleBindingsOuter1 {
  roleBindings: RoleBindingsInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyState".
 */
export interface StrategyState {
  strategyState: StrategyStateValue;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StrategyStateValue".
 */
export interface StrategyStateValue {
  [k: string]: {
    [k: string]: ResourceMissing | ResourcePotentialInput | ResourcePotentialOutput | Resource;
  };
}

export type Normalized =
 {
  identity: JobId;
  implementation: string;
} & Documented &
  RolesOuter;
export type JobId =
 string;
export type Documented =
 Name & Description;
export type ResourceRoleValue =
 {
  resourceTypeId: ResourceTypeId;
} & Documented;
export type ResourceTypeId =
 string;

export interface Name {
  name: string;
}
export interface Description {
  description: string;
}
export interface RolesOuter {
  roles: RolesInner;
}
export interface RolesInner {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
export type RoleMap = Record<ResourceRoleId, ResourceRoleValue>;

export type Normalized =
 {
  identity: ResourceFormatId;
} & Documented;
export type ResourceFormatId =
 string;
export type Documented =
 Name & Description;

export interface Name {
  name: string;
}
export interface Description {
  description: string;
}

export type Normalized =
 {
  identity: ResourceTypeId;
  resourceFormatId: ResourceFormatId;
} & Documented &
  ExtractionSchema & {
};
export type ResourceTypeId =
 string;
export type ResourceFormatId =
 string;
export type Documented =
 Name & Description;
export type ExtractionSchemaValue =
 {
} & {
  $schema: "https://json-schema.org/draft/2020-12/schema";
  $defs?: {
  };
  type: "object";
  allOf?: Array<{[k: string]: unknown}>;
  properties?: {
  };
  required?: string[];
  additionalProperties?: false;
  unevaluatedProperties?: false;
  $anchor: string;
};

export interface Name {
  name: string;
}
export interface Description {
  description: string;
}
export interface ExtractionSchema {
  extractionSchema: ExtractionSchemaValue;
}

export type Normalized =
 {
  identity: StatefulStrategyId;
  statelessStrategy: StatelessStrategy;
} & StrategyState;
export type StatefulStrategyId =
 string;
export type StatelessStrategy =
 {
  identity: StatelessStrategyId;
  steps: Step[];
};
export type StatelessStrategyId =
 string;
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
export type WorkStep =
 StepKind & {
  execution: Execution;
  identity: WorkStepId;
  kind: "work";
};
export type Execution =
 {
  identity: ExecutionId;
  jobId: JobId;
} & RoleBindingsOuter;
export type ExecutionId =
 string;
export type JobId =
 string;
export type ResourceId =
 string;
export type WorkStepId =
 string;
export type BranchStep =
 StepKind & {
  /**
   * @minItems 1
   */
  cases: [ConditionalWrapper, ...ConditionalWrapper[]];
  identity: BranchStepId;
  kind: "branch";
};
export type BranchStepId =
 string;
export type WhileStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: WhileStepId;
  kind: "while";
};
export type WhileStepId =
 string;
export type ForStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: ForStepId;
  kind: "for";
};
export type ForStepId =
 string;
export type ResourceMissing =
 ResourceBase &
  ResourceKind & {
    kind: "missing";
};
export type ResourceBase =
 {
  identity: ResourceId;
  resourceTypeId: ResourceTypeId;
} & CreationContext;
export type ResourceTypeId =
 string;
export type ResourceRoleId =
 string;
export type ResourcePotentialInput =
 ResourceBase &
  ResourceKind & {
    kind: "potential-input";
} & PendingRef;
export type ResourcePotentialOutput =
 ResourceBase &
  ResourceKind & {
    kind: "potential-output";
};
export type Resource =
 ResourceMetaBase & {
  extractedData: {
    [k: string]: JsonValue;
  };
};
export type ResourceMetaBase =
 ResourceBase &
  ResourceKind & {
    kind: "materialized";
} & Timestamp &
  Path;
export type JsonValue =
  | null
  | boolean
  | number
  | string
  | JsonValue
  | {
      [k: string]: JsonValue;
    };

export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
export interface RoleBindingsOuter {
  roleBindings: RoleBindingsInner;
}
export interface RoleBindingsInner {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
export type RoleBindingMap = Record<ResourceRoleId, ResourceId>;
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
}
export interface StrategyState {
  strategyState: StrategyStateValue;
}
export interface StrategyStateValue {
  [k: string]: {
    [k: string]: ResourceMissing | ResourcePotentialInput | ResourcePotentialOutput | Resource;
  };
}
export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: ExecutionId;
  resourceRoleId: ResourceRoleId;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface PendingRef {
  pendingRef: ResourceSocket1;
}
export interface ResourceSocket1 {
  executionId: ExecutionId;
  resourceRoleId: ResourceRoleId;
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}

export type Normalized =
 {
  identity: StatelessStrategyId;
  steps: Step[];
};
export type StatelessStrategyId =
 string;
export type Step =
 WorkStep | BranchStep | WhileStep | ForStep;
export type WorkStep =
 StepKind & {
  execution: Execution;
  identity: WorkStepId;
  kind: "work";
};
export type Execution =
 {
  identity: ExecutionId;
  jobId: JobId;
} & RoleBindingsOuter;
export type ExecutionId =
 string;
export type JobId =
 string;
export type ResourceId =
 string;
export type WorkStepId =
 string;
export type BranchStep =
 StepKind & {
  /**
   * @minItems 1
   */
  cases: [ConditionalWrapper, ...ConditionalWrapper[]];
  identity: BranchStepId;
  kind: "branch";
};
export type BranchStepId =
 string;
export type WhileStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: WhileStepId;
  kind: "while";
};
export type WhileStepId =
 string;
export type ForStep =
 StepKind & {
  case: ConditionalWrapper;
  identity: ForStepId;
  kind: "for";
};
export type ForStepId =
 string;

export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
export interface RoleBindingsOuter {
  roleBindings: RoleBindingsInner;
}
export interface RoleBindingsInner {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
export type RoleBindingMap = Record<ResourceRoleId, ResourceId>;
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
}

