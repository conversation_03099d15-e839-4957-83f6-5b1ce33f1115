// Auto-generated strict composite type. Do not edit.
export type Resource_Job = ResourceMetaBase & {
  extractedData: Job;
};
export type ResourceMetaBase = ResourceBase &
  ResourceKind & {
    kind: "materialized";
  } & Timestamp &
  Path;
export type ResourceBase = {
  identity: string;
  resourceTypeId: string;
} & CreationContext;
export type Job = {
  identity: string;
  implementation: string;
} & Documented &
  RolesOuter;
export type Documented = Name & Description;
export type ResourceRoleValue = {
  resourceTypeId: string;
} & Documented;

export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: string;
  resourceRoleId: string;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}
export interface Name {
  name: string;
}
export interface Description {
  description: string;
}
export interface RolesOuter {
  roles: RolesInner;
}
export interface RolesInner {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
export interface RoleMap {
  [k: string]: ResourceRoleValue;
}
