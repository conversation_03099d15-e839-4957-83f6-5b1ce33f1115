// Auto-generated strict composite type. Do not edit.
export type Resource_ResourceType = ResourceMetaBase & {
  extractedData: ResourceType;
};
export type ResourceMetaBase = ResourceBase &
  ResourceKind & {
    kind: "materialized";
  } & Timestamp &
  Path;
export type ResourceBase = {
  identity: string;
  resourceTypeId: string;
} & CreationContext;
export type ResourceType = {
  identity: string;
  resourceFormatId: string;
} & Documented &
  ExtractionSchema & {
  };
export type Documented = Name & Description;
export type ExtractionSchemaValue = {
} & {
  $schema: "https://json-schema.org/draft/2020-12/schema";
  $defs?: {
  };
  type: "object";
  allOf?: {
  }[];
  properties?: {
  };
  required?: string[];
  additionalProperties?: false;
  unevaluatedProperties?: false;
  $anchor: string;
};

export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: string;
  resourceRoleId: string;
}
export interface ResourceKind {
  kind: "missing" | "potential-input" | "potential-output" | "materialized";
}
export interface Timestamp {
  timestamp: string;
}
export interface Path {
  path: string;
}
export interface Name {
  name: string;
}
export interface Description {
  description: string;
}
export interface ExtractionSchema {
  extractionSchema: ExtractionSchemaValue;
}
