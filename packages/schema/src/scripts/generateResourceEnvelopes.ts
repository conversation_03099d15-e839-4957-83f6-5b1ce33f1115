import fs from 'fs';
import path from 'path';
import { getConfig } from './_lib/config.js';

/**
 * Generate Resource envelopes for all ResourceTypes defined in Genesis.json
 * 
 * This script wraps each ResourceType definition (from $defs) with a Resource envelope
 * that conforms to the Resource.extractionSchema pattern defined in Genesis.json.
 * 
 * For the top-level Genesis ResourceType, extractedData is set to {} to avoid
 * duplicating the entire $defs object.
 * 
 * Resource identities follow the pattern RESOURCE-{Name} where Name is the key
 * from the $defs object. Genesis itself uses RESOURCE-Genesis.
 * 
 * Usage: node ./dist/scripts/generateResourceEnvelopes.js
 */
async function main() {
    const config = getConfig();
    // Use normalized version with anchor refs rewritten to pointers
    const genesisSourcePath = config.getSourcePath().replace('.json', '.normalized.json');
    const outputPath = path.join(
        config.getRoot(),
        'src',
        'genesis',
        'generated',
        'resources',
        'Genesis.json'
    );

    if (!fs.existsSync(genesisSourcePath)) {
        console.error(`Genesis source file not found at ${genesisSourcePath}`);
        process.exit(1);
    }

    const raw = fs.readFileSync(genesisSourcePath, 'utf-8');
    const genesis = JSON.parse(raw);

    // Validate that this is a ResourceType with extractionSchema
    if (!genesis.extractionSchema || !genesis.extractionSchema.$defs) {
        console.error('Genesis.json must have extractionSchema.$defs');
        process.exit(1);
    }

    const defs = genesis.extractionSchema.$defs;
    const defKeys = Object.keys(defs);

    // Generate Resource envelopes
    const resources: Record<string, any> = {};

    // Genesis timestamp: 2025-11-30T00:00:00.000Z marks the genesis of ToolProof
    const genesisTimestamp = '2025-11-30T00:00:00.000Z';

    // First entry is Genesis itself with empty extractedData
    resources['Genesis'] = {
        identity: 'RESOURCE-Genesis',
        resourceTypeId: 'TYPE-ResourceType',
        creationContext: {
            resourceRoleId: 'ROLE-Genesis',
            executionId: 'EXECUTION-Genesis'
        },
        kind: 'materialized',
        timestamp: genesisTimestamp,
        extractedData: {}
    };

    // Generate resources for all other $defs
    defKeys.forEach((defName) => {
        const defValue = defs[defName];

        resources[defName] = {
            identity: `RESOURCE-${defName}`,
            resourceTypeId: 'TYPE-ResourceType',
            creationContext: {
                resourceRoleId: 'ROLE-Genesis',
                executionId: `EXECUTION-${defName}`
            },
            kind: 'materialized',
            timestamp: genesisTimestamp,
            extractedData: defValue
        };
    });

    // Ensure output directory exists
    const outputDir = path.dirname(outputPath);
    fs.mkdirSync(outputDir, { recursive: true });

    // Write the generated resources file
    fs.writeFileSync(outputPath, JSON.stringify(resources, null, 4) + '\n', 'utf-8');
    console.log(`Generated ${defKeys.length + 1} Resource envelopes -> ${outputPath}`);
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
