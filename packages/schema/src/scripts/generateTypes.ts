import fs from 'fs';
import path from 'path';
import { compileFromFile } from 'json-schema-to-typescript';
import { getConfig } from './_lib/config.js';

const config = getConfig();
const projectRoot = config.getRoot();
const inputDir = config.getOutputDir();
// We emit under src/_lib/types and dist/_lib/types
const srcLibTypesDir = config.getTypesSrcDir();
const srcLibOutputPath = config.getTypesSrcPath('types.d.ts');

// Build an index of all schema files by their basename
// This supports location-independent $id values where folder segments were removed
function buildSchemaIndex(root: string): Map<string, string> {
    const index = new Map<string, string>();
    const stack = [root];
    while (stack.length) {
        const current = stack.pop()!;
        const entries = fs.readdirSync(current, { withFileTypes: true });
        for (const entry of entries) {
            const full = path.join(current, entry.name);
            if (entry.isDirectory()) {
                // Skip dist or types output folders if present inside schemas (defensive)
                if (entry.name === 'node_modules' || entry.name.startsWith('.')) continue;
                stack.push(full);
            } else if (entry.isFile() && entry.name.endsWith('.json')) {
                if (entry.name === '.combined-schema.json') continue; // ignore temp file
                const baseName = entry.name; // keep extension for direct mapping
                if (index.has(baseName)) {
                    // Hard fail on collisions so they can be fixed explicitly
                    const existing = index.get(baseName)!;
                    throw new Error(
                        `Schema basename collision detected for "${baseName}"\n` +
                        `First:  ${existing}\n` +
                        `Second: ${full}\n` +
                        `Please rename one of the schemas to ensure unique basenames.`
                    );
                } else {
                    index.set(baseName, full);
                }
            }
        }
    }
    return index;
}

// List all schema files (relative to inputDir), excluding documentation and temp files
function listAllSchemaFiles(root: string): string[] {
    const files: string[] = [];
    const stack = [root];
    while (stack.length) {
        const current = stack.pop()!;
        const entries = fs.readdirSync(current, { withFileTypes: true });
        for (const entry of entries) {
            const full = path.join(current, entry.name);
            if (entry.isDirectory()) {
                if (entry.name === 'documentation' || entry.name === 'node_modules' || entry.name.startsWith('.')) continue;
                stack.push(full);
            } else if (entry.isFile() && entry.name.endsWith('.json')) {
                if (entry.name === '.combined-schema.json') continue; // ignore temp
                // produce path relative to root with posix separators
                const rel = path.relative(root, full).split(path.sep).join('/');
                files.push(rel);
            }
        }
    }
    files.sort(); // deterministic order
    return files;
}

async function main() {
    // Ensure src/_lib/types exists (we no longer write to src/types to avoid duplication)
    fs.mkdirSync(srcLibTypesDir, { recursive: true });
    const parts: string[] = [];
    parts.push('// Auto-generated from JSON schemas. Do not edit.\n');
    // Precompute index for location-independent IDs (filename only after version segment)
    const schemaIndex = buildSchemaIndex(inputDir);
    const idToCanonical: Record<string, string> = {};

    // Custom resolver to map our absolute schema IDs to local files, preventing HTTP fetches.
    const toolproofResolver = {
        order: 1,
        canRead: (file: any) => {
            let url: string = (typeof file.url === 'string' ? file.url : file.url?.href) || '';
            // Strip accidental wrapping quotes
            if ((url.startsWith("'") && url.endsWith("'")) || (url.startsWith('"') && url.endsWith('"'))) {
                url = url.slice(1, -1);
            }
            return config.isSchemaUrl(url);
        },
        read: (file: any) => {
            let url: string = (typeof file.url === 'string' ? file.url : file.url?.href) || '';
            // Strip accidental wrapping quotes
            if ((url.startsWith("'") && url.endsWith("'")) || (url.startsWith('"') && url.endsWith('"'))) {
                url = url.slice(1, -1);
            }
            // Strip hash part (anchors) for path resolution
            const noHash = url.split('#')[0];

            // Extract schema name using config
            const schemaName = config.extractSchemaName(noHash);
            const fileName = schemaName.endsWith('.json') ? schemaName : `${schemaName}.json`;

            // Resolve by basename only (location-independent IDs)
            const indexed = schemaIndex.get(fileName);
            if (indexed) {
                return fs.readFileSync(indexed, 'utf8');
            }

            throw new Error(
                `Toolproof resolver: could not locate schema for URL "${url}". ` +
                `Tried basename "${fileName}" in schema index. Ensure unique basenames and correct $id.`
            );
        }
    } as any;

    // Files to include in the combined schema (auto-discovered, excludes documentation)
    const toCompile = listAllSchemaFiles(inputDir);

    // Collect schema refs and defs; we'll compile files individually to avoid combined-schema traversal issues.
    const definitions: Record<string, unknown> = {};
    const includedNames: string[] = [];

    for (const fileName of toCompile) {
        const p = path.join(inputDir, fileName);
        if (!fs.existsSync(p)) {
            console.warn(`Schema file missing, skipping: ${p}`);
            continue;
        }
        // Definition key: basename without extension, with path segments removed
        const base = path.basename(fileName, '.json');
        // Prefer the schema's declared $id so all references point to the same absolute URI
        // This helps json-schema-to-typescript dedupe declarations instead of emitting FooJson and FooJson1
        let refValue = `./${fileName}`;
        try {
            const raw = fs.readFileSync(p, 'utf8');
            const parsed = JSON.parse(raw);
            if (parsed && typeof parsed.$id === 'string' && parsed.$id.trim()) {
                // Sanitize IDs that may have been emitted with surrounding quotes
                // e.g. quotes around schema URL
                let cleaned = parsed.$id.trim();
                if ((cleaned.startsWith("'") && cleaned.endsWith("'")) || (cleaned.startsWith('"') && cleaned.endsWith('"'))) {
                    cleaned = cleaned.slice(1, -1);
                }
                refValue = cleaned;
                idToCanonical[refValue] = base + 'Json';
            }
            // Promote this file's $defs to top-level combined $defs so each gets its own exported type
            if (parsed && parsed.$defs && typeof parsed.$defs === 'object') {
                const defNames = Object.keys(parsed.$defs).filter((k) => /^[A-Za-z_][A-Za-z0-9_]*$/.test(k));
                for (const defName of defNames) {
                    // Prefer bare def name; if collision, namespace with file base
                    let entryName = defName;
                    if (definitions[entryName]) {
                        entryName = `${base}_${defName}`;
                    }
                    if (!definitions[entryName]) {
                        definitions[entryName] = { $ref: `${refValue}#/$defs/${defName}` } as any;
                        includedNames.push(entryName);
                    }
                }
            }
        } catch (e) {
            // If parsing fails, fall back to relative ref; proceed gracefully
        }
        definitions[base] = { $ref: refValue };
        includedNames.push(base);
    }

    if (includedNames.length === 0) {
        console.warn('No schema files found to compile. Nothing to do.');
        return;
    }

    // Instead of building a combined schema (which can trip json-schema-to-typescript traversals),
    // compile each discovered schema file individually and concatenate the outputs.
    try {
        let ts = '';
        for (const fileName of toCompile) {
            const schemaPath = path.join(inputDir, fileName);
            if (!fs.existsSync(schemaPath)) continue;
            // Load and defensively normalize array-expected keywords to avoid generator crashes
            let toCompilePath = schemaPath;
            try {
                const rawSchema = fs.readFileSync(schemaPath, 'utf8');
                const parsedSchema: any = JSON.parse(rawSchema);
                function normalizeArrays(node: any) {
                    if (Array.isArray(node)) {
                        for (let i = 0; i < node.length; i++) normalizeArrays(node[i]);
                        return;
                    }
                    if (!node || typeof node !== 'object') return;
                    const arrayKeys = ['anyOf', 'allOf', 'oneOf', 'required', 'enum'];
                    for (const k of arrayKeys) {
                        if (k in node) {
                            const v = node[k];
                            if (!Array.isArray(v)) node[k] = [v];
                        }
                    }
                    for (const val of Object.values(node)) normalizeArrays(val);
                }
                // Normalize expected arrays to prevent traversal crashes
                normalizeArrays(parsedSchema);
                const tmpPath = path.join(inputDir, `.normalized.${path.basename(fileName)}`);
                fs.writeFileSync(tmpPath, JSON.stringify(parsedSchema, null, 2), 'utf8');
                toCompilePath = tmpPath;
            } catch (e) {
                // If normalization fails, fall back to original path
            }
            const part = await compileFromFile(toCompilePath, {
                bannerComment: '',
                // 
                declareExternallyReferenced: true,
                unreachableDefinitions: true,
                // Forward ref parser options so absolute $id/$ref URLs resolve from local files
                $refOptions: {
                    // Don’t go to the network; we provide a local resolver for our domain
                    resolve: {
                        file: { order: 2 },
                        http: false,
                        https: false,
                        toolproof: toolproofResolver
                    }
                } as any
            });
            ts += '\n' + part + '\n';
            // Cleanup temp normalized file
            if (toCompilePath !== schemaPath) {
                try { fs.unlinkSync(toCompilePath); } catch { }
            }
        }

        // Remove permissive index signatures that make interfaces open-ended.
        // Keep meaningful map-like signatures (e.g., `[k: string]: ResourceRoleValue`) intact.
        // Robust single-pass: delete the entire line (with optional trailing newline) where the signature appears.
        // This avoids introducing extra blank lines while handling CRLF/LF and varying indentation.
        ts = ts.replace(/^\s*\[k:\s*string\]:\s*unknown;\s*(?:\r?\n)?/gm, '');

        // Fix meta-schema types where json-schema-to-typescript incorrectly interprets
        // schema definitions as literal values. ExtractionSchemaValue describes what
        // an extraction schema looks like, not what the schema meta-properties should be.
        ts = ts.replace(
            /^(export type ExtractionSchemaValue =[\s\S]*?)(\$defs\?:\s*\{[\s\S]*?};)/gm,
            '$1$defs?: {\n    [k: string]: unknown;\n  };'
        );
        ts = ts.replace(
            /^(export type ExtractionSchemaValue =[\s\S]*?)(properties\?:\s*\{[\s\S]*?};)/gm,
            '$1properties?: {\n    [k: string]: unknown;\n  };'
        );
        ts = ts.replace(
            /^(export type ExtractionSchemaValue =[\s\S]*?)(allOf\?:\s*\[\{type:\s*"array";\s*items:\s*\{type:\s*"object"\}\}\];)/gm,
            '$1allOf?: Array<{[k: string]: unknown}>;'
        );
        ts = ts.replace(
            /^(export type ExtractionSchemaValue =[\s\S]*?)(required\?:\s*\[\{type:\s*"array";\s*items:\s*\{type:\s*"string"\};\s*uniqueItems:\s*true\}\];)/gm,
            '$1required?: string[];'
        );

        // Similarly fix IdentityProp which has the same issue
        ts = ts.replace(
            /^(export interface IdentityProp[\s\S]*?)(required:\s*\[\{type:\s*"array";\s*contains:\s*\{const:\s*"identity"\};\s*items:\s*\{type:\s*"string"\};\s*uniqueItems:\s*true\}\];)/gm,
            '$1required?: string[];'
        );

        // Prune verbose type/interface names produced from absolute $id URLs.
        // Deterministic pruning based on original $id -> baseName map
        // This avoids heuristic truncation that dropped prefixes like Resource / Workflow.
        function idToGeneratedIdentifier(id: string): string {
            // json-schema-to-typescript seems to create a PascalCase of the URL with protocol prefix
            // Simplified reconstruction: 'https://' => 'Https', then capitalize path & host segments
            const noProto = id.replace(/^https?:\/\//i, '');
            const tokens = noProto
                .split(/[\/#?.=&_-]+/)
                .filter(Boolean)
                .map((t) => t.replace(/[^A-Za-z0-9]/g, ''))
                .filter(Boolean)
                .map((t) => t.charAt(0).toUpperCase() + t.slice(1));
            return 'Https' + tokens.join('') + 'Json';
        }
        // Perform replacements for known IDs
        for (const [id, canonical] of Object.entries(idToCanonical)) {
            const longName = idToGeneratedIdentifier(id);
            if (longName === canonical) continue; // already minimal
            const re = new RegExp(`\\b${longName}\\b`, 'g');
            ts = ts.replace(re, canonical);
        }
        // Remove version prefixes inside any remaining long identifiers: Https...V0... -> remove V0 if followed by capital
        ts = ts.replace(/(Https[A-Za-z0-9_]*?)V\d+([A-Z])/g, '$1$2');

        // Final cleanup: aggressively strip the domain prefix `HttpsSchemasToolproofCom` from ALL identifiers.
        // This is safe because those long names are only artifacts of json-schema-to-typescript; base names don't start with that sequence.
        ts = ts.replace(/\bHttpsSchemasToolproofCom(?=[A-Z])/g, '');
        // Remove accidental duplicate union entries in any exported union types after shortening.
        ts = ts.replace(/export type ([A-Za-z0-9_]+) =([\s\S]*?);/g, (m, typeName, body) => {
            const lines = body.split(/\n/);
            const seen2 = new Set<string>();
            const kept: string[] = [];
            for (const line of lines) {
                const trimmed = line.trim();
                const match = /^\|\s*([A-Za-z0-9_]+)\b/.exec(trimmed);
                if (match) {
                    const name = match[1];
                    if (!seen2.has(name)) {
                        seen2.add(name);
                        kept.push('  | ' + name);
                    }
                } else if (trimmed.length) {
                    kept.push(line);
                }
            }
            return `export type ${typeName} =\n` + kept.join('\n') + ';';
        });
        // If nothing was emitted, compile Genesis.json as a final fallback.
        if (!ts || !ts.trim()) {
            const primary = path.join(inputDir, 'Genesis.json');
            if (fs.existsSync(primary)) {
                ts = await compileFromFile(primary, {
                    bannerComment: '',
                    declareExternallyReferenced: true,
                    unreachableDefinitions: true,
                    $refOptions: {
                        resolve: {
                            file: { order: 2 },
                            http: false,
                            https: false,
                            toolproof: toolproofResolver
                        }
                    } as any
                });
            }
        }
        // Still nothing? ensure we emit a module so downstream imports don't fail.
        if (!ts || !ts.trim()) {
            ts = '// (No concrete types emitted by generator)\nexport {}\n';
        }

        // Overlay Id aliases with template literal types inferred from Genesis.json patterns.
        // For each $defs entry ending with "Id" or specific key types that provide a `pattern`,
        // we derive a TS template literal. If no recognizable pattern exists,
        // we fall back to plain `string` (moving away from branded types).
        function deriveTemplateFromPattern(pattern: string): string | undefined {

            // Common form: ^PREFIX-.+$ => PREFIX-${string}
            const m1 = /^\^([^$]+)\.\+\$/.exec(pattern);
            if (m1) {
                const prefix = m1[1]; // e.g., 'WORKFLOW-'
                // Basic safety: ensure backticks/interpolations aren't present
                if (!/[`]/.test(prefix)) {
                    return '`' + prefix + '${string}`';
                }
            }
            // Slightly stricter forms: ^PREFIX-[A-Za-z0-9]+$ => PREFIX-${string}
            const m2 = /^\^([A-Za-z0-9._:-]+-)\[?\^?[A-Za-z0-9]+\]?\+?\$/.exec(pattern);
            if (m2) {
                const prefix = m2[1];
                if (!/[`]/.test(prefix)) {
                    return '`' + prefix + '${string}`';
                }
            }
            return undefined;
        }

        function loadIdTemplates(): Record<string, string> {
            const map: Record<string, string> = {};
            try {
                const genesisPath = path.join(inputDir, config.getSourceFile());
                if (fs.existsSync(genesisPath)) {
                    const raw = fs.readFileSync(genesisPath, 'utf8');
                    const parsed = JSON.parse(raw);
                    const defs = parsed?.$defs && typeof parsed.$defs === 'object' ? parsed.$defs : {};
                    for (const [defName, defVal] of Object.entries(defs)) {
                        // Process Id types
                        const isIdType = /Id$/.test(defName);
                        if (!isIdType) continue;

                        const v: any = defVal;
                        if (v && v.type === 'string' && typeof v.pattern === 'string') {
                            const tmpl = deriveTemplateFromPattern(v.pattern);
                            if (tmpl) map[defName] = tmpl;
                        }
                    }
                }
            } catch {
                // ignore failures; we'll just fall back to string
            }
            return map;
        }

        const idTemplates = loadIdTemplates();

        // Replace any exported Id aliases to use the inferred template literals where available.
        // Handle both `= string;` and any pre-existing branded alias `= Branded<string, 'X'>;`.
        ts = ts.replace(/^(export\s+type\s+)([A-Za-z_][A-Za-z0-9_]*Id)(\s*=\s*)Branded<string,\s*'[^']+'>\s*;$/gm, (_m, p1, typeName, p3) => {
            const tmpl = idTemplates[typeName];
            return `${p1}${typeName}${p3}${tmpl ?? 'string'};`;
        });
        ts = ts.replace(/^(export\s+type\s+)([A-Za-z_][A-ZaZ0-9_]*Id)(\s*=\s*)string\s*;$/gm, (_m, p1, typeName, p3) => {
            const tmpl = idTemplates[typeName];
            return `${p1}${typeName}${p3}${tmpl ?? 'string'};`;
        });

        // Post-process map-like interfaces to enforce key constraints via template literal Id types.
        // Replace index-signature interfaces with Record<IdType, ValueType> so object literal keys are checked.
        ts = ts.replace(/export interface RoleMap\s*{[^}]*}/g,
            'export type RoleMap = Record<ResourceRoleId, ResourceRoleValue>;');
        ts = ts.replace(/export interface RoleBindingMap\s*{[^}]*}/g,
            'export type RoleBindingMap = Record<ResourceRoleId, ResourceId>;');
        ts = ts.replace(/export interface StrategyState\s*\{[^}]*\{[^}]*\}[^}]*\}/gs,
            'export type StrategyState = Record<ExecutionId, Record<ResourceRoleId, ResourcePotentialInput | ResourcePotentialOutput | Resource>>;');

        parts.push(ts);

        let output = parts.join('\n');

        // Final guard: strip any lingering `[k: string]: unknown;` that might have been
        // reintroduced by later transforms.
        output = output.replace(/^\s*\[k:\s*string\]:\s*unknown;\s*$/gm, '');

        // Cosmetic post-format: remove lone blank lines before closing braces and collapse excessive blank lines
        // - Remove a single blank line before `};` and `}`
        // - Collapse 3+ consecutive newlines into a maximum of 2
        output = output
            .replace(/\r?\n\s*\r?\n(\s*};)/g, '\n$1')
            .replace(/\r?\n\s*\r?\n(\s*})/g, '\n$1')
            .replace(/(\r?\n){3,}/g, '\n\n');

        // As an additional safeguard, make sure the final .d.ts is treated as a module.
        // If no export/interface/module is present, append an empty export.
        if (!/\bexport\b|\bdeclare\s+module\b|\bdeclare\s+namespace\b/.test(output)) {
            output += '\nexport {}\n';
        }

        // Write only under src/_lib/types to avoid duplicate src/types folder
        try {
            fs.writeFileSync(srcLibOutputPath, output, 'utf8');
            console.log('Wrote', srcLibOutputPath);
        } catch (e) {
            console.warn('Failed to write types to src/_lib:', e);
        }

        // Also write a copy into dist so consumers get the generated declarations
        // Write only to dist/_lib/types to keep the same path structure under dist
        const distLibTypesDir = config.getTypesDistDir();
        const distLibOutputPath = config.getTypesDistPath('types.d.ts');
        try {
            fs.mkdirSync(distLibTypesDir, { recursive: true });
            fs.writeFileSync(distLibOutputPath, output, 'utf8');
            console.log('Wrote', distLibOutputPath);
        } catch (e) {
            // If copying to dist fails, log but don't crash the generator.
            console.warn('Failed to write types to dist:', e);
        }

        // Ensure there is a runtime-resolvable module for './_lib/types/types.js'
        // Some consumers and TS NodeNext resolution expect a concrete .js next to .d.ts
        // The file is intentionally empty as all exports are types-only.
        try {
            const srcLibTypesJsPath = config.getTypesSrcPath('types.js');
            if (!fs.existsSync(srcLibTypesJsPath)) {
                fs.writeFileSync(srcLibTypesJsPath, 'export {}\n', 'utf8');
                console.log('Wrote', srcLibTypesJsPath);
            }
        } catch (e) {
            console.warn('Failed to write types.js to src/_lib:', e);
        }
        try {
            const distLibTypesJsPath = config.getTypesDistPath('types.js');
            fs.writeFileSync(distLibTypesJsPath, 'export {}\n', 'utf8');
            console.log('Wrote', distLibTypesJsPath);
        } catch (e) {
            console.warn('Failed to write types.js to dist/_lib:', e);
        }
    } finally {
        // No temp combined schema used.
    }
}

main().catch((err) => {
    console.error(err);
    process.exitCode = 1;
});