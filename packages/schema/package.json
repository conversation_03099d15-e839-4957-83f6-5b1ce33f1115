{"name": "@toolproof-npm/schema", "version": "0.1.40", "description": "JSON schemas and TypeScript types for ToolProof", "keywords": ["toolproof", "schemas", "json-schema", "typescript"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/_schemas"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc -b", "build:scripts": "tsc -p tsconfig.scripts.json", "rewriteAnchors": "node ./dist/scripts/rewriteAnchors.js", "extractSchemas": "node ./dist/scripts/extractSchemas.js", "extractSubschema": "node ./dist/scripts/extractSubschemaWithDefs.js", "generateTypes": "node ./dist/scripts/generateTypes.js", "generateResourceTypeType": "node ./dist/scripts/generateResourceTypeType.js", "generateResourceEnvelopes": "node ./dist/scripts/generateResourceEnvelopes.js", "generateSchemaShims": "node ./dist/scripts/generateSchemaShims.js", "update": "rimraf /s /q dist && pnpm run build:scripts && pnpm run rewriteAnchors && pnpm run generateResourceEnvelopes && pnpm run extractSchemas && pnpm run extractSubschema -- --name Job && pnpm run extractSubschema -- --name ResourceFormat && pnpm run extractSubschema -- --name ResourceType && pnpm run extractSubschema -- --name StatelessStrategy && pnpm run extractSubschema -- --name StatefulStrategy && pnpm run generateSchemaShims && pnpm run generateTypes && pnpm run generateResourceTypeType -- --name Job && pnpm run generateResourceTypeType -- --name ResourceFormat && pnpm run generateResourceTypeType -- --name ResourceType && pnpm run generateResourceTypeType -- --name StatelessStrategy && pnpm run build"}, "files": ["dist", "README.md"], "devDependencies": {"@apidevtools/json-schema-ref-parser": "^14.2.1", "@types/node": "^20.8.1", "ajv-cli": "^5.0.0", "ajv-formats": "^3.0.1", "json-schema-to-typescript": "^15.0.4", "rimraf": "^5.0.0", "typescript": "^5.0.0"}}