import type { SpaceLoaderRegistry, SpaceSwitchOptions } from './_lib/types.js';
import type { SpaceInterface } from './spaces/_lib/types.js';
import { InteractionLayer } from './interactors/InteractionLayer.js';
import { createCamera } from './prefabs/camera.js';
import { createLights } from './prefabs/lights.js';
import { createScene } from './prefabs/scene.js';
import { createRenderer } from './systems/renderer.js';
import { createControls } from './systems/controls.js';
import { Resizer } from './systems/Resizer.js';
import * as THREE from 'three';


export default class Explorer {
    private spaceMap = new Map<string, SpaceInterface>();
    private SpaceLoaderRegistry: SpaceLoaderRegistry = new Map();
    private activeSpaceKey: string | null = null;
    scene;
    renderer;
    camera;
    private cameraRig = new THREE.Group();
    private clock = new THREE.Clock();
    private interactionLayer?: InteractionLayer;
    // Adapter: listen for generic interactor actions and route/log them here
    private interactorActionListener?: (e: Event) => void;
    private resizeHandler?: () => void;

    constructor(container: HTMLDivElement) {
        console.log('[Explorer] Constructor called');
        this.scene = createScene('skyblue'); // ATTENTION: should use config value
        this.renderer = createRenderer();
        const { ambientLight, mainLight } = createLights();
        this.camera = createCamera(30);
        this.cameraRig.add(this.camera);
        createControls(this.camera, this.renderer.domElement);

        // Ensure we don't stack multiple canvases if Explorer is re-created (e.g., React StrictMode)
        try {
            while (container.firstChild) container.removeChild(container.firstChild);
        } catch { }
        container.append(this.renderer.domElement);
        const resizer = new Resizer(container, this.camera, this.renderer);

        // Add resize handler to adjust camera angle for mobile
        this.resizeHandler = () => {
            this.updateCameraForViewport();
        };
        window.addEventListener('resize', this.resizeHandler);

        this.scene.add(ambientLight, mainLight, this.cameraRig);

        // Install a space-agnostic adapter for interactor actions
        this.interactorActionListener = (e: Event) => {
            try {
                const ce = e as CustomEvent;
                const action = ce.detail?.action;

                // Handle space-switching events
                if (action === 'switch-space' && ce.detail?.targetSpace) {
                    const { targetSpace, keepAlive } = ce.detail;
                    console.log(`[Explorer] Received switch-space event: ${targetSpace}`);
                    this.switchSpace(targetSpace, { keepAlive: keepAlive ?? false });
                    return;
                }

                // For other actions: just log the action payload
                // Future: translate to space/explorer config changes (e.g., toggle role mode)
                // eslint-disable-next-line no-console
                console.log('[Explorer] interactor action:', ce.detail);
            } catch { /* ignore */ }
        };
        try { window.addEventListener('explorer-interactor-action', this.interactorActionListener as EventListener); } catch { }
    }

    loadSpace(loaderRegistry: SpaceLoaderRegistry): void {
        console.log('[Explorer] loadSpace called');

        // Register all spaces without initializing
        this.SpaceLoaderRegistry.clear();
        loaderRegistry.forEach((config, key) => {
            // Config already matches SpaceLoaderRegistry structure
            this.SpaceLoaderRegistry.set(key, config);
        })
    }

    switchSpace(spaceKey: string, options: SpaceSwitchOptions = {}): void {
        console.log(`[Explorer] Switching to space: ${spaceKey}`);

        const loader = this.SpaceLoaderRegistry.get(spaceKey);
        if (!loader) {
            console.error(`[Explorer] Space '${spaceKey}' not found in registry`);
            return;
        }

        // Dispose or hide previous space based on keepAlive setting
        if (this.activeSpaceKey && this.activeSpaceKey !== spaceKey) {
            const previousSpace = this.spaceMap.get(this.activeSpaceKey);
            if (previousSpace) {
                if (options.keepAlive !== false) {
                    // Hide space but keep in memory
                    console.log(`[Explorer] Hiding space: ${this.activeSpaceKey}`);
                    previousSpace.hide?.();
                } else {
                    // Dispose space to free memory
                    console.log(`[Explorer] Disposing space: ${this.activeSpaceKey}`);
                    try {
                        previousSpace.dispose();
                    } catch { /* ignore */ }
                    this.spaceMap.delete(this.activeSpaceKey);
                }
            }
        }

        // Clean up previous interaction layer
        if (this.interactionLayer) {
            try {
                this.interactionLayer.dispose();
                this.interactionLayer = undefined;
            } catch { /* ignore */ }
        }

        // Get or create space instance
        let space = this.spaceMap.get(spaceKey);
        if (!space) {
            console.log(`[Explorer] Creating new space: ${spaceKey}`);
            space = loader.spaceFactory(this.scene, this.renderer);
            if (!space) {
                console.error(`[Explorer] spaceFactory for '${spaceKey}' returned undefined`);
                return;
            }
            this.spaceMap.set(spaceKey, space);

            // Build scene for newly created space
            try {
                space.drawScene();
            } catch (err) {
                console.error(`[Explorer] drawScene failed for space '${spaceKey}':`, err);
            }
        } else {
            console.log(`[Explorer] Reusing existing space: ${spaceKey}`);
            // Show the space (it may have been hidden)
            space.show?.();
        }

        // Bind new InteractionLayer for this space
        this.interactionLayer = new InteractionLayer(space.explorerConfig.interactor);
        this.interactionLayer.bind({
            scene: this.scene,
            renderer: this.renderer,
            camera: this.camera,
            cameraRig: this.cameraRig,
            selector: space.explorerConfig.interactor.selector,
            filter: {
                interactionPredicate: space.explorerConfig.interactor.interactionPredicate,
                recursiveRaycast: space.explorerConfig.interactor.recursiveRaycast,
            },
            displayTargetSelector: space.explorerConfig.interactor.displayTargetSelector,
        });
        space.attachInteractionView(this.interactionLayer);

        // Update active space key
        this.activeSpaceKey = spaceKey;

        // Trigger onSpaceReady callback
        try { loader.onSpaceReady?.(space); } catch { /* ignore */ }
    }

    private updateCameraForViewport(): void {
        const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

        if (isMobile) {
            // Mobile view
            // Position camera at an angle that shows Engine, Jobs, and Resources clearly
            this.camera.position.set(-700, 150, 0);
            this.camera.lookAt(0, 0, 0);
        } else {
            // Desktop view: Default positioning
            this.camera.position.set(-200, 50, 0);
            this.camera.lookAt(0, 0, 0);
        }
    }

    async init() {
        // console.log('[Explorer.init] Starting initialization...');
        // Build scene, but don't let failures prevent the render loop from starting
        try {
            // Position cameraRig (not just camera) so it works in both desktop and XR modes
            // In XR, the camera position is controlled by the headset relative to the rig
            // Moving the rig ensures both modes start at a good viewing distance
            this.cameraRig.position.set(0, 0, 150);

            // Set initial camera angle based on viewport
            this.updateCameraForViewport();

            // console.log('[Explorer.init] Setup complete, calling start()...');
        } catch (err) {
            // Log but proceed to start the loop so background and future updates work
            console.error('[Explorer.init] createScene failed:', err);
        } finally {
            this.start();
            // console.log('[Explorer.init] start() called');
        }
    }

    private start() {
        this.renderer.setAnimationLoop(() => {
            const dt = this.clock.getDelta();

            // Update interaction system (input, raycasting, highlighting)
            this.interactionLayer?.update(dt);

            // Get active space
            const activeSpace = this.activeSpaceKey ? this.spaceMap.get(this.activeSpaceKey) : undefined;
            if (!activeSpace) {
                // Render empty scene if no space is active
                this.renderer.render(this.scene, this.camera);
                return;
            }

            // Update space animations and per-frame logic
            try {
                activeSpace.update?.(dt);
            } catch { /* ignore */ }

            // Notify space of interaction state (selection takes precedence over hover)
            try {
                activeSpace.updateOnSelection?.(this.interactionLayer?.selectedObject ?? null);
            } catch { /* ignore */ }

            try {
                activeSpace.updateOnHover?.();
            } catch (e) {
                console.error('[Explorer] updateOnHover error:', e);
            }

            // Render frame
            this.renderer.render(this.scene, this.camera);
        });
    }

    get space(): SpaceInterface | undefined {
        return this.activeSpaceKey ? this.spaceMap.get(this.activeSpaceKey) : undefined;
    }

    getSpace(spaceKey: string): SpaceInterface | undefined {
        return this.spaceMap.get(spaceKey);
    }

    get activeSpace(): string | null {
        return this.activeSpaceKey;
    }

    get spaces(): ReadonlyMap<string, SpaceInterface> {
        return this.spaceMap;
    }

    dispose(): void {
        try {
            // Stop render loop
            this.renderer.setAnimationLoop(null);
        } catch { }
        try {
            // Dispose interaction layer
            this.interactionLayer?.dispose();
            this.interactionLayer = undefined;
        } catch { }
        try {
            // Remove interactor action adapter
            if (this.interactorActionListener) {
                window.removeEventListener('explorer-interactor-action', this.interactorActionListener as EventListener);
                this.interactorActionListener = undefined;
            }
        } catch { }
        try {
            // Remove resize handler
            if (this.resizeHandler) {
                window.removeEventListener('resize', this.resizeHandler);
                this.resizeHandler = undefined;
            }
        } catch { }
        try {
            // Dispose all space-owned resources
            this.spaceMap.forEach((space) => {
                try {
                    space.dispose();
                } catch { /* ignore */ }
            });
            this.spaceMap.clear();
            this.SpaceLoaderRegistry.clear();
            this.activeSpaceKey = null;
        } catch { }
        try {
            // Detach renderer canvas
            if (this.renderer?.domElement && this.renderer.domElement.parentElement) {
                this.renderer.domElement.parentElement.removeChild(this.renderer.domElement);
            }
        } catch { }
        try {
            // Dispose renderer and scene resources
            this.scene.traverse((obj: THREE.Object3D) => {
                const mesh = obj as THREE.Mesh;
                if (mesh && (mesh as unknown as { isMesh?: boolean }).isMesh) {
                    const geom = mesh.geometry as THREE.BufferGeometry | undefined;
                    const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
                    if (geom) geom.dispose?.();
                    if (Array.isArray(mat)) mat.forEach(m => m.dispose?.()); else mat?.dispose?.();
                }
            });
            this.renderer.dispose?.();
        } catch { }
    }

}