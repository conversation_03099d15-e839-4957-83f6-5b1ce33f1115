'use client';
import type { LoaderMap, SpaceLoaderRegistry, SpaceSwitchOptions } from './_lib/types.js';
import type { SpaceInterface } from './spaces/_lib/types.js';
import Explorer from './Explorer.js';
import * as THREE from 'three';
import { useEffect, useRef, useImperativeHandle, forwardRef } from 'react';


interface ExplorerHostProps {
    loaderMap: LoaderMap;
    initialSpaceKey?: string;
    /** Options for space switching. Default: { keepAlive: true } for hot-swapping */
    switchOptions?: SpaceSwitchOptions;
    children?: React.ReactNode;
}

export interface ExplorerHostRef {
    switchSpace: (spaceKey: string, options?: SpaceSwitchOptions) => void;
    getExplorer: () => Explorer | null;
}

function ExplorerHostComponent(props: ExplorerHostProps, ref: React.Ref<ExplorerHostRef>) {
    const { loaderMap, initialSpaceKey, switchOptions, children } = props;
    const containerRef = useRef<HTMLDivElement>(null);
    const explorerRef = useRef<Explorer | null>(null);
    const currentSpaceKeyRef = useRef<string | null>(null);

    // Expose switchSpace method to parent components
    useImperativeHandle(ref, () => ({
        switchSpace: (spaceKey: string, options?: SpaceSwitchOptions) => {
            if (explorerRef.current) {
                explorerRef.current.switchSpace(spaceKey, options);
                currentSpaceKeyRef.current = spaceKey;
            }
        },
        getExplorer: () => explorerRef.current,
    }));

    // DOC: Create Explorer instance once when container is ready (mount only)
    useEffect(() => {
        if (!containerRef.current || explorerRef.current) return;
        const explorer = new Explorer(containerRef.current);
        explorerRef.current = explorer;
        explorer.init(); // Fire-and-forget; internal start handles render loop

        // Listen for portal-click events from DomInteractor
        const handlePortalClick = (event: Event) => {
            const customEvent = event as CustomEvent<{ targetSpace: string }>;
            const targetSpace = customEvent.detail.targetSpace;
            console.log('[ExplorerHost] Portal clicked, switching to space:', targetSpace);
            if (explorerRef.current) {
                explorerRef.current.switchSpace(targetSpace, switchOptions);
                currentSpaceKeyRef.current = targetSpace;
                
                // Update data for the switched space
                const loader = loaderMap.get(targetSpace);
                if (loader?.dataTransform && loader.props) {
                    const space = explorerRef.current.getSpace(targetSpace);
                    if (space) {
                        const dataUpdate = loader.dataTransform(loader.props);
                        space.updateData(dataUpdate as Partial<unknown>);
                    }
                }
            }
        };

        const canvas = explorer.renderer.domElement;
        canvas.addEventListener('portal-click', handlePortalClick as EventListener);

        return () => {
            canvas.removeEventListener('portal-click', handlePortalClick as EventListener);
            try { explorerRef.current?.dispose(); } finally { explorerRef.current = null; }
        };
    }, [loaderMap, switchOptions]); // Add switchOptions to deps

    // DOC: Register all spaces and switch to initial space
    useEffect(() => {
        if (!explorerRef.current) return;

        // Convert LoaderMap to SpaceLoaderRegistry (extract only what Explorer needs)
        const SpaceLoaderRegistry: SpaceLoaderRegistry = new Map();
        loaderMap.forEach((config, key) => {
            SpaceLoaderRegistry.set(key, {
                spaceFactory: config.spaceFactory as (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => SpaceInterface,
                onSpaceReady: config.onSpaceReady as ((space: SpaceInterface) => void) | undefined,
            });
        });

        // Register all spaces
        explorerRef.current.loadSpace(SpaceLoaderRegistry);

        // Switch to initial space if specified
        if (initialSpaceKey) {
            explorerRef.current.switchSpace(initialSpaceKey, switchOptions);
            currentSpaceKeyRef.current = initialSpaceKey;
            
            // Update data for initial space
            const loader = loaderMap.get(initialSpaceKey);
            if (loader?.dataTransform && loader.props) {
                const space = explorerRef.current.getSpace(initialSpaceKey);
                if (space) {
                    const dataUpdate = loader.dataTransform(loader.props);
                    space.updateData(dataUpdate as Partial<unknown>);
                }
            }
        }
    }, [loaderMap, initialSpaceKey, switchOptions]);

    // DOC: Update active space data when props change
    useEffect(() => {
        if (!explorerRef.current) return;

        const currentSpaceKey = currentSpaceKeyRef.current;
        if (!currentSpaceKey) return;

        const loader = loaderMap.get(currentSpaceKey);
        if (!loader || !loader.dataTransform || !loader.props) return;

        const space = explorerRef.current.getSpace(currentSpaceKey);
        if (!space) return;

        const dataUpdate = loader.dataTransform(loader.props);
        // Type widening: space.updateData accepts Partial<unknown> which is compatible
        space.updateData(dataUpdate as Partial<unknown>);
    }, [loaderMap]);

    const defaultStyle: React.CSSProperties = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'orange',
        touchAction: 'none' // Prevent default touch behaviors for 3D controls
    };

    // Use style from first loader if available (for single-space case)
    const firstLoader = loaderMap.values().next().value;
    const mergedStyle = firstLoader?.style ? { ...defaultStyle, ...firstLoader.style } : defaultStyle;

    return (
        <div style={{ position: 'relative', width: '100vw', height: '100vh', userSelect: 'none', touchAction: 'none' }}>
            <div
                ref={containerRef}
                className={firstLoader?.className}
                style={mergedStyle}
            />
            {children}
        </div>
    );

}

// Export with forwardRef to support ref forwarding
const ExplorerHost = forwardRef(ExplorerHostComponent);

export default ExplorerHost;
