// Barrel exports for the Explorer package
// Core classes
export { default as Explorer } from './Explorer.js';
export { default as ExplorerHost } from './ExplorerHost.jsx';

export { default as BaseSpace } from './spaces/BaseSpace.js';
export * from './spaces/Portal.js';

// Interaction system
export { InteractionLayer } from './interactors/InteractionLayer.js';
export { default as SwitchingInteractor } from './interactors/SwitchingInteractor.js';
export { default as DomInteractor } from './interactors/DomInteractor.js';
export { default as XrInteractor } from './interactors/XrInteractor.js';
export * from './interactors/selectors.js';

// Public types and helpers
export * from './_lib/types.js';
export * from './_lib/config.js';
export * from './_lib/eventHelpers.js';

// Spaces public types (if consumers need them)
export * from './spaces/_lib/types.js';
export * from './spaces/_lib/utils.js';
export * from './spaces/_lib/config.js';

export * from './interactors/_lib/types.js';
export * from './interactors/_lib/config.js';
export * from './interactors/_lib/utils.js';

