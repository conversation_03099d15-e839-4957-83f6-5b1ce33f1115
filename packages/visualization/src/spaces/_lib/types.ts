import type { ExplorerConfig } from '../../_lib/types.js';
import type { InteractionView } from '../../interactors/_lib/types.js';
import * as THREE from 'three';


export interface RingConfig<T extends GeometryConfig> {
    isVisible: boolean;
    ringRadius: number;
    ringGuide: MeshConfig<LineConfig, LineMaterialConfig>;
    mesh: MeshConfig<T>;
}

export interface GeometryConfig {
    kind: 'box' | 'sphere' | 'panel' | 'line';
}

export interface SphereConfig extends GeometryConfig {
    kind: 'sphere';
    radius: number;
    widthSegments: number;
    heightSegments: number;
}

export interface BoxConfig extends GeometryConfig {
    kind: 'box';
    width: number;
    height: number;
    depth: number;
    widthSegments: number;
    heightSegments: number;
    depthSegments: number;
}

export interface PanelConfig extends GeometryConfig {
    kind: 'panel';
    width: number;
    height: number;
}

export interface LineConfig extends GeometryConfig {
    kind: 'line';
    segments: number;
}

export interface MaterialConfig {
    color: THREE.Color;
    metalness: number;
    roughness: number;
    emissive: THREE.Color;
    side: THREE.Side;
    transparent: boolean;
    opacity: number;
    depthWrite: boolean;
    depthTest: boolean;
}

export interface LineMaterialConfig extends MaterialConfig {
    linewidth: number;
}

export interface MeshConfig<T extends GeometryConfig, U = MaterialConfig> {
    isVisible: boolean;
    geometry: T;
    material: U;
}

export type NormalDirection = 'x' | 'y' | 'z';

export type OrientationMode = 'given' | 'radial' | 'tangent';

export type EntityMeshMap = Record<string, THREE.Mesh>;

// Internal type used during ring construction before flattening to EntityMeshMap
export type EntityMeshMapInner = Record<string, { name: string; description: string; mesh: THREE.Mesh }>;

export interface SpaceInterface<SpaceConfig = unknown, SpaceData = unknown> {
    explorerConfig: ExplorerConfig<SpaceConfig>;
    drawScene(): void;
    attachInteractionView(interactionView: InteractionView): void;
    updateData: (payload: Partial<SpaceData>) => void;
    updateOnHover: () => void;
    updateOnSelection?: (selected: THREE.Object3D | null) => void;
    update?: (delta: number) => void; // Optional: for per-frame updates like animations
    // Visibility control for hot-swapping spaces without disposal
    show?: () => void;
    hide?: () => void;
    // Optional: spaces may expose mesh lookup by entity name and id
    getMeshById?: (entity: string, id: string) => THREE.Mesh | undefined;
    dispose(): void;
}

// Data-agnostic dummy visuals shared across spaces
export interface DummyConfig {
    dummiesEnabled: boolean;
    dummyIndicatorPoleHeight: number;
    dummyFlagColor: THREE.Color;
    nonDummyFlagColor: THREE.Color;
}

// Generic augmentation: clone-like dummies to reach totalCount.
export type DummyLinkOptions<T> = {
    // Candidate IDs from the related collection to link to (e.g., dummy formats/types/roles)
    linkIds?: string[];
    // Given the dummy index and candidate linkIds, return a shallow patch to merge into the dummy
    // For example: { resourceFormatId } or { resourceTypeId } or { resources: { inputs: [...], outputs: [...] } }
    getLinkPatch?: (idx: number, linkIds: string[]) => Partial<T>;
};

export interface SpaceConfig {
    dummy: DummyConfig;
}
