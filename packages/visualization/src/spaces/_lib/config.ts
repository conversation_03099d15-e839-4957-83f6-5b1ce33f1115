import type { SpaceConfig } from './types.js';
import * as THREE from 'three';


export const baseSpaceConfig: SpaceConfig = {
    dummy: {
        dummiesEnabled: false,
        dummyIndicatorPoleHeight: 1.0,
        dummyFlagColor: new THREE.Color(0x000000),
        nonDummyFlagColor: new THREE.Color(0xffffff),
    },
};

export function makeSpaceConfig(overrides?: Partial<SpaceConfig>): SpaceConfig {
    return { ...baseSpaceConfig, ...(overrides ?? {}) };
}
