import type { EntityMeshMap, OrientationMode, RingConfig, MeshConfig, SphereConfig, BoxConfig, LineConfig, LineMaterialConfig, DummyLinkOptions } from './types.js';
import * as THREE from 'three';
import { v4 as uuidv4 } from 'uuid';


export const orientationMode: OrientationMode = 'radial';

// Generic augmentation: clone-like dummies to reach totalCount.
export const augmentWithDummies = <T extends { id: string }>(items: T[], totalCount: number, cloneLike?: (example: T, idx: number) => T, linkOptions?: DummyLinkOptions<T>): T[] => {
    const result = [...items];
    const needed = Math.max(totalCount - result.length, 0);
    if (needed === 0) return result;

    const example = items[0];

    // If we have no example at all, synthesize minimal dummies so visuals still render.
    if (!example) {
        for (let i = 0; i < needed; i++) {
            const dummy = { id: uuidv4() } as T;
            (dummy as unknown as Record<string, unknown>)['name'] = 'Dummy';
            (dummy as unknown as Record<string, unknown>)['description'] = '';
            const ids = linkOptions?.linkIds ?? [];
            if (ids.length && linkOptions?.getLinkPatch) {
                const patch = linkOptions.getLinkPatch(i, ids);
                Object.assign(dummy as unknown as Record<string, unknown>, patch as Record<string, unknown>);
            }
            result.push(dummy);
        }
        return result;
    }

    for (let i = 0; i < needed; i++) {
        let dummy: T;
        if (cloneLike) {
            dummy = cloneLike(example, i);
        } else {
            // Best-effort shallow clone preserving required fields; adjust common labels if present
            dummy = { ...(example as unknown as Record<string, unknown>) } as T;
            (dummy as unknown as Record<string, unknown>)['id'] = uuidv4();
            if ('name' in example) (dummy as unknown as Record<string, unknown>)['name'] = 'Dummy';
            if ('description' in example) (dummy as unknown as Record<string, unknown>)['description'] = 'Dummy';
        }

        // Optionally link this dummy to related dummy IDs (formats/types/roles) for realism
        const ids = linkOptions?.linkIds ?? [];
        if (ids.length && linkOptions?.getLinkPatch) {
            const patch = linkOptions.getLinkPatch(i, ids);
            Object.assign(dummy as unknown as Record<string, unknown>, patch as Record<string, unknown>);
        }
        result.push(dummy);
    }
    return result;
};

// Compute an orthonormal basis (u, v, n) for a plane given its normal vector n.
export function computeBasisFromNormal(normalVec: THREE.Vector3): { u: THREE.Vector3; v: THREE.Vector3; n: THREE.Vector3 } {
    const n = normalVec.clone().normalize();
    // Choose a helper up that is not parallel to n
    const up = Math.abs(n.y) > 0.9 ? new THREE.Vector3(1, 0, 0) : new THREE.Vector3(0, 1, 0);
    const u = new THREE.Vector3().crossVectors(up, n);
    if (u.lengthSq() < 1e-8) {
        // Fallback if up was accidentally parallel
        const alt = new THREE.Vector3(0, 0, 1);
        u.crossVectors(alt, n);
    }
    u.normalize();
    const v = new THREE.Vector3().crossVectors(n, u).normalize();
    return { u, v, n };
}

// Compute a basis (u, v) whose plane follows the format ring at the given format center.
// The plane normal equals the tangent of the format ring at that location (up x radial),
// making this orientation insensitive to Y offsets of child rings.
export function computeFormatFollowingBasis(formatCenter: THREE.Vector3) {
    const spaceUp = new THREE.Vector3(0, 1, 0);
    const radial = new THREE.Vector3(formatCenter.x, 0, formatCenter.z);
    if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
    radial.normalize();
    const tangentNormal = new THREE.Vector3().crossVectors(spaceUp, radial);
    if (tangentNormal.lengthSq() < 1e-6) tangentNormal.set(0, 0, -1);
    tangentNormal.normalize();
    const b = computeBasisFromNormal(tangentNormal);
    return { u: b.u, v: b.v, normal: tangentNormal };
}

// Synthesize dummy resources for visualization when real data is sparse.
// - For every dummy type, ensure a small number of resources exist.
// - For every dummy signature, also ensure resources exist for the types referenced by its roles.
// NOTE: Resource/job dummy synthesis helpers removed. Provide your own in space interceptors.

// Synthesize dummy jobs for dummy signatures when missing/sparse.
// Ensures at least a random count per dummy signature.
// NOTE: Job dummy augmentation removed.

// Create dummy instances for each job/specification.
// These are lightweight stand-ins for "job instances" that we visualize on the outer sphere's Equator.
// Returns a map: jobId -> array of instances, each with id and name.
// NOTE: Job instance dummy generation removed.



// Attach a small flag above a mesh to indicate dummy status
export function attachFlag(parent: THREE.Mesh, options?: { height?: number; color?: THREE.ColorRepresentation }) {
    const height = options?.height ?? 0.5;
    const color = options?.color ?? 0xffaa00;

    // Compute vertical half-extent from geometry; prefer boundingBox height over boundingSphere radius
    let base = 0.9;
    const geom = parent.geometry as THREE.BufferGeometry | undefined;
    if (geom) {
        // Try bounding box first (accurate for boxes and works for spheres too)
        geom.computeBoundingBox();
        const box = geom.boundingBox;
        const scaleY = parent.scale?.y ?? 1;
        if (box) {
            const heightY = (box.max.y - box.min.y) * scaleY;
            base = heightY / 2;
        } else {
            // Fallback: bounding sphere radius
            geom.computeBoundingSphere();
            const r = geom.boundingSphere?.radius;
            if (typeof r === 'number' && isFinite(r)) {
                base = r * scaleY;
            }
        }
    }

    const group = new THREE.Group();
    group.name = 'dummyFlag';

    // Thin pole
    const poleRadius = 0.02;
    const poleGeom = new THREE.CylinderGeometry(poleRadius, poleRadius, height, 8);
    const poleMat = new THREE.MeshBasicMaterial({ color: 0x333333 });
    const pole = new THREE.Mesh(poleGeom, poleMat);
    pole.position.set(0, base + height / 2, 0);
    group.add(pole);

    // Simple flag (small plane) sized proportionally to the pole height
    const flagWidth = height * 0.5;
    const flagHeight = height * 0.3;
    const flagGeom = new THREE.PlaneGeometry(flagWidth, flagHeight);
    const flagMat = new THREE.MeshBasicMaterial({ color, side: THREE.DoubleSide });
    const flag = new THREE.Mesh(flagGeom, flagMat);
    flag.position.set(0, base + height - flagHeight / 2, poleRadius + flagWidth / 2);
    flag.rotateY(Math.PI / 2);
    group.add(flag);

    parent.add(group);
}

// Draw a guide ring using an explicit basis (u, v) in the plane
function drawRingGuide(
    parent: THREE.Object3D,
    resourceshapeConst: string,
    center: THREE.Vector3,
    radius: number,
    count: number,
    basis: { u: THREE.Vector3; v: THREE.Vector3 },
    options?: {
        guideConfig?: MeshConfig<LineConfig, LineMaterialConfig>;
        orientationMode?: OrientationMode;
        origin?: THREE.Vector3; // reference point for radial/tangent; default (0,0,0)
        up?: THREE.Vector3; // up vector for tangent; default (0,1,0)
        // Optional explicit ring radius override (useful when geometry kind isn't 'sphere')
        ringRadius?: number;
    }
) {
    const guideVisible = options?.guideConfig?.isVisible ?? true;
    if (!guideVisible) return;

    const segments = options?.guideConfig?.geometry.segments ?? Math.max(64, count * 12);
    const color = options?.guideConfig?.material.color ?? 0x888888;
    const linewidth = options?.guideConfig?.material.linewidth ?? 0;
    // Determine effective basis according to orientation toggle
    let { u, v } = basis;
    const orientationMode = options?.orientationMode ?? 'given';
    if (orientationMode !== 'given') { // ATTENTION: duplication with drawRing
        const origin = options?.origin ?? new THREE.Vector3(0, 0, 0);
        const up = (options?.up ?? new THREE.Vector3(0, 1, 0)).clone().normalize();
        // Radial vector from origin to center, projected onto plane perpendicular to up
        const radial = center.clone().sub(origin);
        // Project out the up component: radial -= up * (radial·up)
        const upComp = up.clone().multiplyScalar(radial.dot(up));
        radial.sub(upComp);
        if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0); // fallback
        radial.normalize();
        let normal: THREE.Vector3;
        if (orientationMode === 'radial') {
            normal = radial;
        } else {
            // tangent-based normal = up x radial
            normal = new THREE.Vector3().crossVectors(up, radial);
            if (normal.lengthSq() < 1e-6) normal.set(0, 0, -1);
            normal.normalize();
        }
        const b = computeBasisFromNormal(normal);
        u = b.u; v = b.v;
    }

    const circlePoints: THREE.Vector3[] = [];
    for (let s = 0; s <= segments; s++) {
        const t = (s / segments) * Math.PI * 2;
        const pt = center.clone()
            .add(u.clone().multiplyScalar(Math.cos(t) * radius))
            .add(v.clone().multiplyScalar(Math.sin(t) * radius));
        circlePoints.push(pt);
    }

    if (linewidth > 0) {
        const curve = new THREE.CatmullRomCurve3(circlePoints, true, 'catmullrom', 0.0);
        const tubularSegments = Math.max(segments * 2, 128);
        const radialSegments = 8;
        const tubeGeom = new THREE.TubeGeometry(curve, tubularSegments, linewidth, radialSegments, true);
        // Non-occluding, faint guide; draw late to be on top visually but without depth side-effects
        const tubeMat = new THREE.MeshBasicMaterial({ color, depthTest: false, depthWrite: false, transparent: true, opacity: 0.35 });
        const tube = new THREE.Mesh(tubeGeom, tubeMat);
        tube.name = `${resourceshapeConst}-ring-guide`;
        tube.renderOrder = 9999;
        parent.add(tube);
    } else {
        const circleGeom = new THREE.BufferGeometry().setFromPoints(circlePoints);
        const circleMat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.5, depthTest: false, depthWrite: false });
        const circleLine = new THREE.LineLoop(circleGeom, circleMat);
        circleLine.name = `${resourceshapeConst}-ring-guide`;
        circleLine.renderOrder = 9999;
        parent.add(circleLine);
    }
}

// Generic ring drawer using an explicit basis (u, v) for the ring plane
export function drawRing<T extends { id: string; name: string; description: string }>(
    parent: THREE.Object3D,
    groupName: string,
    entities: T[],
    center: THREE.Vector3,
    ringConfig: RingConfig<SphereConfig | BoxConfig>,
    basis: { u: THREE.Vector3; v: THREE.Vector3 },
    options?: {
        guideConfig?: MeshConfig<LineConfig, LineMaterialConfig>;
        orientationMode?: OrientationMode;
        origin?: THREE.Vector3; // reference point for radial/tangent; default (0,0,0)
        up?: THREE.Vector3; // up vector for tangent; default (0,1,0)
        semicircleBy?: (entity: T) => 'upper' | 'lower';
        angleBy?: (entity: T, index: number, count: number) => number;
        nameBy?: (entity: T) => string;
        decorateMesh?: (mesh: THREE.Mesh, entity: T) => void;
        targetMap?: EntityMeshMap;
    }
): EntityMeshMap {
    const count = entities.length;
    const out: EntityMeshMap = options?.targetMap ?? {} as EntityMeshMap;
    // Single container for all meshes in this ring for easier cleanup
    const container = new THREE.Group();
    container.name = groupName;
    parent.add(container);
    // Determine effective basis according to orientation toggle
    let { u, v } = basis;
    const orientationMode = options?.orientationMode ?? 'given';
    if (orientationMode !== 'given') {
        const origin = options?.origin ?? new THREE.Vector3(0, 0, 0);
        const up = (options?.up ?? new THREE.Vector3(0, 1, 0)).clone().normalize();
        // Radial vector from origin to center, projected onto plane perpendicular to up
        const radial = center.clone().sub(origin);
        const upComp = up.clone().multiplyScalar(radial.dot(up));
        radial.sub(upComp);
        if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
        radial.normalize();
        let normal: THREE.Vector3;
        if (orientationMode === 'radial') {
            normal = radial;
        } else {
            normal = new THREE.Vector3().crossVectors(up, radial);
            if (normal.lengthSq() < 1e-6) normal.set(0, 0, -1);
            normal.normalize();
        }
        const b = computeBasisFromNormal(normal);
        u = b.u; v = b.v;
    }

    // Determine ring radius from geometry (sphere configs carry ringRadius) or option override
    const ringRadius = ringConfig.ringRadius;

    // Draw the guide ring with the provided basis
    drawRingGuide(parent, groupName, center, ringRadius, count, { u, v }, options);

    // Helper to create a mesh for an entity at a given angle on the ring

    const placeEntityAtAngle = (entity: T, angle: number) => {
        const offset = u.clone().multiplyScalar(Math.cos(angle) * ringRadius)
            .add(v.clone().multiplyScalar(Math.sin(angle) * ringRadius));

        let geometry: THREE.BufferGeometry;
        switch (ringConfig.mesh.geometry.kind) {
            case 'sphere':
                geometry = new THREE.SphereGeometry(
                    ringConfig.mesh.geometry.radius,
                    ringConfig.mesh.geometry.widthSegments,
                    ringConfig.mesh.geometry.heightSegments
                );
                break;
            case 'box':
                geometry = new THREE.BoxGeometry(
                    ringConfig.mesh.geometry.width,
                    ringConfig.mesh.geometry.height,
                    ringConfig.mesh.geometry.depth,
                    ringConfig.mesh.geometry.widthSegments,
                    ringConfig.mesh.geometry.heightSegments,
                    ringConfig.mesh.geometry.depthSegments
                );
                break;
            default:
                throw new Error('Unsupported geometry kind');
        }

        const material = new THREE.MeshStandardMaterial({
            color: ringConfig.mesh.material.color,
            metalness: ringConfig.mesh.material.metalness,
            roughness: ringConfig.mesh.material.roughness,
            emissive: ringConfig.mesh.material.emissive,
        });
        const mesh = new THREE.Mesh(geometry, material.clone());
        mesh.position.copy(center).add(offset);
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        const displayName = options?.nameBy ? options.nameBy(entity) : (entity.name ?? entity.id);
        mesh.userData = { entity: groupName, id: entity.id, name: displayName, description: entity.description ?? '' };
        // Allow caller-specific decoration
        options?.decorateMesh?.(mesh, entity);
        container.add(mesh);
        out[entity.id] = mesh;
    };

    const kind = options?.semicircleBy;
    if (!kind) {
        const angleProvider = options?.angleBy;
        if (angleProvider) {
            for (let i = 0; i < count; i++) {
                const angle = angleProvider(entities[i], i, count);
                placeEntityAtAngle(entities[i], angle);
            }
        } else {
            // Default: distribute on full circle
            for (let i = 0; i < count; i++) {
                const angle = (i / count) * Math.PI * 2;
                placeEntityAtAngle(entities[i], angle);
            }
        }
    } else {
        // Split entities across upper (y>0) and lower (y<0) half-spaces
        const uppers: T[] = [];
        const lowers: T[] = [];
        for (const e of entities) {
            const tag = kind(e);
            if (tag === 'upper') uppers.push(e); else lowers.push(e);
        }

        // Determine phase so that y>0 corresponds to angles in (alpha-π/2, alpha+π/2)
        const uy = u.y; const vy = v.y;
        const mag = Math.hypot(uy, vy);
        let alpha = 0;
        if (mag > 1e-6) {
            alpha = Math.atan2(vy, uy);
        } else {
            // Degenerate: fall back to using v as 'up' reference in-plane
            alpha = 0; // split at 0 and π; will not introduce y separation if plane is horizontal
        }

        const placeHalf = (arr: T[], start: number) => {
            const n = arr.length;
            if (n === 0) return;
            for (let i = 0; i < n; i++) {
                // Center items within the half-arc
                const angle = start + ((i + 0.5) / n) * Math.PI;
                placeEntityAtAngle(arr[i], angle);
            }
        };

        // Upper half: centered around alpha (from alpha-π/2 to alpha+π/2)
        placeHalf(uppers, alpha - Math.PI / 2);
        // Lower half: opposite semicircle
        placeHalf(lowers, alpha + Math.PI / 2);
    }
    return out;
}

// Compute the angle (radians) of the in-plane inward direction from a ring center toward the space origin.
export function computeInnerCenterAngle(center: THREE.Vector3, origin = new THREE.Vector3(0, 0, 0)): number {
    const toOrigin = origin.clone().sub(center);
    const toOriginXZ = new THREE.Vector3(toOrigin.x, 0, toOrigin.z);
    if (toOriginXZ.lengthSq() > 1e-8) {
        toOriginXZ.normalize();
        return Math.atan2(toOriginXZ.z, toOriginXZ.x);
    }
    return 0;
}

// Provide a half-arc angleBy function centered at centerAngle.
export function angleByHalfArc<T = unknown>(centerAngle: number) {
    return (_entity: T, i: number, count: number) => centerAngle - Math.PI / 2 + ((i + 0.5) / count) * Math.PI;
}

// Generic utility: find a mesh in a scene by entity name and id.
// Data-agnostic: works with any entity type (types, formats, resources, jobs, etc.)
export function findMeshById(scene: THREE.Scene, entity: string, id: string): THREE.Mesh | undefined {
    let found: THREE.Mesh | undefined;
    scene.traverse((obj: THREE.Object3D) => {
        if (found) return;
        const m = obj as THREE.Mesh;
        const ud = (m?.userData ?? {}) as { entity?: unknown; id?: unknown };
        if ((m as unknown as { isMesh?: boolean }).isMesh && String(ud.entity ?? '') === entity && String(ud.id ?? '') === id) {
            found = m;
        }
    });
    return found;
}
