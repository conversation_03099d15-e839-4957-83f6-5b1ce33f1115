import type { ExplorerConfig } from '../_lib/types.js';
import type { SpaceInterface } from './_lib/types.js';
import type { InteractionView } from '../interactors/_lib/types.js';
import * as THREE from 'three';

// BaseSpace centralizes host wiring and a dedicated root group for mount/unmount.
// Interaction is provided externally via an InteractionView (from InteractionLayer).
// Subclasses must implement drawScene() for their layout logic.
export default abstract class BaseSpace<Config, Data> implements SpaceInterface<Config> {
  public explorerConfig: ExplorerConfig<Config>;
  protected scene: THREE.Scene;
  protected renderer: THREE.WebGLRenderer;
  protected interactionView: InteractionView | null = null;
  // DOC: Dedicate a root group so spaces can be mounted/unmounted as a unit.
  protected root: THREE.Group = new THREE.Group();

  protected constructor(explorerConfig: ExplorerConfig<Config>, scene: THREE.Scene, renderer: THREE.WebGLRenderer) {
    this.explorerConfig = explorerConfig;
    this.scene = scene;
    this.renderer = renderer;
    this.root.name = 'space-root';
    // Mount immediately (lightweight). Future: Explorer can control mount lifecycle explicitly.
    this.scene.add(this.root);
  }

  // Subclasses provide scene construction
  abstract drawScene(): void;

  // Subclasses provide data update handling
  abstract updateData(payload: Partial<Data>): void;

  updateOnHover(): void {
    // Default: no-op; subclasses can override to respond to hover changes
  }

  attachInteractionView(interactionView: InteractionView) {
    this.interactionView = interactionView;
  }

  show(): void {
    this.root.visible = true;
  }

  hide(): void {
    this.root.visible = false;
  }

  dispose(): void {
    try {
      // Remove and dispose space root children (but leave shared lights / camera alone)
      if (this.root.parent) this.root.parent.remove(this.root);
      this.root.traverse((obj) => {
        const mesh = obj as THREE.Mesh;
        if ((mesh as unknown as { isMesh?: boolean }).isMesh) {
          const geom = mesh.geometry as THREE.BufferGeometry | undefined;
          const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
          if (geom) geom.dispose?.();
          if (Array.isArray(mat)) mat.forEach(m => m.dispose?.()); else mat?.dispose?.();
        }
      });
    } catch { /* ignore */ }
  }

  protected getSelectedObject(): THREE.Object3D | null { return this.interactionView?.selectedObject ?? null; }
  protected gethoveredObject(): THREE.Object3D | null { return this.interactionView?.hoveredObject ?? null; }
  protected setInteractionEntityFilter(entities: Set<string>) { this.interactionView?.setEntityFilter?.(entities); }

  // Subclasses can override to provide an entity filter derived from their data (e.g., EntityMeshMap keys)
  protected getInteractiveEntities(): Set<string> | undefined { return undefined; }

  // Allow descendants / external manager to access the space root
  getRoot(): THREE.Group { return this.root; }
}
