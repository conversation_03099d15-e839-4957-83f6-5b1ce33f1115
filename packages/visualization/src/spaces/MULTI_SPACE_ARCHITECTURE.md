# Multi-Space Explorer Architecture

This document explains the refactored Explorer architecture that supports multiple spaces with hot-swapping and lazy data loading.

## Overview

The Explorer has been refactored from a single-space architecture to support multiple spaces that can be switched dynamically. Key features:

- **Hot-swapping by default**: Spaces kept in memory for instant switching
- **Lazy data loading**: Only fetch data for the active space
- **Unified API**: Single-space usage is just a map with one entry
- **Type-safe**: Full TypeScript support for space configurations and data

## Core Principle

**Single-space is just a special case of multi-space** (a map with one entry). All code uses the same API.

## Architecture Components

### 1. Explorer Class (`Explorer.ts`)

The core Three.js manager now maintains a map of spaces instead of a single space instance:

```typescript
class Explorer {
    private spaceMap = new Map<string, SpaceInterface>();
    private SpaceLoaderRegistry = new Map<string, ExplorerLoader>();
    private activeSpaceKey: string | null = null;
    
    // Register multiple spaces
    loadSpace(loaderRegistry: SpaceLoaderRegistry): void;
    
    // Switch between spaces
    switchSpace(spaceKey: string, options?: SpaceSwitchOptions): void;
    
    // Get specific space
    getSpace(spaceKey: string): SpaceInterface | undefined;
}
```

**Key methods:**
- `loadSpace()`: Accepts a `SpaceLoaderRegistry` map of space configurations
- `switchSpace()`: Activates a registered space (defaults to `keepAlive: true` for hot-swapping)
- `getSpace()`: Retrieve a specific space instance by key

### 2. ExplorerHost Component (`ExplorerHost.tsx`)

React wrapper that always uses multi-space API:

```tsx
<ExplorerHost
  ref={explorerRef}
  LoaderMap={spaceLoaderMap}
  initialSpaceKey="cosmos"
  switchOptions={{ keepAlive: true }} // Default, can be omitted
>
  {activeSpaceKey === 'cosmos' && <CosmosOverlays />}
  {activeSpaceKey === 'workflow' && <WorkflowOverlays />}
</ExplorerHost>
```

**For single-space usage** (just a map with one entry):
```tsx
const LoaderMap = new Map([['cosmos', cosmosLoader]]);

<ExplorerHost
  LoaderMap={LoaderMap}
  initialSpaceKey="cosmos"
>
  <CosmosOverlays />
</ExplorerHost>
```

**Exposed ref API:**
```typescript
interface ExplorerHostRef {
  switchSpace: (spaceKey: string, options?: SpaceSwitchOptions) => void;
  getExplorer: () => Explorer | null;
}
```

### 3. Data Context Pattern

Each space type has its own independent context provider:

```tsx
// CosmosDataProvider provides cosmosSpaceData via useCosmosData() hook
<CosmosDataProvider>
  <WorkflowDataProvider>
    <MultiSpaceExplorerView />
  </WorkflowDataProvider>
</CosmosDataProvider>
```

**Benefits:**
- No provider coupling - each provider is independent
- No nesting complexity - flat structure
- Realtime updates - Firebase hooks maintain subscriptions
- Easy to extend - add new space contexts without refactoring

**Context API:**
```typescript
// Each space has its own hook
const { cosmosSpaceData, loading, error } = useCosmosData();
const { workflowSpaceData, loading, error } = useWorkflowData();
```

### 4. Event System (`_lib/eventHelpers.ts`)

Space switching can be triggered from anywhere:

```typescript
import { dispatchSpaceSwitch } from '@/explorer/_lib/eventHelpers';

// In a space interaction handler, UI button, etc.
dispatchSpaceSwitch('workflow-builder', { keepAlive: true });
```

The Explorer's `interactorActionListener` handles these events automatically.

## Usage Patterns

### Single Space

Create a map with one entry:

```tsx
// CosmosView.tsx
const LoaderMap = useMemo(() => {
  const map: LoaderMap = new Map();
  const config = createCosmosLoader({
    cosmosSpaceData,
    onSpaceReady: (space) => { spaceRef.current = space; },
    disableVRButton
  });
  map.set('cosmos', config as Loader<SpaceInterface, unknown, unknown, unknown>);
  return map;
}, [cosmosSpaceData, disableVRButton]);

<ExplorerHost LoaderMap={LoaderMap} initialSpaceKey="cosmos">
  <CosmosOverlays />
</ExplorerHost>
```

### Multiple Spaces

#### Step 1: Create Space Data Providers

Each space type should have its own context provider with a custom hook:

```tsx
// CosmosDataProvider.tsx
export function useCosmosData(): CosmosDataContextValue {
  const context = useContext(CosmosDataContext);
  if (!context) throw new Error('useCosmosData must be used within CosmosDataProvider');
  return context;
}

export default function CosmosDataProvider({ children }: { children: ReactNode }) {
  // Firebase hooks maintain realtime subscriptions
  const { items: resourceFormatMetaMap } = useResourceShapesMeta(...);
  const { items: resourceTypeMetaMap } = useResourceShapesMeta(...);
  
  const cosmosSpaceData = useMemo(() => ({ 
    resourceFormatMetaMap, resourceTypeMetaMap, resourceMap, workflowSpec: null 
  }), [resourceFormatMetaMap, resourceTypeMetaMap, resourceMap]);
  
  const value = useMemo(() => ({ 
    cosmosSpaceData, loading, error 
  }), [cosmosSpaceData, loading, error]);
  
  return <CosmosDataContext.Provider value={value}>{children}</CosmosDataContext.Provider>;
}
```

Repeat for other space types (WorkflowDataProvider, etc.)

#### Step 2: Combine Spaces in Component

```tsx
function MultiSpaceExplorerView() {
  const explorerRef = useRef<ExplorerHostRef>(null);
  const [activeSpaceKey, setActiveSpaceKey] = useState('cosmos');
  
  // Pull data from independent context providers
  const { cosmosSpaceData } = useCosmosData();
  const { workflowSpaceData } = useWorkflowData();
  
  // Build loader config map from all available spaces
  const LoaderMap = useMemo(() => {
    const map = new Map();
    map.set('cosmos', createCosmosLoader({ cosmosSpaceData, ... }));
    map.set('workflow', createWorkflowLoader({ workflowSpaceData, ... }));
    return map;
  }, [cosmosSpaceData, workflowSpaceData]);
  
  const handleSpaceSwitch = (spaceKey: string) => {
    explorerRef.current?.switchSpace(spaceKey, { keepAlive: true });
    setActiveSpaceKey(spaceKey);
  };
  
  return (
    <ExplorerHost
      ref={explorerRef}
      LoaderMap={LoaderMap}
      initialSpaceKey="cosmos"
      switchOptions={{ keepAlive: true }}
    >
      {activeSpaceKey === 'cosmos' && <CosmosOverlays />}
      {activeSpaceKey === 'workflow' && <WorkflowOverlays />}
    </ExplorerHost>
  );
}
```

#### Step 3: Wrap with Providers

```tsx
// At page/app level
<CosmosDataProvider>
  <WorkflowDataProvider>
    <MultiSpaceExplorerView />
  </WorkflowDataProvider>
</CosmosDataProvider>
```

## Performance Considerations

### Memory Management

**Default: Keep spaces in memory (hot-swapping optimized)**
```tsx
// switchOptions can be omitted - keepAlive: true is default
<ExplorerHost LoaderMap={LoaderMap} initialSpaceKey="cosmos">
```

Spaces are kept in memory by default for instant switching. To explicitly dispose inactive spaces:
```tsx
switchOptions={{ keepAlive: false }}
```

**Trade-offs:**
- `keepAlive: true` (default): Instant switching, higher memory usage
- `keepAlive: false`: Lower memory, rebuilding scene takes time

### Data Loading

Context providers maintain their subscriptions independently:

```typescript
// Both contexts are active and receiving realtime updates
const { cosmosSpaceData } = useCosmosData(); // Firebase subscription active
const { workflowSpaceData } = useWorkflowData(); // Firebase subscription active

// All data is always current - no lazy loading needed
```

**Benefits:**
- Data is always up-to-date for instant space switching
- Realtime updates work across all spaces simultaneously
- No loading states when switching spaces

## Type Safety

The architecture preserves full type safety:

```typescript
// Space-specific types are preserved
const cosmosData = getSpaceData<CosmosSpaceData>('cosmos');
const workflowData = getSpaceData<WorkflowSpaceData>('workflow');

// Loader configs maintain their generic types
type CosmosLoader = Loader<CosmosSpace, CosmosConfig, CosmosSpaceData, CosmosLoaderProps>;
type WorkflowLoader = Loader<WorkflowSpace, WorkflowConfig, WorkflowSpaceData, WorkflowLoaderProps>;
```

## Examples

See:
- `examples/MultiSpaceExplorerView.tsx` - Full multi-space example with context pattern
- `spaces/cosmos/CosmosView.tsx` - Single-space pattern (map with one entry)
- `spaces/cosmos/contexts/CosmosDataProvider.tsx` - Example context provider implementation
- `builders/BuildersEntry.tsx` - Using useCosmosData() hook
- `builders/workflow/WorkflowBuilder.tsx` - Single-space usage in workflow builder

## Event Reference

### Switch Space Event

Dispatch from anywhere to trigger space switching:

```typescript
window.dispatchEvent(new CustomEvent('explorer-interactor-action', {
  detail: {
    action: 'switch-space',
    targetSpace: 'workflow',
    keepAlive: true,
  },
}));
```

Or use the helper:
```typescript
import { dispatchSpaceSwitch } from '@/explorer/_lib/eventHelpers';
dispatchSpaceSwitch('workflow', { keepAlive: true });
```

### Custom Interactor Actions

```typescript
import { dispatchInteractorAction } from '@/explorer/_lib/eventHelpers';
dispatchInteractorAction('custom-action', { data: 'value' });
```

## Future Enhancements

Potential improvements to consider:

1. **Conditional provider loading**: Only mount providers for spaces that will be used
2. **Transition animations**: Smooth fade/slide between spaces
3. **Shared resources**: Cache common assets (textures, geometries) across spaces
4. **State persistence**: Save/restore space state when switching
5. **URL-based routing**: Sync active space with browser URL
6. **Subscription management**: Pause/resume Firebase subscriptions for inactive spaces to reduce bandwidth

