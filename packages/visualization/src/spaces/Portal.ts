import * as THREE from 'three';

export interface PortalConfig {
	/** Radius of the portal ring */
	radius: number;
	/** Target space key this portal leads to */
	targetSpace: string;
	/** Color of the portal effect */
	color: THREE.ColorRepresentation;
	/** Portal position in space */
	position: THREE.Vector3;
	/** Portal rotation (Euler angles) */
	rotation?: THREE.Euler;
	/** Glow intensity */
	glowIntensity?: number;
}

/**
 * Creates a portal mesh that serves as a gateway between spaces.
 * Portals are interactive objects that trigger space swapping on click (DOM) or walk-through (XR).
 * 
 * @param config - Portal configuration
 * @returns Portal mesh with appropriate userData for interaction
 */
export function createPortalMesh(config: PortalConfig): THREE.Group {
	const {
		radius,
		targetSpace,
		color,
		position,
		rotation,
		glowIntensity = 1.5,
	} = config;

	const portalGroup = new THREE.Group();
	portalGroup.position.copy(position);
	if (rotation) {
		portalGroup.rotation.copy(rotation);
	}
	portalGroup.name = `portal-to-${targetSpace}`;
	portalGroup.userData = {
		type: 'space-portal',
		targetSpace,
		portalRadius: radius,
	};

	// 1. Outer glowing ring
	const ringGeometry = new THREE.TorusGeometry(radius, radius * 0.08, 16, 64);
	const ringMaterial = new THREE.MeshStandardMaterial({
		color,
		emissive: color,
		emissiveIntensity: glowIntensity,
		metalness: 0.8,
		roughness: 0.2,
	});
	const ring = new THREE.Mesh(ringGeometry, ringMaterial);
	ring.userData = { type: 'space-portal', targetSpace };
	portalGroup.add(ring);

	// 2. Semi-transparent portal surface
	const surfaceGeometry = new THREE.CircleGeometry(radius * 0.95, 64);
	const surfaceMaterial = new THREE.MeshStandardMaterial({
		color,
		transparent: true,
		opacity: 0.3,
		side: THREE.DoubleSide,
		emissive: color,
		emissiveIntensity: 0.5,
	});
	const surface = new THREE.Mesh(surfaceGeometry, surfaceMaterial);
	surface.userData = { type: 'space-portal', targetSpace };
	portalGroup.add(surface);

	// 3. Particle effect ring (using points)
	const particleCount = 100;
	const particleGeometry = new THREE.BufferGeometry();
	const positions = new Float32Array(particleCount * 3);
	const sizes = new Float32Array(particleCount);

	for (let i = 0; i < particleCount; i++) {
		const angle = (i / particleCount) * Math.PI * 2;
		const radiusVariation = radius + (Math.random() - 0.5) * radius * 0.1;
		positions[i * 3] = Math.cos(angle) * radiusVariation;
		positions[i * 3 + 1] = Math.sin(angle) * radiusVariation;
		positions[i * 3 + 2] = (Math.random() - 0.5) * radius * 0.1;
		sizes[i] = radius * 0.02 + Math.random() * radius * 0.01;
	}

	particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
	particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

	const particleMaterial = new THREE.PointsMaterial({
		color,
		size: radius * 0.03,
		transparent: true,
		opacity: 0.8,
		sizeAttenuation: true,
		blending: THREE.AdditiveBlending,
	});

	const particles = new THREE.Points(particleGeometry, particleMaterial);
	particles.userData = { type: 'space-portal', targetSpace, isParticles: true };
	portalGroup.add(particles);

	// Store animation data
	portalGroup.userData.animate = (deltaTime: number) => {
		// Rotate ring slowly
		ring.rotation.z += deltaTime * 0.3;

		// Pulse surface opacity
		const time = Date.now() * 0.001;
		surfaceMaterial.opacity = 0.2 + Math.sin(time * 2) * 0.15;

		// Rotate particles in opposite direction
		particles.rotation.z -= deltaTime * 0.5;
	};

	return portalGroup;
}

/**
 * Get the forward normal vector of a portal (direction it faces)
 */
export function getPortalNormal(portal: THREE.Group): THREE.Vector3 {
	const normal = new THREE.Vector3(0, 0, 1);
	normal.applyQuaternion(portal.quaternion);
	return normal;
}

/**
 * Check if a position is in front of a portal (approaching from correct side)
 */
export function isInFrontOfPortal(
	position: THREE.Vector3,
	portal: THREE.Group
): boolean {
	const toPosition = position.clone().sub(portal.position);
	const normal = getPortalNormal(portal);
	return toPosition.dot(normal) > 0;
}

/**
 * Calculate if camera has crossed through portal threshold
 */
export function hasPortalBeenCrossed(
	prevPosition: THREE.Vector3,
	currentPosition: THREE.Vector3,
	portal: THREE.Group,
	threshold: number
): boolean {
	const portalPos = portal.position;
	const portalRadius = (portal.userData.portalRadius as number) || 1;

	// Check if within portal radius horizontally
	const currentDist2D = new THREE.Vector2(
		currentPosition.x - portalPos.x,
		currentPosition.z - portalPos.z
	).length();

	if (currentDist2D > portalRadius) return false;

	// Check if crossed the portal plane
	const normal = getPortalNormal(portal);
	const prevDot = prevPosition.clone().sub(portalPos).dot(normal);
	const currDot = currentPosition.clone().sub(portalPos).dot(normal);

	// Crossed if signs differ and we're moving in the correct direction (negative dot = moving through)
	return prevDot > threshold && currDot <= threshold && currDot < 0;
}
