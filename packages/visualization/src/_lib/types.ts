import type { InteractorConfig } from '../interactors/_lib/types.js';
import type { SpaceInterface } from '../spaces/_lib/types.js';
import type * as THREE from 'three';


// Unified top-level config object combining interactor + space-specific configuration.
// SpaceConfig is the space-specific config type (e.g., CosmosConfig). Additional loader/spaces can
// extend SpaceConfig with their own properties. This allows passing a single object through the space
// construction chain and keeps extension ergonomic.
export interface ExplorerConfig<SpaceConfig> {
  interactor: InteractorConfig;
  space: SpaceConfig;
}

/**
 * Type-safe loader for creating and managing Explorer spaces.
 * 
 * @template TSpace - The specific space class type (must extend SpaceInterface)
 * @template TConfig - The space-specific configuration type
 * @template TData - The space-specific data type
 * @template TProps - Additional props passed to the loader
 */
export interface Loader<
  TSpace extends SpaceInterface<TConfig, TData>,
  TConfig = unknown,
  TData = unknown,
  TProps = Record<string, never>
> {
  /**
   * Factory function that creates the space instance.
   * Must return an instance of TSpace that implements SpaceInterface<TConfig, TData>.
   */
  spaceFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => TSpace;

  /**
   * Optional callback invoked when the space is ready (after instantiation).
   * Useful for capturing a reference to the space instance for imperative updates.
   */
  onSpaceReady?: (space: TSpace) => void;

  /**
   * Optional function to transform props into data updates for the space.
   * Called when props change to update the space via space.updateData().
   */
  dataTransform?: (props: TProps) => Partial<TData>;

  /**
   * Optional props passed to the loader (e.g., cosmosSpaceData, workflowSpec).
   * These are separate from the loader config to allow type-safe prop passing.
   */
  props?: TProps;

  /**
   * Optional class name for the ExplorerHost container div.
   */
  className?: string;

  /**
   * Optional inline styles for the ExplorerHost container div.
   */
  style?: React.CSSProperties;
}

/**
 * Map of loaders for multi-space support.
 * Each key is a space name, and the value contains its loader and props.
 * Uses a flexible type to support heterogeneous space types.
 */
export type LoaderMap = Map<string, Loader<SpaceInterface, unknown, unknown, unknown>>;

/**
 * Narrowed loader containing only what Explorer.loadSpace actually needs.
 * This avoids coupling Explorer to props/dataTransform which are only used in ExplorerHost.
 */
export interface ExplorerLoader<
  TSpace extends SpaceInterface<TConfig, TData>,
  TConfig = unknown,
  TData = unknown
> {
  spaceFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => TSpace;
  onSpaceReady?: (space: TSpace) => void;
}

/**
 * Registry of space loader configurations, keyed by space name.
 * Each entry defines how to create and initialize a specific space.
 * Uses a flexible type to support heterogeneous space types in the same registry.
 * The onSpaceReady callback is made optional and flexible to support any space type.
 */
export type SpaceLoaderRegistry = Map<string, {
  spaceFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => SpaceInterface;
  onSpaceReady?: (space: SpaceInterface) => void;
}>;

/**
 * Registry of space data payloads, keyed by space name.
 * Each entry contains the data needed to update a specific space.
 * 
 * @template TData - Union type of all possible space data types
 */
export type SpaceDataRegistry<TData = unknown> = Map<string, TData>;

/**
 * Options for controlling space lifecycle during switching.
 */
export interface SpaceSwitchOptions {
  /**
   * If true, keep the previous space in memory (hidden) instead of disposing it.
   * Useful for fast switching between frequently-accessed spaces.
   * Default: true (optimized for hot-swapping)
   */
  keepAlive?: boolean;
}