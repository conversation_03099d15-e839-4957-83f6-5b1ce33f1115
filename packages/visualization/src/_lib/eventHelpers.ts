/**
 * Utility functions for dispatching Explorer events.
 * These events are handled by the Explorer instance's interactorActionListener.
 */

/**
 * Dispatch a space-switch event to the Explorer instance.
 * The Explorer will handle the event and switch to the specified space.
 * 
 * @param targetSpace - The key of the space to switch to
 * @param keepAlive - If true, keep the current space in memory (hidden); if false, dispose it
 * 
 * @example
 * ```typescript
 * // In a space interaction handler or UI component
 * dispatchSpaceSwitch('workflow-builder', { keepAlive: true });
 * ```
 */
export function dispatchSpaceSwitch(targetSpace: string, options?: { keepAlive?: boolean }): void {
    const event = new CustomEvent('explorer-interactor-action', {
        detail: {
            action: 'switch-space',
            targetSpace,
            keepAlive: options?.keepAlive ?? false,
        },
    });
    
    try {
        window.dispatchEvent(event);
    } catch (err) {
        console.error('[dispatchSpaceSwitch] Failed to dispatch event:', err);
    }
}

/**
 * Dispatch a generic interactor action event.
 * Can be used for custom actions beyond space switching.
 * 
 * @param action - The action type/name
 * @param payload - Additional data to include in the event
 * 
 * @example
 * ```typescript
 * dispatchInteractorAction('toggle-role-mode', { enabled: true });
 * ```
 */
export function dispatchInteractorAction(action: string, payload?: Record<string, unknown>): void {
    const event = new CustomEvent('explorer-interactor-action', {
        detail: {
            action,
            ...payload,
        },
    });
    
    try {
        window.dispatchEvent(event);
    } catch (err) {
        console.error('[dispatchInteractorAction] Failed to dispatch event:', err);
    }
}
