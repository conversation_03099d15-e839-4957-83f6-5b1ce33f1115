import type { Interactor, InteractionContext, XRManagerLike, Selector } from './_lib/types.js';
import DomInteractor from './DomInteractor.js';
import XrInteractor from './XrInteractor.js';
import * as THREE from 'three';
import { VRButton } from 'three/examples/jsm/webxr/VRButton.js';


// A thin delegating interactor that switches between DOM and XR jobs
// based on WebXR session lifecycle, keeping Explorer unaware of mode specifics.
export default class SwitchingInteractor implements Interactor {
    private scene: THREE.Scene;
    private renderer: THREE.WebGLRenderer;
    private camera: THREE.Camera;
    private cameraRig: THREE.Group;
    private selector: Selector;
    private domInteractor: DomInteractor;
    private xrInteractor: XrInteractor | null = null;
    private onSessionStart?: () => void;
    private onSessionEnd?: () => void;
    private xrManager?: XRManagerLike;
    private vrButtonElement: HTMLElement | null = null;
    private weCreatedVrButton = false;
    private pendingEntityFilter: Set<string> | undefined;

    constructor(ctx: InteractionContext, options?: { disableVRButton?: boolean }) {
        this.scene = ctx.scene;
        this.renderer = ctx.renderer;
        this.camera = ctx.camera;
        this.cameraRig = ctx.cameraRig;
        this.selector = ctx.selector;

        // Start in non-XR mode
        this.domInteractor = new DomInteractor(ctx);

        // Ensure WebXR is enabled and present an entry button before any session exists
        // Skip VR button creation if disabled (e.g., for carousel mode)
        if (!options?.disableVRButton) {
            try {
                // Enable XR on the renderer (safe to do once)
                (this.renderer.xr as unknown as { enabled: boolean }).enabled = true;
                // Prefer adopting an existing VRButton to avoid duplicate offer sessions (e.g., HMR or multiple explorers)
                if (typeof document !== 'undefined') {
                    const existing = document.getElementById('VRButton') as HTMLElement | null;
                    if (existing) {
                        this.vrButtonElement = existing;
                    } else {
                        const btn = VRButton.createButton(this.renderer);
                        document.body.appendChild(btn);
                        this.vrButtonElement = btn;
                        this.weCreatedVrButton = true;
                    }
                }
            } catch { /* ignore if not supported or SSR */ }
        } else {
            // Still enable XR on renderer even if button is disabled
            try {
                (this.renderer.xr as unknown as { enabled: boolean }).enabled = true;
            } catch { /* ignore if not supported or SSR */ }
        }

        // Listen to XR session lifecycle to lazily switch
        const xrManager = this.renderer.xr as unknown as XRManagerLike;
        this.xrManager = xrManager;

        this.onSessionStart = () => {
            // Initialize XR interactor when entering XR
            if (!this.xrInteractor) {
                this.xrInteractor = new XrInteractor(ctx);
                // Apply any pending entity filter from renderer
                const xr = this.xrInteractor as unknown as { setEntityFilter?: (s: Set<string>) => void };
                if (this.pendingEntityFilter && typeof xr.setEntityFilter === 'function') {
                    try { xr.setEntityFilter(this.pendingEntityFilter); } catch { }
                }
            }
            // No-op: rely on VRButton internal throttling; no custom offer gating
        };
        this.onSessionEnd = () => {
            // Dispose XR-specific UI when exiting XR
            if (this.xrInteractor) {
                this.xrInteractor.dispose();
                // Explicitly clear reference so current() can lazily re-create on next session
                this.xrInteractor = null;
            }
            // No custom gating state to reset
        };

        try {
            xrManager.addEventListener('sessionstart', this.onSessionStart!);
            xrManager.addEventListener('sessionend', this.onSessionEnd!);
        } catch { /* ignore if not supported */ }
    }

    private current(): Interactor {
        // Use XR interactor when a session is active
        const hasSession = !!this.renderer.xr.getSession();
        if (hasSession) {
            if (!this.xrInteractor) {
                // Minimal context for XR path: no entities/interactionPredicate unless provided earlier.
                this.xrInteractor = new XrInteractor({ scene: this.scene, camera: this.camera, renderer: this.renderer, cameraRig: this.cameraRig, selector: this.selector });
                const xr = this.xrInteractor as unknown as { setEntityFilter?: (s: Set<string>) => void };
                if (this.pendingEntityFilter && typeof xr.setEntityFilter === 'function') {
                    try { xr.setEntityFilter(this.pendingEntityFilter); } catch { }
                }
            }
            return this.xrInteractor;
        }
        return this.domInteractor;
    }

    // Expose current interactor state through readonly accessors
    get hoveredObject(): THREE.Object3D | null { return this.current().hoveredObject; }
    get selectedObject(): THREE.Object3D | null { return this.current().selectedObject; }

    updateInteraction(): void {
        this.current().updateInteraction();
    }

    updateMovement(delta: number): void {
        this.current().updateMovement(delta);
    }

    raycastFromController(): THREE.Object3D | null {
        return this.current().raycastFromController();
    }

    dispose(): void {
        if (this.onSessionStart && this.xrManager) {
            try { this.xrManager.removeEventListener('sessionstart', this.onSessionStart); } catch { }
        }
        if (this.onSessionEnd && this.xrManager) {
            try { this.xrManager.removeEventListener('sessionend', this.onSessionEnd); } catch { }
        }
        try {
            // Remove VR button if present (whether we created or adopted it)
            if (this.vrButtonElement && this.vrButtonElement.parentElement) {
                this.vrButtonElement.parentElement.removeChild(this.vrButtonElement);
            }
            this.vrButtonElement = null;
        } catch { }
        this.domInteractor.dispose();
        if (this.xrInteractor) {
            this.xrInteractor.dispose();
            this.xrInteractor = null;
        }
    }

    // Allow renderers to scope interactive groups dynamically.
    setEntityFilter = (entities: Set<string>) => {
        this.pendingEntityFilter = entities;
        const dom = this.domInteractor as unknown as { setEntityFilter?: (s: Set<string>) => void };
        if (typeof dom.setEntityFilter === 'function') {
            try { dom.setEntityFilter(entities); } catch { }
        }
        const xr = this.xrInteractor as unknown as { setEntityFilter?: (s: Set<string>) => void };
        if (this.xrInteractor && typeof xr.setEntityFilter === 'function') {
            try { xr.setEntityFilter(entities); } catch { }
        }
    };

    // Clear selection on the current interactor (used when animation starts)
    clearSelection(): void {
        const cur = this.current() as unknown as { clearSelection?: () => void };
        if (typeof cur.clearSelection === 'function') {
            try { cur.clearSelection(); } catch { }
        }
    }

    // Clear hover and selection state on the current interactor
    clearInteraction(): void {
        const cur = this.current() as unknown as { clearInteraction?: () => void };
        if (typeof cur.clearInteraction === 'function') {
            try { cur.clearInteraction(); } catch { }
        } else {
            this.clearSelection();
        }
    }
}
