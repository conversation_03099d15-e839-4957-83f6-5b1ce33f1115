import type { InteractorConfig } from './types.js';


// Base (static) Explorer config. Dynamic React-provided deps (interactionPredicate, selector, interactorFactory)
// are injected by the factory below inside the component file.
export const baseInteractorConfig: Omit<InteractorConfig, 'interactionPredicate' | 'selector' | 'interactorFactory'> = {
        speedMultiplier: 5,
        skyColor: 'skyblue',
        laserColor: 'yellow',
        isGrabbable: false,
        recursiveRaycast: true,
};

export type InteractorDynamicDeps = Pick<InteractorConfig, 'interactionPredicate' | 'selector' | 'interactorFactory'>;

// Factory to compose final InteractorConfig in React-land while keeping this module React-free.
export function makeInteractorConfig(
    deps: InteractorDynamicDeps,
    overrides?: Partial<Omit<InteractorConfig, keyof InteractorDynamicDeps>>
): InteractorConfig {
    return { ...baseInteractorConfig, ...(overrides ?? {}), ...deps } as InteractorConfig;
}
