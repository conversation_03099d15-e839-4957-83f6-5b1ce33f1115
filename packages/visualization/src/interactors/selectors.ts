import type { Selector, SelectionCommand } from './_lib/types.js';
import * as THREE from 'three';


export class TransientSelector implements Selector {
    onSelectStart(hoveredObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: hoveredObject, restoreOriginalPosition: true };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: null };
    }
}

export class PersistentSelector implements Selector {
    private lastSelectedObject: THREE.Object3D | null = null;

    clear() {
        this.lastSelectedObject = null;
    }

    onSelectStart(hoveredObject: THREE.Object3D | null): SelectionCommand {
        // console.log('[PersistentSelector.onSelectStart] hoveredObject:', hoveredObject?.name, 'lastSelectedObject:', this.lastSelectedObject?.name, 'same?:', hoveredObject === this.lastSelectedObject);
        // Deselect only if clicking empty space
        if (!hoveredObject) {
            // console.log('[PersistentSelector.onSelectStart] Deselecting - clicked empty space');
            this.lastSelectedObject = null;
            return { selectedObject: null };
        }

        // Keep selection even if clicking the same object (no toggle)
        // console.log('[PersistentSelector.onSelectStart] Selecting object:', hoveredObject.name);
        this.lastSelectedObject = hoveredObject;
        return { selectedObject: hoveredObject };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: currentObject }; // no change
    }
}