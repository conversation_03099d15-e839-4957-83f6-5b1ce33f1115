{"compilerOptions": {"target": "ES2020", "module": "nodenext", "baseUrl": ".", "resolveJsonModule": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "skipLibCheck": true, "outDir": "dist", "rootDir": "src", "declaration": true, "jsx": "preserve", "paths": {"@/*": ["./src/*"]}}, "types": ["node"], "include": ["src/**/*"], "exclude": ["node_modules"]}