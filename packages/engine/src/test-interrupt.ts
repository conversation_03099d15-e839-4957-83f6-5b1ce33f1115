import { NodeInterruptCheck } from './nodes/NodeInterruptCheck.js';
import { GraphState } from './types.js';

// Mock the statefulStrategyMock data from StrategyBuilder
const mockState: Partial<GraphState> = {
    statefulStrategy: {
        'identity': 'STATEFUL_STRATEGY-nPavImsl9vOOjs2BXFAt',
        'statelessStrategy': {
            'identity': 'STATELESS_STRATEGY-fS6EbDxmW4JMRCDhCue8',
            'steps': [
                {
                    'identity': 'WORK-32V4Rxc3xXwGZSIml7r3',
                    'kind': 'work',
                    'execution': {
                        'identity': 'EXECUTION-32V4Rxc3xXwGZSIml7r3',
                        'jobId': 'RESOURCE-ontpa4TFOaRFegKAaXZE',
                        'roleBindings': {
                            'inputBindingMap': {
                                'ROLE-22l2GgttEtvnnQSYdg6h': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                                'ROLE-c6VOzBwzoF6MbeVGL5xE': 'RESOURCE-wE8ERPi8k25bM16KXV9Q'
                            },
                            'outputBindingMap': {
                                'ROLE-IMRry7yMBc1fe0Pur4xl': 'RESOURCE-oGb5nyHFh7bUUb1Afl1B'
                            }
                        }
                    }
                }
            ]
        },
        'strategyState': {
            'EXECUTION-32V4Rxc3xXwGZSIml7r3': {
                'ROLE-22l2GgttEtvnnQSYdg6h': {
                    'identity': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                    'resourceTypeId': 'TYPE-Natural',
                    'creationContext': {
                        'resourceRoleId': 'ROLE-Builder',
                        'executionId': 'EXECUTION-eJ1alasXsIf9wW20Hxxt'
                    },
                    'kind': 'materialized',
                    'path': 'TYPE-Natural/85149ba8dfb3fea64a69448f5dff9e8e0ba215e8a7d95344800a01e9fdfde2e4',
                    'timestamp': '2025-12-14T14:59:38.215Z',
                    'extractedData': {
                        'identity': 1
                    }
                },
                'ROLE-c6VOzBwzoF6MbeVGL5xE': {
                    'identity': 'RESOURCE-wE8ERPi8k25bM16KXV9Q',
                    'resourceTypeId': 'TYPE-Natural',
                    'creationContext': {
                        'resourceRoleId': 'ROLE-Builder',
                        'executionId': 'EXECUTION-eJ1alasXsIf9wW20Hxxt'
                    },
                    'kind': 'missing' as any  // This should trigger the interrupt
                },
                'ROLE-IMRry7yMBc1fe0Pur4xl': {
                    'identity': 'RESOURCE-oGb5nyHFh7bUUb1Afl1B',
                    'resourceTypeId': 'TYPE-Natural',
                    'creationContext': {
                        'resourceRoleId': 'ROLE-IMRry7yMBc1fe0Pur4xl',
                        'executionId': 'EXECUTION-32V4Rxc3xXwGZSIml7r3'
                    },
                    'kind': 'potential-output'
                }
            }
        }
    },
    dryModeManager: {
        dryRunMode: false,
        delay: 0
    },
    messages: []
};

async function testInterrupt() {
    console.log('Testing NodeInterruptCheck...');
    
    const node = new NodeInterruptCheck();
    
    try {
        const result = await node.invoke(mockState as GraphState);
        console.log('Node execution result:', result);
    } catch (error) {
        console.log('Expected interrupt error:', error);
        
        // Check if it's the interrupt we expect
        if (error && typeof error === 'object' && 'message' in error) {
            const message = (error as any).message;
            if (message && message.includes('missing')) {
                console.log('✅ Interrupt triggered correctly for missing role!');
                console.log('Interrupt message:', message);
            } else {
                console.log('❌ Unexpected error:', message);
            }
        } else {
            console.log('❌ Unexpected error type:', error);
        }
    }
}

testInterrupt().catch(console.error);
