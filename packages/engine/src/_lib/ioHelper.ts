import { CONSTANTS } from '@toolproof-npm/shared/constants';

const prefix = `https://storage.googleapis.com/${CONSTANTS.STORAGE.BUCKETS.tp_resources}/`;

export const fetchData = async (path: string) => {
    // Fetch data from the provided path
    const response = await fetch(`${prefix}${path}`);
    if (!response.ok) {
        throw new Error(`Failed to fetch data from ${path}: ${response.statusText}`);
    }
    const data = await response.json();

    return data;

}










