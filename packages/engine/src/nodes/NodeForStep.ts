import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as CONSTANTS_LOCAL from '../constants.js';
import { getNewId } from '@toolproof-npm/shared/server';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_FOR_STEP;


export class NodeForStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        return {
            
        }
    }

}