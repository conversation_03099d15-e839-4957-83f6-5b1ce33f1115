import type { WorkStep<PERSON>son } from '@toolproof-npm/schema';
import { BaseNode, GraphState } from '../types.js';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as CONSTANTS_LOCAL from '../constants.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_BRANCH_STEP;


export class NodeBranchStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        return {
            
        }
    }

}