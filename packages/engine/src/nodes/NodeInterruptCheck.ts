import { interrupt } from '@langchain/langgraph';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import * as CONSTANTS_LOCAL from '../constants.js';

const nodeName = CONSTANTS_LOCAL.NODE_INTERRUPT_CHECK;

/**
 * Node that checks for missing role kinds in strategyState and interrupts for user input
 */
export class NodeInterruptCheck extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        console.log('NodeInterruptCheck: Checking for missing role kinds...');
        
        // Check if we have strategyState
        if (!state.statefulStrategy?.strategyState) {
            console.log('NodeInterruptCheck: No strategyState found, continuing...');
            return {};
        }

        const strategyState = state.statefulStrategy.strategyState;
        const missingRoles: Array<{
            executionId: string;
            roleId: string;
            resourceTypeId?: string;
        }> = [];

        // Iterate through all executions and roles to find missing kinds
        for (const [executionId, roleMap] of Object.entries(strategyState)) {
            for (const [roleId, resource] of Object.entries(roleMap)) {
                // Use type assertion to check for missing kind
                if ((resource as any).kind === 'missing') {
                    missingRoles.push({
                        executionId,
                        roleId,
                        resourceTypeId: resource.resourceTypeId
                    });
                }
            }
        }

        if (missingRoles.length > 0) {
            console.log(`NodeInterruptCheck: Found ${missingRoles.length} missing roles:`, missingRoles);
            
            // Create a user-friendly message for the interrupt
            const missingRolesList = missingRoles.map((role, index) => 
                `${index + 1}. Execution: ${role.executionId}, Role: ${role.roleId}, Type: ${role.resourceTypeId || 'Unknown'}`
            ).join('\n');

            const interruptMessage = `Found missing role kinds that need user input:\n\n${missingRolesList}\n\nPlease select a number (1-${missingRoles.length}) to provide input for:`;

            // Trigger the interrupt and wait for user response
            const userResponse = await interrupt(interruptMessage);
            
            console.log('NodeInterruptCheck: User response received:', userResponse);

            // Parse user response (expecting a number)
            const selectedIndex = parseInt(userResponse as string) - 1;
            
            if (selectedIndex >= 0 && selectedIndex < missingRoles.length) {
                const selectedRole = missingRoles[selectedIndex];
                console.log('NodeInterruptCheck: User selected role:', selectedRole);
                
                // You can add logic here to handle the selected role
                // For now, we'll just log it and continue
                return {
                    // Add any state updates needed based on user selection
                    // For now, just return empty state - could add messages or other state updates
                };
            } else {
                console.log('NodeInterruptCheck: Invalid selection, continuing without changes');
                return {};
            }
        } else {
            console.log('NodeInterruptCheck: No missing roles found, continuing...');
            return {};
        }
    }
}
