import type { <PERSON>S<PERSON><PERSON>son, StrategyState<PERSON>son, JsonValue<PERSON>son, ResourcePotentialInputJson, ResourcePotentialOutputJson, ResourceJson, ResourceIdJson, ResourceRoleIdJson, ResourceSocketJson } from '@toolproof-npm/schema';
import * as CONSTANTS_LOCAL from '../constants.js';
import { fetchData } from '../_lib/ioHelper.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import axios from 'axios';


type ResolveResult =
    | { status: 'materialized'; entry: ResourceJson; path: ResourceSocketJson[] }
    | { status: 'blocked-output'; entry: ResourcePotentialOutputJson; path: ResourceSocketJson[] }
    | { status: 'unresolved'; reason: 'missing-entry' | 'cycle' | 'depth-exceeded'; path: ResourceSocketJson[] };

function resolveResourceChain(strategyState: StrategyStateJson, start: ResourceSocketJson, maxDepth = 50): ResolveResult {
    const visited = new Set<string>();
    const path: ResourceSocketJson[] = [];
    let current: ResourceSocketJson = start;
    for (let depth = 0; depth <= maxDepth; depth++) {
        path.push(current);
        const key = `${current.executionId}::${current.resourceRoleId}`;
        if (visited.has(key)) return { status: 'unresolved', reason: 'cycle', path };
        visited.add(key);
        const bucket = strategyState[current.executionId];
        if (!bucket) return { status: 'unresolved', reason: 'missing-entry', path };
        const entry = bucket[current.resourceRoleId] as (ResourceJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | undefined);
        if (!entry) return { status: 'unresolved', reason: 'missing-entry', path };
        if (entry.kind === 'materialized') return { status: 'materialized', entry: entry as ResourceJson, path };
        if (entry.kind === 'potential-output') return { status: 'blocked-output', entry: entry as ResourcePotentialOutputJson, path };
        if (entry.kind === 'potential-input') {
            const pointer = (entry as ResourcePotentialInputJson).pendingRef;
            if (!pointer) return { status: 'unresolved', reason: 'missing-entry', path };
            current = pointer as ResourceSocketJson;
            continue;
        }
        return { status: 'unresolved', reason: 'missing-entry', path };
    }
    return { status: 'unresolved', reason: 'depth-exceeded', path };
}
// Job output augmented after extractor run
type JobOutput = { path: string; timestamp: string, extractedData?: JsonValueJson };


const nodeName = CONSTANTS_LOCAL.NODE_Work_Step;


export class NodeWorkStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        try {
            const workStep = state.statefulStrategy.statelessStrategy.steps[state.stepCounter] as WorkStepJson; // safe to assert as WorkStepJson because EdgeRouting ensures that only WorkSteps reach here
            const execution = workStep.execution;

            const inputBindingMap = execution.roleBindings.inputBindingMap;
            const outputBindingMap = execution.roleBindings.outputBindingMap;
            const strategyState = state.statefulStrategy.strategyState;

            const job = state.dataLib.jobDataMap.get(execution.jobId);
            if (!job) {
                throw new Error(`Job with ID ${execution.jobId} not found in dataLib.jobs`);
            }

            // console.log('job:', JSON.stringify(job, null, 2));

            const payload: Record<string, ResourceJson | ResourcePotentialOutputJson> = {};

            // Resolve inputs
            for (const inputResourceRoleId of Object.keys(inputBindingMap)) {
                const inputRoleName = job.roles.inputMap[inputResourceRoleId as ResourceRoleIdJson].name;
                const entry = strategyState[execution.id]?.[inputResourceRoleId as ResourceRoleIdJson] as (ResourceJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | undefined);
                if (!entry) throw new Error(`Missing resource map entry for input role '${inputResourceRoleId}' in execution '${execution.id}'`);
                if (entry.kind === 'materialized') {
                    payload[inputRoleName] = entry;
                    continue;
                }
                if (entry.kind === 'potential-input') {
                    const pointer = (entry as ResourcePotentialInputJson).pendingRef;
                    const result = resolveResourceChain(strategyState, pointer as ResourceSocketJson);
                    if (result.status === 'materialized') {
                        payload[inputRoleName] = result.entry;
                        continue;
                    }
                    // Blocked or unresolved: halt execution gracefully
                    throw new Error(`Cannot realize input role '${inputResourceRoleId}': ${result.status === 'blocked-output' ? 'blocked by future output' : 'unresolved chain (' + result.reason + ')'} `);
                }
                if (entry.kind === 'potential-output') {
                    throw new Error(`Input role '${inputResourceRoleId}' unexpectedly bound to potential-output in same execution.`);
                }
                throw new Error(`Unsupported resource kind for input role '${inputResourceRoleId}'`);
            }

            // Pass output potentials unchanged (will be materialized post job execution)
            for (const outputResourceRoleId of Object.keys(outputBindingMap)) {
                const outputRoleName = job.roles.outputMap[outputResourceRoleId as ResourceRoleIdJson].name;
                const pot = strategyState[execution.id]?.[outputResourceRoleId as ResourceRoleIdJson] as (ResourcePotentialOutputJson | undefined);
                if (!pot || pot.kind !== 'potential-output') {
                    throw new Error(`Expected potential-output entry for output role '${outputResourceRoleId}'`);
                }
                payload[outputRoleName] = pot;
            }

            // console.log('payload:', JSON.stringify(payload, null, 2));

            const asyncWrapper = async (url: string): Promise<Record<ResourceRoleIdJson, JobOutput>> => {

                const response = await axios.post(
                    url,
                    payload,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        timeout: 30 * 60 * 1000, // 30 minutes in milliseconds
                    }
                );

                const result = response.data;

                // console.log('result:', JSON.stringify(result, null, 2));

                return result.outputs;
            }

            const roleNameOutputMap = await asyncWrapper(job.uri);

            // Build reverse lookup map from role name to role ID
            const roleNameToIdMap = new Map(
                Object.entries(job.roles.outputMap).map(([id, role]) => [role.name, id])
            );

            const resourceRoleIdOutputMap: Record<ResourceRoleIdJson, JobOutput> = {};

            // Map back from role names to role IDs
            for (const [roleName, output] of Object.entries(roleNameOutputMap)) {
                const id = roleNameToIdMap.get(roleName);
                if (!id) {
                    throw new Error(`Output role name '${roleName}' not found in job roles`);
                }
                resourceRoleIdOutputMap[id as ResourceRoleIdJson] = output;
            }

            // Here, for each output we must invoke the respective ResourceType's extractor job
            await Promise.all(Object.entries(resourceRoleIdOutputMap).map(async ([outputResourceRoleId, output]) => {

                const path = output.path;
                const extractedData = await fetchData(path);

                // console.log('outputRole:', JSON.stringify(outputRole, null, 2));

                // Merge the extracted data with the output
                resourceRoleIdOutputMap[outputResourceRoleId as ResourceRoleIdJson] = {
                    ...(output as any),
                    extractedData: extractedData as JsonValueJson
                };

            }));

            // Now outputs have the extractedData property added

            // Update resourceMap by converting potential-output entries to materialized
            const updatedResourceMap: ResourceMapJson = { ...strategyState };
            const execBucket = { ...updatedResourceMap[execution.id] };
            
            for (const outputResourceRoleId of Object.keys(outputBindingMap)) {
                const pot = strategyState[execution.id]?.[outputResourceRoleId as ResourceRoleIdJson] as (ResourcePotentialOutputJson | undefined);
                if (!pot || pot.kind !== 'potential-output') continue;
                const outputInfo = resourceRoleIdOutputMap[outputResourceRoleId as ResourceRoleIdJson];
                if (!outputInfo) continue;
                
                const extracted = outputInfo.extractedData as any;
                const identityValue: any = (extracted && typeof extracted !== 'object') ? extracted : (extracted?.identity ?? 0);
                
                execBucket[outputResourceRoleId as ResourceRoleIdJson] = {
                    id: pot.id as ResourceIdJson,
                    resourceTypeId: pot.resourceTypeId,
                    creationContext: pot.creationContext,
                    kind: 'materialized',
                    path: outputInfo.path,
                    timestamp: outputInfo.timestamp,
                    extractedData: {
                        identity: identityValue,
                        ...((typeof extracted === 'object' && extracted !== null) ? extracted : {})
                    }
                };
            }
            updatedResourceMap[execution.id] = execBucket;

            return {
                messages: [new AIMessage(`${nodeName} completed`)],
                statefulStrategy: {
                    ...state.statefulStrategy,
                    resourceMaps: [
                        updatedResourceMap,
                        ...state.statefulStrategy.resourceMaps.slice(1)
                    ]
                },
                stepCounter: state.stepCounter + 1
            };

        } catch (error: any) {
            // Structured logging to help diagnose HTTP / Axios errors (EROLE_BAD_REQUEST, etc.)
            try {
                // Use Axios config data (if present) rather than referencing local variables that
                // may be out of scope when the catch runs.
                const payloadPreview = error?.config?.data ?? null;
                console.error(`Error in ${nodeName}:`, {
                    message: error?.message,
                    code: error?.code,
                    status: error?.response?.status,
                    responseData: error?.response?.data,
                    requestUrl: error?.config?.url,
                    payload: payloadPreview,
                });
            } catch (logErr) {
                // Fallback if structured logging throws
                console.error(`Error in ${nodeName} (logging failed):`, error);
            }

            return {
                messages: [new AIMessage(`${nodeName} failed`)],
                statefulStrategy: {
                    ...state.statefulStrategy,
                },
                stepCounter: state.stepCounter + 1
            };
        }
    }
}



