import * as CONSTANTS_LOCAL from '../constants.js';
import { listResources } from '@toolproof-npm/shared/server';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_LOAD;


export class NodeLoad extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        if (state.stepCounter === 0) {
            try {
                console.log("GOOGLE_APPLICATION_CREDENTIALS_JSON : ", process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
                const asyncWrapper = async () => {

                    const resourceMap = await listResources(
                        [
                            'TYPE-Job',
                            'TYPE-Natural',
                        ]
                    );

                    return resourceMap;
                };

                const resourceMap = await asyncWrapper();

                return {
                    messages: [new AIMessage(`${nodeName} completed successfully`)],
                    resourceMap
                };
            } catch (error: any) {
                throw new Error(`Error in ${nodeName}: ${error.message}`);
                /* console.error(`Error in ${nodeName}:`, error);
                return {
                    messages: [new AIMessage(`${nodeName} failed`)],
                    dataLib: new Map()
                }; */
            }
        } else {
            throw new Error(`${nodeName} can only be run at the start of the workflow`);
        }
    }

}



