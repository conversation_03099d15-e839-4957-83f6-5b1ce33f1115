import type { StatefulStrategyJson } from '@toolproof-npm/schema';
import type { ResourceMap } from '@toolproof-npm/shared/types';
import type { DryRunManagerType } from './types.js';
import { Annotation, MessagesAnnotation } from '@langchain/langgraph';


export const GraphStateAnnotationRoot = Annotation.Root({
    ...MessagesAnnotation.spec,
    dryModeManager: Annotation<DryRunManagerType>(
        {
            reducer: (prev, next) => next,
            default: () => ({
                dryRunMode: false,
                delay: 0,
                drySocketMode: false, // ATTENTION: why no error here?
            }),
        }
    ),
    statefulStrategy: Annotation<StatefulStrategyJson>(),
    resourceMap: Annotation<ResourceMap>(),
    stepCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
    iterationCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
});