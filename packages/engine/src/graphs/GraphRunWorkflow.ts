import * as CONSTANTS_LOCAL from '../constants.js';
import { GraphStateAnnotationRoot } from '../graphState.js';
// import { NodeLoad } from '../nodes/NodeLoad.js';
// import { NodeWorkStep } from '../nodes/NodeWorkStep.js';
// import { NodeBranchStep } from 'src/nodes/NodeBranchStep.js';
// import { NodeWhileStep } from '../nodes/NodeWhileStep.js';
// import { NodeForStep } from '../nodes/NodeForStep.js';
import { NodeInterruptCheck } from '../nodes/NodeInterruptCheck.js';
import { StateGraph, START, END, MemorySaver } from '@langchain/langgraph';


// Commented out old EdgeRouting function for interrupt implementation
/*
const EdgeRouting = (state: GraphState) => {

    // console.log('EgeRouting', JSON.stringify([...state.dataLib.jobs], null, 2));

    // return END;

    if (state.stepCounter >= state.statefulStrategy.workflow.steps.length) {
        return END;
    }

    const step = state.statefulStrategy.workflow.steps[state.stepCounter];

    if (step.kind === CONSTANTS.STEP.work) {
        return CONSTANTS_LOCAL.NODE_Work_Step;
    } else if (step.kind === CONSTANTS.STEP.branch) {
        return CONSTANTS_LOCAL.NODE_BRANCH_STEP;
    }
    else if (step.kind === CONSTANTS.STEP.while) {
        return CONSTANTS_LOCAL.NODE_WHILE_STEP;
    } else if (step.kind === CONSTANTS.STEP.for) {
        return CONSTANTS_LOCAL.NODE_FOR_STEP;
    }

    return END;

};
*/


// Commented out old graph structure for interrupt implementation
/*
const stateGraph = new StateGraph(GraphStateAnnotationRoot)
    .addNode(
        CONSTANTS_LOCAL.NODE_LOAD,
        new NodeLoad()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_Work_Step,
        new NodeWorkStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_BRANCH_STEP,
        new NodeBranchStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_WHILE_STEP,
        new NodeWhileStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_FOR_STEP,
        new NodeForStep()
    )
    .addEdge(START, CONSTANTS_LOCAL.NODE_LOAD)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_LOAD, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_Work_Step, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_BRANCH_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_WHILE_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_FOR_STEP, EdgeRouting)
*/

// New simplified graph with interrupt functionality
const stateGraph = new StateGraph(GraphStateAnnotationRoot)
    .addNode(
        CONSTANTS_LOCAL.NODE_INTERRUPT_CHECK,
        new NodeInterruptCheck()
    )
    .addEdge(START, CONSTANTS_LOCAL.NODE_INTERRUPT_CHECK)
    .addEdge(CONSTANTS_LOCAL.NODE_INTERRUPT_CHECK, END)


// Compile with checkpointer to enable interrupts
const checkpointer = new MemorySaver();
export const graph = stateGraph.compile({ checkpointer });



