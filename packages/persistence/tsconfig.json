{
  "compilerOptions": {
    "target": "ES2020",
    "module": "nodenext",
    "baseUrl": ".",
    "paths": {
      "shared/*": [
        "../shared/*"
      ]
    },
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitAny": true,
    "skipLibCheck": true,
    "outDir": "dist",
    "rootDir": "src",
    "declaration": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
  ]
}