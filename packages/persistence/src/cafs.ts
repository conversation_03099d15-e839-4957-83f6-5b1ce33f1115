import { createHash } from 'crypto';
import { StorageHelper } from './storageHelper.js';
import {
    PersistenceWriteResult,
    PersistenceConfig
} from './_lib/types.js';
import type { PartialResourceMeta } from '@toolproof-npm/shared/types'

/**
 * Content Addressable File Storage (CAFS)
 */
export class CAFS {
    private storageHelper: StorageHelper;
    private config: PersistenceConfig;

    constructor(config: Partial<PersistenceConfig> = {}) {
        this.config = {
            bucketName: config.bucketName || process.env.BUCKET_NAME || 'tp-resources',
            maxFileSize: config.maxFileSize || 10 * 1024 * 1024, // 10MB default
            defaultContentType: config.defaultContentType || 'application/json'
        };

        this.storageHelper = new StorageHelper(this.config.bucketName);
    }

    async storeContent(
        partialMeta: PartialResourceMeta,
        content: string
    ): Promise<PersistenceWriteResult> {
        try {
            // Validate content size
            const contentSize = Buffer.byteLength(content, 'utf8');
            if (contentSize > this.config.maxFileSize) {
                return {
                    success: false,
                    path: '',
                    error: `Content size ${contentSize} exceeds maximum allowed size ${this.config.maxFileSize}`
                };
            }

            // Generate content hash
            const contentHash = this.generateContentHash(content);
            const path = this.getStoragePath(partialMeta.resourceTypeId, contentHash);

            const timestamp = new Date().toISOString();

            const fileExists = await this.storageHelper.checkExistence(path);

            if (!fileExists.fileExists) {

                // Store content in GCS
                await this.storageHelper.writeRawContent(content,
                    {
                        ...partialMeta,
                        kind: 'materialized',
                        path,
                        timestamp
                    }
                );
            }

            await this.storageHelper.writeToFirestore(
                {
                    ...partialMeta,
                    kind: 'materialized',
                    path,
                    timestamp
                }
            );

            return {
                success: true,
                path: path,
            };

        } catch (error) {
            return {
                success: false,
                path: '',
                error: `Failed to store content: ${error}`
            };
        }
    }

    private getStoragePath(resourceTypeId: string, contentHash: string): string {
        return `${resourceTypeId}/${contentHash}`;
    }

    async retrieveContent(path: string): Promise<string> {
        try {

            // Check if content exists
            const { fileExists: exists } = await this.storageHelper.checkExistence(path);
            if (!exists) {
                throw new Error(`Content with path ${path} not found`);
            }

            // Retrieve content
            const content = await this.storageHelper.readRawContent(path);

            return content;

        } catch (error) {
            throw new Error(`Failed to retrieve content: ${error}`);
        }
    }

    /**
     * Generates SHA-256 hash of content
     * @param content The content to hash
     * @returns The SHA-256 hash as hex string
     */
    private generateContentHash(content: string): string {
        return createHash('sha256').update(content).digest('hex');
    }


}
