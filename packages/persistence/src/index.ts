/**
 * Express Server with CAFS APIs
 *
 * This server provides:
 * - REST API endpoints for Content Addressable File Storage (CAFS)
 * - Store content with deduplication
 * - Retrieve content by hash
 */

import type { PersistenceWriteResult } from './_lib/types.js';
import type { PartialResourceMeta } from '@toolproof-npm/shared/types'
import { CAFS } from './cafs.js';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';


// Load environment variables
dotenv.config();

// Create Express app
const app: express.Application = express();
const port = Number(process.env.PORT) || 3000;

// Initialize CAFS instance
const cafs = new CAFS({
    bucketName: process.env.BUCKET_NAME || 'tp-resources',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    defaultContentType: 'application/json'
});

// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-device-agent']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req: express.Request, res: express.Response) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'CAFS API Server'
    });
});

// Store content endpoint
app.post('/api/cafs/store', async (req: express.Request, res: express.Response) => {
    try {
        const { meta, content } = req.body as { meta: PartialResourceMeta; content: string };

        // Validate required fields
        if (!meta || !content) {
            return res.status(400).json({
                error: 'Missing required fields: meta and content'
            });
        }

        if (!meta.identity || !meta.resourceTypeId || !meta.creationContext.resourceRoleId || !meta.creationContext.executionId) {
            return res.status(400).json({
                error: 'Missing required meta fields: identity, resourceTypeId, resourceRoleId, executionId'
            });
        }

        // Validate content is string
        if (typeof content !== 'string') {
            return res.status(400).json({
                error: 'Content must be a string'
            });
        }

        // Store content using CAFS
        const result: PersistenceWriteResult = await cafs.storeContent(meta, content);

        if (result.success) {
            res.status(201).json({
                success: true,
                path: result.path,
                message: 'Content stored successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error || 'Failed to store content'
            });
        }

    } catch (error) {
        console.error('Error storing content:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error while storing content'
        });
    }
});

// Retrieve content endpoint - use middleware to handle any path
app.use('/api/cafs/retrieve', async (req: express.Request, res: express.Response) => {
    try {
        // Extract path after /api/cafs/retrieve/
        const fullPath = req.path; // e.g., /TYPE-Natural/abc123
        const path = fullPath.startsWith('/') ? fullPath.substring(1) : fullPath;

        if (!path) {
            return res.status(400).json({
                error: 'Path is required'
            });
        }

        // Retrieve content using CAFS
        const content = await cafs.retrieveContent(path);

        res.json({
            success: true,
            path,
            content,
            retrievedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error retrieving content:', error);

        if (error instanceof Error && error.message.includes('not found')) {
            const fullPath = req.path;
            const path = fullPath.startsWith('/') ? fullPath.substring(1) : fullPath;
            res.status(404).json({
                success: false,
                error: `Content at path ${path} not found`
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Internal server error while retrieving content'
            });
        }
    }
});


// Global error handling
process.on('unhandledRejection', (reason: any) => {
    process.stderr.write('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (err: any) => {
    process.stderr.write('❌ Uncaught Exception:', err);
    process.exit(1);
});

process.on('SIGINT', () => {
    console.log('Shutting down server gracefully...');
    process.exit(0);
});


// Start server (with HMR guard to prevent port collisions)
if (!(globalThis as any).__cafsServerStarted) {
    (globalThis as any).__cafsServerStarted = true;
    app.listen(port, '0.0.0.0', () => {
        console.log(`🚀 CAFS API Server running on port ${port}`);
        console.log(`📊 Health check: http://localhost:${port}/health`);
        console.log(`📝 Store content: POST http://localhost:${port}/api/cafs/store`);
        console.log(`📖 Retrieve content: GET http://localhost:${port}/api/cafs/retrieve/:contentHash`);
        console.log(`🔍 Check existence: GET http://localhost:${port}/api/cafs/exists/:contentHash`);
    }).on('error', (err: any) => {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    });
}

// Export for testing
export default app;