
/**
 * Configuration for the GCS Utils SDK
 */
export interface PersistenceConfig {
    /** Google Cloud Storage bucket name */
    bucketName: string;
    /** Maximum file size allowed (in bytes) */
    maxFileSize: number;
    /** Default content type for files */
    defaultContentType: string;
}

/**
 * Result of a CAFS operation
 */
export interface PersistenceWriteResult {
    success: boolean;
    path: string;
    error?: string;
}
