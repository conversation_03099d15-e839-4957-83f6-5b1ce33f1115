{"name": "@toolproof/persistence", "version": "1.0.0", "description": "TypeScript SDK for Google Cloud Storage with Content Addressable File Storage (CAFS) support", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b", "dev": "tsc --watch", "start": "pnpm run build && node dist/index.js", "start:dev": "tsc && node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t cafs-api .", "docker:run": "docker run -p 3000:3000 cafs-api"}, "keywords": ["google-cloud-storage", "gcs", "content-addressable", "cafs", "typescript", "sdk"], "author": "Your Name", "license": "MIT", "dependencies": {"@google-cloud/firestore": "^7.1.0", "@google-cloud/storage": "^7.7.0", "cors": "^2.8.5", "dotenv": "^17.2.3", "express": "^5.1.0", "firebase-admin": "^13.5.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "uuid": "^9.0.1", "@toolproof-npm/schema": "^0.1.38", "@toolproof-npm/shared": "^0.1.45"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.5", "@types/morgan": "^1.9.10", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "rimraf": "^5.0.5", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b"}