import type { ResourceId<PERSON><PERSON>, ExecutionId<PERSON><PERSON>, ResourceRoleIdJson, ResourceRoleValue<PERSON><PERSON>, ResourceTypeIdJson, ResourceJson } from '@toolproof-npm/schema';
import { CONSTANTS } from './constants.js';

export type BucketConst = typeof CONSTANTS.STORAGE.BUCKETS.tp_shapes | typeof CONSTANTS.STORAGE.BUCKETS.tp_resources;

export type CollectionConst = typeof CONSTANTS.STORAGE.COLLECTIONS.shapes | typeof CONSTANTS.STORAGE.COLLECTIONS.resources;

export type FilterConst = typeof CONSTANTS.STORAGE.FILTER.members | typeof CONSTANTS.STORAGE.FILTER.specials;

export type ResourceShapeConst = typeof CONSTANTS.SHAPES.formats | typeof CONSTANTS.SHAPES.types;

export type ResourceRoleConst = typeof CONSTANTS.ROLES.roles;

export type ResourceConst = typeof CONSTANTS.RESOURCES.resources;

export type StepConst = typeof CONSTANTS.STEP.work | typeof CONSTANTS.STEP.branch | typeof CONSTANTS.STEP.while | typeof CONSTANTS.STEP.for;

export type WorkflowConst = typeof CONSTANTS.WORKFLOW.workflow | typeof CONSTANTS.WORKFLOW.workflowSpec | typeof CONSTANTS.WORKFLOW.execution;

export type Role = { id: ResourceRoleIdJson } & ResourceRoleValueJson;

export type ResourceMap = Record<ResourceTypeIdJson, ResourceJson[]>;

export type PartialResourceMeta = {
	identity: ResourceIdJson;
	resourceTypeId: ResourceTypeIdJson;
	creationContext: {
		resourceRoleId: ResourceRoleIdJson;
		executionId: ExecutionIdJson;
	};
};