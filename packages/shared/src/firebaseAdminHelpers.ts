import type { ResourceTypeId<PERSON><PERSON>, ResourceJson } from '@toolproof-npm/schema';
import type { ResourceShapeConst, ResourceRoleConst, StepConst, WorkflowConst, ResourceMap, ResourceConst } from './types.d.ts';
import { CONSTANTS } from './constants.js';
import { dbAdmin, storageAdmin } from "./firebaseAdminInit.js";


export function getNewId(identifiable: ResourceShapeConst | ResourceRoleConst | ResourceConst | 'jobs' | 'executions' | StepConst | 'STATELESS_STRATEGY' | 'STATEFUL_STRATEGY') { // ATTENTION
    const base = identifiable.toUpperCase();
    const normalized = base.endsWith('S') ? base.slice(0, -1) : base;
    const prefix = normalized + '-';
    const docRef = dbAdmin.collection(CONSTANTS.STORAGE.COLLECTIONS.shapes).doc(identifiable).collection(CONSTANTS.STORAGE.FILTER.members).doc();
    return prefix + docRef.id;
}

export async function listResources( // ATTENTION: must clean up
    resourceTypeIds: ResourceTypeIdJson[]
): Promise<ResourceMap> {
    const bucket = storageAdmin.bucket(CONSTANTS.STORAGE.BUCKETS.tp_resources);

    async function fetchFilesUnder(resourceTypeId: string): Promise<Array<{ data: unknown; meta: any; name: string }>> {
        const prefix = `${resourceTypeId}/`;
        const [found] = await bucket.getFiles({ prefix });
        const files = found.filter(f => {
            const name = f.name || '';
            if (!name || name.endsWith('/')) return false;
            return true;
        });
        if (!files.length) return [];
        const items = await Promise.all(files.map(async (file) => {
            try {
                const [buf] = await file.download();
                const meta = file.metadata || (await file.getMetadata())[0];
                const data = JSON.parse(buf.toString('utf8')) as unknown;
                return { data, meta, name: file.name };
            } catch {
                return null as unknown as { data: unknown; meta: any; name: string };
            }
        }));
        return items.filter(Boolean) as Array<{ data: unknown; meta: any; name: string }>;
    }

    const entries = await Promise.all(
        resourceTypeIds.map(async (resourceTypeId) => {
            const rows = await fetchFilesUnder(resourceTypeId as unknown as string);
            const items: ResourceJson[] = rows.map(({ data, meta, name }) => {
                const flat = meta?.metadata ?? {};
                // Reconstruct nested object from flattened keys (dot and array index notation)
                const root: any = {};
                for (const [k, vRaw] of Object.entries(flat)) {
                    if (typeof vRaw !== 'string') continue; // GCS should store only strings
                    const vStr = vRaw.trim();
                    // Attempt JSON parse for non-simple primitives
                    let value: any = vStr;
                    if ((vStr.startsWith('{') && vStr.endsWith('}')) || (vStr.startsWith('[') && vStr.endsWith(']'))) {
                        try { value = JSON.parse(vStr); } catch { value = vStr; }
                    }
                    // Split by '.' while preserving array indices
                    const segments = k.split('.');
                    let cursor = root;
                    for (let i = 0; i < segments.length; i++) {
                        const seg = segments[i];
                        const arrIdxMatch = seg.match(/^(.*)\[(\d+)\]$/);
                        if (arrIdxMatch) {
                            const base = arrIdxMatch[1];
                            const idx = parseInt(arrIdxMatch[2], 10);
                            if (!cursor[base]) cursor[base] = [];
                            if (!Array.isArray(cursor[base])) cursor[base] = [];
                            while (cursor[base].length <= idx) cursor[base].push(undefined);
                            if (i === segments.length - 1) {
                                cursor[base][idx] = value;
                            } else {
                                if (!cursor[base][idx]) cursor[base][idx] = {};
                                cursor = cursor[base][idx];
                            }
                        } else {
                            if (i === segments.length - 1) {
                                cursor[seg] = value;
                            } else {
                                if (!cursor[seg] || typeof cursor[seg] !== 'object') cursor[seg] = {};
                                cursor = cursor[seg];
                            }
                        }
                    }
                }
                const identity = root.identity as string | undefined;
                const resourceTypeIdMeta = root.resourceTypeId as string | undefined;
                // creationContext may be flattened as creationContext.resourceRoleId or direct resourceRoleId
                const resourceRoleId = (root.creationContext?.resourceRoleId ?? root.resourceRoleId) as string | undefined;
                const executionId = (root.creationContext?.executionId ?? root.executionId) as string | undefined;
                const kind = root.kind as string | undefined;
                const path = root.path as string | undefined;
                const timestamp = root.timestamp as string | undefined;

                const missing = [
                    ['identity', identity],
                    ['resourceTypeId', resourceTypeIdMeta],
                    ['resourceRoleId', resourceRoleId],
                    ['executionId', executionId],
                    ['kind', kind],
                    ['timestamp', timestamp],
                    ['path', path],
                ].filter(([_, v]) => typeof v !== 'string' || (v as string).length === 0) as Array<[string, string | undefined]>;

                if (missing.length) {
                    const keys = missing.map(([k]) => k).join(', ');
                    throw new Error(`Missing required metadata keys [${keys}] for resource file: ${name}`);
                }

                return {
                    identity,
                    resourceTypeId,
                    creationContext: {
                        resourceRoleId,
                        executionId,
                    },
                    kind: kind as string,
                    path: path as string,
                    timestamp: timestamp as string,
                    extractedData: data as any,
                } as unknown as ResourceJson;
            });
            return [resourceTypeId, items] as const;
        })
    );
    return Object.fromEntries(entries) as ResourceMap;
}


