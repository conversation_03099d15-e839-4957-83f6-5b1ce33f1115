# @toolproof-npm/shared

Core library utilities for ToolProof.

## Installation

```bash
pnpm add @toolproof-npm/shared
# or
npm install @toolproof-npm/shared
# or
yarn add @toolproof-npm/shared
```

## Usage

### Basic Import

```typescript
import { /* your exports */ } from '@toolproof-npm/shared';
```

### Constants

```typescript
import { /* constants */ } from '@toolproof-npm/shared/constants';
```

### Types

```typescript
import { /* types */ } from '@toolproof-npm/shared/types';
```

### Server Utilities (Node.js only)

```typescript
import { /* server utilities */ } from '@toolproof-npm/shared/server';
```

## Features

- TypeScript support with full type definitions
- ESM module format
- Tree-shakeable exports
- Server-side utilities for Firestore admin operations

## Requirements

- Node.js 16+ 
- TypeScript 4.5+ (for TypeScript projects)

## License

MIT

