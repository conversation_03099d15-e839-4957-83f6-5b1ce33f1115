import type { ResourceType<PERSON>son } from "@toolproof-npm/schema";
import type { UIContext } from "./types/types.js";
import { getGenesisRef, getGenesisSubschema, getAjvGenesis } from "./compilers.js";
import { getResourceTypeMap } from "./utils.js";
import type { ErrorObject } from 'ajv';


export function start() {
    const resourceTypeMap = getResourceTypeMap();

    // Add a test case with invalid data to verify validation is working
    /* resourceTypeMap.set("InvalidTest", {
        identity: "INVALID-NotATypeId",  // Should fail pattern match
        name: "Test",
        description: "Test",
        resourceFormatId: "FORMAT-Test"
        // Missing extractionSchema - should fail required check
    } as any); */

    const validationResults = new Map<string, { isValid: boolean; errors: ErrorObject[] | null }>();

    for (const [key, value] of resourceTypeMap.entries()) {
        // Validate each ResourceType definition against the ResourceType schema
        const result = validateGenesis("ResourceType", key, value);
        validationResults.set(key, { isValid: result.isValid, errors: result.errors });
    }


    // Convert Maps to objects for display
    const resultsObj = Object.fromEntries(
        Array.from(validationResults.entries()).map(([k, v]) => [k, v.isValid])
    );

    // console.log("Validation Results validateSchema:", JSON.stringify({ "Name": validationResults.get("Name") }, null, 2));
}


export function validateGenesis(defsPointer: string, key: string, data: any): { isValid: boolean; errors: ErrorObject[] | null } {
    const uiContext: UIContext = { uiHints: {} };
    try {
        // Use $ref to the schema that's already loaded in AJV
        // This way all anchor references are resolved within the Genesis schema context
        const ref = getGenesisRef(defsPointer);
        const subschema = getGenesisSubschema(defsPointer, false);
        // Logging just once since subschema is the same for each call
        if (key === "Name") {
            // console.log("ref:", ref);
            // console.log('subschema:', JSON.stringify(subschema, null, 2));
        }
        const ajv = getAjvGenesis();

        const validate = ajv.compile({ $ref: ref });
        const ok = validate.call(
            uiContext,
            data
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}