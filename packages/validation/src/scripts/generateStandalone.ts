import { GenesisSchema } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getGenesisRef, getGenesisValidator } from '../compilers.js';
// NodeNext/ESM requires explicit extension for subpath imports
import standaloneCode from 'ajv/dist/standalone/index.js';
import fs from 'fs';
import path from 'path';

const subSchema = CONSTANTS.SCHEMA.ResourceTypeData;

export function generateStandalone() {
    const ref = getGenesisRef(subSchema);
    console.log(`ref: ${ref}`);
    const schema = { $ref: ref };
    const { validate, ajv } = getGenesisValidator(schema, `__ref:${ref}`);
    if (!validate) {
        throw new Error(`Schema not found in Ajv: ${GenesisSchema.$id}`);
    }
    // IMPOTYPEANT: pass the original compiled function; binding removes the `.source` Ajv attaches
    const moduleCode = standaloneCode(ajv, validate);

    console.log('Generated standalone validator code:');
    // console.log(moduleCode);

    // Ensure target directory exists: write into src/generated
    const outDir = path.join(process.cwd(), 'src', 'generated');
    const outFile = path.join(outDir, `validate${subSchema}.js`);
    fs.mkdirSync(outDir, { recursive: true });
    fs.writeFileSync(outFile, moduleCode, 'utf8');

}

generateStandalone();