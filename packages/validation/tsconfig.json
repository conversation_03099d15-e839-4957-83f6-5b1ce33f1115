{
  "compilerOptions": {
    "target": "es2020",
    "module": "nodenext",
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "emitDeclarationOnly": false,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    // "noEmit": true,
    // "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "lib": [
      "es2022"
    ],
    "types": [
      "node"
    ]
  },
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules",
    "stashed"
  ]
}