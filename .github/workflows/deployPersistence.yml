name: Deploy persistence to <PERSON><PERSON>

on:
  push:
    branches:
      - master
    paths:
      - "packages/persistence/**"

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}
  GKE_ZONE: ${{ secrets.GKE_ZONE }}
  REGION: ${{ secrets.REGION }}
  REPOSITORY: ${{ secrets.REPOSITORY }}
  IMAGE: ${{ secrets.IMAGE_NAME }}
  DEPLOYMENT_NAME: persistence-deployment
  CONTAINER_NAME: image-persistence-1

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up gcloud
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Configure Docker authentication
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

      - name: Build Docker image
        run: |
          docker build \
            -f packages/persistence/Dockerfile \
            -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE }}:latest \
            packages/persistence
      
      - name: Push Docker image
        run: |
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE }}:latest

      - name: Install gcloud CLI and GKE auth plugin
        run: |
          echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
          curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg
          sudo apt-get update
          sudo apt-get install -y google-cloud-cli google-cloud-cli-gke-gcloud-auth-plugin

      - name: Get GKE credentials
        run: |
          export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

      - name: Deploy to GKE
        run: |
          kubectl rollout restart deployment/${{ env.DEPLOYMENT_NAME }}
          kubectl rollout status deployment/${{ env.DEPLOYMENT_NAME }}
